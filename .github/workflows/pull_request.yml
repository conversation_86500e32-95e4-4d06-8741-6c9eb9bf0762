name: Pull Request

on:
  pull_request:

jobs:
  fixup:
    name: Auto squash check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Block fixup commits
        shell: bash
        run: |
          git fetch --all
          fixup_commits=$(git log --format="%H" origin/${{ github.base_ref }}..origin/${{ github.head_ref }} | xargs -I {} git log -n1 --format="%s" {} | grep -c "^fixup!" || true)
          if [ "$fixup_commits" -gt 0 ]; then
            echo "Found $fixup_commits fixup commit(s) in this pull request."
            echo "Please squash these commits before merging."
            exit 1
          else
            echo "No fixup commits found. Good to go!"
          fi
  commit_lint:
    name: Commit message lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: wagoid/commitlint-github-action@v6
  setup-tests:
    name: Setup Tests
    needs: [fixup, commit_lint]
    runs-on: ubuntu-latest
    outputs:
      jobs: ${{ steps.split-tests.outputs.jobs }}
      has-uapi-changes: ${{ steps.changes.outputs.has_uapi_spec_changes }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Build Flux Base
        uses: ./.github/actions/build-base
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          s3-bucket-amd64: ${{ vars.S3_CACHE_AMD_BUCKET }}
      - name: Split Tests
        uses: ./.github/actions/split-tests
        id: split-tests
        with:
          config: |
            [
              {
                "type": "split",
                "path": "test/unit/domains/",
                "ignore": ["test/unit/domains/event"],
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/adapters/",
                "ignore": ["test/unit/adapters/uapi"],
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/adapters/uapi/",
                "ignore": ["test/unit/adapters/uapi/events_domain_endpoints"],
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/domains/event",
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/flux_services/",
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/adapters/uapi/events_domain_endpoints",
                "chunks": 2
              },
              {
                "type": "fixed",
                "path": "test/unit/app/ test/unit/utils/"
              },
              {
                "type": "fixed",
                "path": "test/unit/services/"
              }
            ]

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v45

      - name: UAPI Changes
        id: changes
        run: |
          # Check if changes include uapi-spec directory
          if echo "${{ steps.changed-files.outputs.all_changed_files }}" | tr ' ' '\n' | grep -q "^uapi-spec/"; then
            echo "has_uapi_spec_changes=true" >> "$GITHUB_OUTPUT"
          else
            echo "has_uapi_spec_changes=false" >> "$GITHUB_OUTPUT"
          fi
      
  checks:
    name: Checks
    needs: [setup-tests]
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Run checks
      uses: ./.github/actions/check
      with:
        target-branch: ${{ github.event.pull_request.base.ref }}
  tests:
    name: Tests
    needs: [checks, setup-tests]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        unit_tests_to_run: ${{ fromJson(needs.setup-tests.outputs.jobs) }}
    services:
      registry:
        image: registry:2
        ports:
          - 5000:5000
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}
    - name: Run tests
      uses: ./.github/actions/test
      with:
        deepsource-dsn: ${{ secrets.DEEPSOURCE_DSN }}
        unit-tests-to-run: ${{ matrix.unit_tests_to_run }}
  test-result:
    if: ${{ always() }}
    runs-on: ubuntu-latest
    name: Test Result
    needs: [tests]
    steps:
      - shell: bash
        run: |
          result="${{ needs.tests.result }}"
          if [[ $result == "success" || $result == "skipped" ]]; then
            exit 0
          else
            exit 1
          fi

  build-uapi-spec:
    name: Build UAPI Spec
    if: ${{ needs.setup-tests.outputs.has-uapi-changes == 'true' }}
    runs-on: ubuntu-latest
    needs: [test-result, setup-tests]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to AWS
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Build Private Spec
        uses: ./.github/actions/build-uapi
        with:
          spec: private
          s3_prefix: previews/private/${{ github.event.pull_request.number }}
          s3_bucket: ${{ vars.UAPI_PRIVATE_DOC_S3_BUCKET }}

      - name: Build Public Spec
        uses: ./.github/actions/build-uapi
        with:
          spec: public
          s3_prefix: previews/${{ github.event.pull_request.number }}
          s3_bucket: ${{ vars.UAPI_PRIVATE_DOC_S3_BUCKET }}
