name: Deploy Cron Server

on:
  workflow_dispatch:

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: cron
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    - name: Get current date
      id: current-date
      run: echo "DATE=$(TZ=:America/Toronto date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    - name: Get required variables from aws ssm
      id: ssm
      run: |
        PRODUCTION_FLUX_DOMAIN=$(aws ssm get-parameter --name /production/flux/domain --query Parameter.Value)
        PRODUCTION_LIVEDISPLAY_DOMAIN=$(aws ssm get-parameter --name /production/livedisplay/domain --query Parameter.Value)
        STAGE_FLUX_DOMAIN=$(aws ssm get-parameter --name /stage/flux/domain --query Parameter.Value)
        STAGE_LIVEDISPLAY_DOMAIN=$(aws ssm get-parameter --name /stage/livedisplay/domain --query Parameter.Value)
        AWS_FARGATE_TASK_IAM_ROLE_ARN=$(aws ssm get-parameter --name /production/terraform/iam/ecs/task_role_arn --query Parameter.Value)
        AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=$(aws ssm get-parameter --name /production/terraform/iam/ecs/task_execution_role_arn --query Parameter.Value)
        echo "PRODUCTION_FLUX_DOMAIN=${PRODUCTION_FLUX_DOMAIN:1:-1}" >> $GITHUB_OUTPUT
        echo "PRODUCTION_LIVEDISPLAY_DOMAIN=${PRODUCTION_LIVEDISPLAY_DOMAIN:1:-1}" >> $GITHUB_OUTPUT
        echo "STAGE_FLUX_DOMAIN=${STAGE_FLUX_DOMAIN:1:-1}" >> $GITHUB_OUTPUT
        echo "STAGE_LIVEDISPLAY_DOMAIN=${STAGE_LIVEDISPLAY_DOMAIN:1:-1}" >> $GITHUB_OUTPUT
        echo "AWS_FARGATE_TASK_IAM_ROLE_ARN=${AWS_FARGATE_TASK_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
        echo "AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=${AWS_FARGATE_EXECUTION_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
    - name: Create cron jobs
      run: |
        printf "##Stage crons\n" >> cronjobs
        printf "#LiveDisplay - social\n*/2 * * * * curl -s https://${{ steps.ssm.outputs.STAGE_LIVEDISPLAY_DOMAIN }}/api/v1/update/twitter/${{ secrets.LD_TWITTER_WEBHOOK }}\n" >> cronjobs
        printf "#LiveDisplay - news\n*/5 * * * * curl -s https://${{ steps.ssm.outputs.STAGE_LIVEDISPLAY_DOMAIN }}/api/v1/update/news/${{ secrets.LD_NEWS_WEBHOOK }}\n" >> cronjobs
        printf "#LiveDisplay - agenda\n*/2 * * * * curl -s https://${{ steps.ssm.outputs.STAGE_LIVEDISPLAY_DOMAIN }}/api/v1/update/event/agenda/${{ secrets.LD_AGENDA_WEBHOOK }}\n" >> cronjobs
        printf "#LiveDisplay - leaderboard\n* * * * * curl -s https://${{ steps.ssm.outputs.STAGE_LIVEDISPLAY_DOMAIN }}/api/v1/update/event/leaderboard/${{ secrets.LD_LEADERBOARD_WEBHOOK }}\n" >> cronjobs
        printf "#Cleanup pending and cancelled images\n0 10 * * * curl -s https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/uapi/cron/clean-images -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.3\"\n" >> cronjobs
        printf "#Notify upcoming sessions\n*/2 * * * * curl -s -X POST https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/uapi/sessions/schedule-upcoming-notifications/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#User email sender\n*/1 * * * * curl -s -X POST https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/uapi/user-emails/send-scheduled-user-emails/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Delete Non-Production SSO user pools\n0 8 * * 0 curl -s -X DELETE https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/uapi/organizations/sso/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Process org custom domains\n5 * * * * curl -s -X POST https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/uapi/organizations/process-custom-domains/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "#Send Offline Notifications\n*/5 * * * * curl -s -X POST https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/v1/action/send-offline-notifications -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Content-Type: application/json\" -d '{\"time_offset\": 3}'" >> cronjobs
        printf "#Expire reg orders\n*/1 * * * * curl -s -X POST https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/uapi/registration/orders/expire-orders/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.9\"\n" >> cronjobs
        printf "#Process pending payment transactions\n*/5 * * * * curl -s -X POST https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/uapi/payments/process-pending-transactions/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.9\"\n" >> cronjobs
        printf "#Run Sender Email Domain Periodic Verification \n*/5 * * * * curl -s -X POST https://${{ steps.ssm.outputs.STAGE_FLUX_DOMAIN }}/uapi/organizations/sender-email-domains/run-periodic-verification/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "##Production crons\n" >> cronjobs
        printf "#LiveDisplay - social\n*/2 * * * * curl -s https://${{ steps.ssm.outputs.PRODUCTION_LIVEDISPLAY_DOMAIN }}/api/v1/update/twitter/${{ secrets.LD_TWITTER_WEBHOOK }}\n" >> cronjobs
        printf "#LiveDisplay - news\n*/5 * * * * curl -s https://${{ steps.ssm.outputs.PRODUCTION_LIVEDISPLAY_DOMAIN }}/api/v1/update/news/${{ secrets.LD_NEWS_WEBHOOK }}\n" >> cronjobs
        printf "#LiveDisplay - agenda\n*/2 * * * * curl -s https://${{ steps.ssm.outputs.PRODUCTION_LIVEDISPLAY_DOMAIN }}/api/v1/update/event/agenda/${{ secrets.LD_AGENDA_WEBHOOK }}\n" >> cronjobs
        printf "#LiveDisplay - leaderboard\n* * * * * curl -s https://${{ steps.ssm.outputs.PRODUCTION_LIVEDISPLAY_DOMAIN }}/api/v1/update/event/leaderboard/${{ secrets.LD_LEADERBOARD_WEBHOOK }}\n" >> cronjobs
        printf "#Cleanup expired oauth2 tokens\n*/20 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/cleanup-expired-tokens?limit=1000 -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.4\"\n" >> cronjobs
        printf "#Populate Live Stream delivery\n2 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/videos/livestream/populate-delivery-records/ -H 'accept: application/vnd.eventmobi+json; version=p.5' -H 'authorization: Basic ${{ secrets.UAPI_TOKEN }}'\n" >> cronjobs
        printf "#Cleanup pending and cancelled images\n0 10 * * * curl -s https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/cron/clean-images -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.3\"\n" >> cronjobs
        printf "#Cleanup orphaned images and associated files\n0 23 * * 6 curl -s -X DELETE https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/images/clean-orphaned-images -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Deletion Clean Up\n0 * * * * curl -s -X DELETE https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/deletion_requests/remove -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Notify upcoming sessions\n*/2 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/sessions/schedule-upcoming-notifications/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Delete video assets\n0 6 * * * curl -s -X DELETE https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/videos/asset/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Pre-schedule announcement sender\n*/1 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/announcements/send_scheduled_announcements/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Process org custom domains\n5 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/process-custom-domains/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "#Expire org custom domains\n0 0 * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/expire-custom-domains/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "#Delete org custom domains\n55 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/delete-custom-domains/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "#Disconnect org custom domains\n15 8 * * 1 curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/disconnect-custom-domains/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "#Lock user credits for past and ongoing events\n15 6 * * * curl -s -X PATCH https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/lock-user-credits/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        # TODO: Remove the comments below when EXP-12954 is completed
        # printf "#Delete expired SSO settings\n30 8 * * 0 curl -s -X DELETE https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/sso/expired/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        # printf "#Send SSO email notifications\n0 8 * * * curl -s -X DELETE https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/sso/send-notifications/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        # printf "#Clean expired native application data\n*/0 9 * * 0 curl -s -X DELETE https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/native-applications/clean-expired-data/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#User email sender\n*/1 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/user-emails/send-scheduled-user-emails/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Notify and check user credit limits\n0 5 * * * curl -s -X PATCH https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/contracts/check-and-notify-limits/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "#Send Offline Notifications\n*/5 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/v1/action/send-offline-notifications -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Content-Type: application/json\" -d '{\"time_offset\": 3}'" >> cronjobs
        printf "#Run Sender Email Domain Periodic Verification \n*/5 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/sender-email-domains/run-periodic-verification/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "#Notify lead capture representatives that event has ended\n*/0 12 * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/cron/notify-company-representatives/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Archive old events\n*/0 9 * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/event-management/archive-events/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.7\"\n" >> cronjobs
        printf "#Test all payment gateways\n*/0 9 * * 0 curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/payments/gateways/test-all-connections/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.5\"\n" >> cronjobs
        printf "#Expire reg orders\n*/1 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/registration/orders/expire-orders/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.9\"\n" >> cronjobs
        printf "#Process pending payment transactions\n*/5 * * * * curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/payments/process-pending-transactions/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.9\"\n" >> cronjobs
        printf "#Notify people credit event organizers\n*/0 10 * * 0 curl -s -X POST https://${{ steps.ssm.outputs.PRODUCTION_FLUX_DOMAIN }}/uapi/organizations/events/notify-upcoming-people-credit-organizers/ -H \"Authorization: Basic ${{ secrets.UAPI_TOKEN }}\" -H \"Accept: application/vnd.eventmobi+json; version=p.9\"\n" >> cronjobs
        printf "\n" >> cronjobs
    - name: Login to aws ecr
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
    - name: Build, tag, and push image to aws ecr
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: eventmobi/cron
        IMAGE_TAG: ${{ github.sha }}_${{ steps.current-date.outputs.DATE }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:latest -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
        echo "$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" > image_uri.txt
    - name: Update task definition
      env:
        FARGATE_ENV: production
        AWS_REGION: us-east-1
      run: |
        sed -i.bak \
        -e "s#<TASK_DEFINITION_FAMILY>#Cron${FARGATE_ENV^}#g" \
        -e "s#<TASK_DEFINITION_CONTAINER>#Cron${FARGATE_ENV^}Container#g" \
        -e "s#<TASK_DEFINITION_ENVIRONMENT>#${FARGATE_ENV}#g" \
        -e "s#<TASK_DEFINITION_TASK_ROLE_ARN>#${{ steps.ssm.outputs.AWS_FARGATE_TASK_IAM_ROLE_ARN }}#g" \
        -e "s#<TASK_DEFINITION_EXECUTION_ROLE_ARN>#${{ steps.ssm.outputs.AWS_FARGATE_EXECUTION_IAM_ROLE_ARN }}#g" \
        -e "s#<AWS_REGION>#${AWS_REGION}#g" \
        task-definition.json
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: cron-artifacts
        path: |
          cron/*.json
          cron/*.txt
  deploy:
    name: Deploy
    needs: build
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: cron-artifacts
    - name: Set image uri
      id: image-uri
      run: |
        IMAGE_URI=`cat image_uri.txt`
        echo "IMAGE=$IMAGE_URI" >> $GITHUB_OUTPUT
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    - name: Render aws ecs task definition for cron container
      id: cron-container
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: CronProductionContainer
        image: ${{ steps.image-uri.outputs.IMAGE }}
    - name: Deploy aws ecs task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v2
      with:
        task-definition: ${{ steps.cron-container.outputs.task-definition }}
        service: cron-production
        cluster: production-fargate
        wait-for-service-stability: true
