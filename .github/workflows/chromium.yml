name: Chromium updater
on:
  workflow_dispatch:
    inputs:
      environment:
        description: Environment
        required: true
        type: choice
        options:
          - stage
          - production
      action:
        description: Action
        required: true
        type: choice
        options:
            - upgrade
            - downgrade
      version:
          description: Version of the chromium build that we want to upgrade to
          required: true
      aws-region:
        description: AWS region
        required: true
        type: choice
        options:
            - us-east-1
jobs:
  updater:
    name: Chromium updater
    runs-on: ubuntu-latest
    steps:
    - name: Download binaries
      if: github.event.inputs.action == 'upgrade'
      run: |
          wget "https://playwright.azureedge.net/builds/chromium/${{ github.event.inputs.version }}/chromium-linux.zip"
          wget "https://playwright.azureedge.net/builds/chromium/${{ github.event.inputs.version }}/chromium-linux-arm64.zip"
      shell: bash
    - name: Verify download
      if: github.event.inputs.action == 'upgrade'
      run: |
          if [ -f chromium-linux.zip ] && [ -f chromium-linux-arm64.zip ]; then
            echo "Files downloaded successfully"
          else
            echo "File download failed"
            exit 1
          fi
      shell: bash
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ github.event.inputs.aws-region }}
    - name: Get s3 bucket
      id: s3_bucket
      run: |
          S3_BUCKET=$(aws ssm get-parameter --name /${{ github.event.inputs.environment }}/flux/s3/private_bucket --query Parameter.Value)
          echo "S3_BUCKET=${S3_BUCKET:1:-1}" >> $GITHUB_OUTPUT
      shell: bash
    - name: Upgrade binaries
      if: github.event.inputs.action == 'upgrade'
      env:
          BUCKET: ${{ steps.s3_bucket.outputs.S3_BUCKET }}
      run: |
            if aws s3api head-object --bucket $BUCKET --key assets/chromium.zip 2>/dev/null; then
                aws s3 mv "s3://$BUCKET/assets/chromium.zip" "s3://$BUCKET/assets/chromium-old.zip"
            fi
            aws s3 cp chromium-linux.zip "s3://$BUCKET/assets/chromium.zip"

            if aws s3api head-object --bucket $BUCKET --key assets/chromium-arm64.zip 2>/dev/null; then
                aws s3 mv "s3://$BUCKET/assets/chromium-arm64.zip" "s3://$BUCKET/assets/chromium-arm64-old.zip"
            fi
            aws s3 cp chromium-linux-arm64.zip "s3://$BUCKET/assets/chromium-arm64.zip"
      shell: bash
    - name: Downgrade to previous version
      if: github.event.inputs.action == 'downgrade'
      env:
          BUCKET: ${{ steps.s3_bucket.outputs.S3_BUCKET }}
      run: |
            aws s3 mv "s3://$BUCKET/assets/chromium-old.zip" "s3://$BUCKET/assets/chromium.zip"
            aws s3 mv "s3://$BUCKET/assets/chromium-arm64-old.zip" "s3://$BUCKET/assets/chromium-arm64.zip"
      shell: bash
