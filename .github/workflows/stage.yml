name: Stage

on:
  workflow_dispatch:
  push:
    branches:
    - master

jobs:
  setup-tests:
    name: Setup Tests
    runs-on: ubuntu-latest
    outputs:
      jobs: ${{ steps.split-tests.outputs.jobs }}
    services:
      registry:
        image: registry:2
        ports:
          - 5000:5000
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Build Flux Base
        uses: ./.github/actions/build-base
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          s3-bucket-amd64: ${{ vars.S3_CACHE_AMD_BUCKET }}
      - name: Split Tests
        uses: ./.github/actions/split-tests
        id: split-tests
        with:
          config: |
            [
              {
                "type": "split",
                "path": "test/unit/domains/",
                "ignore": ["test/unit/domains/event"],
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/adapters/",
                "ignore": ["test/unit/adapters/uapi"],
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/adapters/uapi/",
                "ignore": ["test/unit/adapters/uapi/events_domain_endpoints"],
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/domains/event",
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/flux_services/",
                "chunks": 2
              },
              {
                "type": "split",
                "path": "test/unit/adapters/uapi/events_domain_endpoints",
                "chunks": 2
              },
              {
                "type": "fixed",
                "path": "test/unit/app/ test/unit/utils/"
              },
              {
                "type": "fixed",
                "path": "test/unit/services/"
              }
            ]
  checks:
    name: Checks
    needs: setup-tests
    runs-on: ubuntu-latest
    services:
      registry:
        image: registry:2
        ports:
          - 5000:5000
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Run checks
      uses: ./.github/actions/check
  tests:
    name: Tests
    needs: [checks, setup-tests]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        unit_tests_to_run: ${{ fromJson(needs.setup-tests.outputs.jobs) }}
    services:
      registry:
        image: registry:2
        ports:
          - 5000:5000
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Run tests
      uses: ./.github/actions/test
      with:
        deepsource-dsn: ${{ secrets.DEEPSOURCE_DSN }}
        unit-tests-to-run: ${{ matrix.unit_tests_to_run }}
  run-migration:
    needs: [tests]
    if: "!contains(github.event.pull_request.title, 'Revert')"
    name: Run Local Migration
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run Local Migration
        uses: ./.github/actions/local-migration
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          local-mysql-root-password: ${{ secrets.LOCAL_MYSQL_ROOT_PASSWORD }}
          local-mysql-database: ${{ secrets.LOCAL_MYSQL_DATABASE }}
          local-mysql-user: ${{ secrets.LOCAL_MYSQL_USER }}
          local-mysql-password: ${{ secrets.LOCAL_MYSQL_PASSWORD }}
          s3-bucket: ${{ vars.LOCAL_DB_DUMP_BUCKET }}
  build-localdb:
    needs: [run-migration]
    if: "!contains(github.event.pull_request.title, 'Revert')"
    name: Build Local DB
    runs-on: ubuntu-latest-arm64
    steps:
      - uses: actions/checkout@v4
      - name: Build & Push Local DB
        uses: ./.github/actions/local-db
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          local-mysql-root-password: ${{ secrets.LOCAL_MYSQL_ROOT_PASSWORD }}
          local-mysql-database: ${{ secrets.LOCAL_MYSQL_DATABASE }}
          local-mysql-user: ${{ secrets.LOCAL_MYSQL_USER }}
          local-mysql-password: ${{ secrets.LOCAL_MYSQL_PASSWORD }}
          s3-bucket: ${{ vars.LOCAL_DB_DUMP_BUCKET }}
  build:
    name: Build
    needs: [run-migration, tests]
    runs-on: ubuntu-latest-arm64
    services:
      registry:
        image: registry:2
        ports:
          - 5000:5000
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Translation
      uses: ./.github/actions/translation
      with:
        transifex-api-token: ${{ secrets.TRANSIFEX_API_TOKEN }}
        sendgrid-api-token: ${{ secrets.SENDGRID_API_KEY }}
    - name: Build and push
      id: build-push
      uses: ./.github/actions/build
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
        s3-bucket-arm64: ${{ vars.S3_CACHE_ARM_BUCKET }}
        s3-bucket-amd64: ${{ vars.S3_CACHE_AMD_BUCKET }}
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    - name: Get required variables from aws ssm
      id: ssm
      run: |
        AWS_FARGATE_TASK_IAM_ROLE_ARN=$(aws ssm get-parameter --name /stage/terraform/iam/ecs/task_role_arn --query Parameter.Value)
        AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=$(aws ssm get-parameter --name /stage/terraform/iam/ecs/task_execution_role_arn --query Parameter.Value)
        echo "AWS_FARGATE_TASK_IAM_ROLE_ARN=${AWS_FARGATE_TASK_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
        echo "AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=${AWS_FARGATE_EXECUTION_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
    - name: Update task definition
      uses: ./.github/actions/task-definition
      with:
        fargate-env: stage
        fargate-task-iam-role-arn: ${{ steps.ssm.outputs.AWS_FARGATE_TASK_IAM_ROLE_ARN }}
        fargate-execution-iam-role-arn: ${{ steps.ssm.outputs.AWS_FARGATE_EXECUTION_IAM_ROLE_ARN }}
        aws-region: us-east-1
        app-version: ${{ steps.build-push.outputs.APP_VERSION }}
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: stage-artifacts
        path: |
          task_definitions/*.json
          *_uri.txt
  migration:
    name: Db migration
    needs: build
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: stage-artifacts
    - name: Run db migration
      uses: ./.github/actions/flask-command
      with:
        environment: stage
        command: '[\"./cli.sh\",\"db\",\"upgrade\",\"head\"]'
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
        cpu: 1024
        memory: 2048
        wait-for-response: true
  deploy:
    name: Deploy
    needs: migration
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: stage-artifacts
    - name: Run deployment
      uses: ./.github/actions/deploy
      with:
        environment: stage
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
