name: Private UAPI Spec

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version of the UAPI Spec'
        required: true
        default: 'latest'
  push:
    branches:
      - master
    paths:
      - 'uapi-spec/**'

jobs:
  build-and-push:
    name: Build & Push
    runs-on: ubuntu-latest
    steps:

      - name: Values
        id: values
        shell: bash
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            if [ "${{ github.event.inputs.version }}" == "latest" ]; then
              ref="master"
              s3_prefix="private/latest"
            else
              ref="uapi-${{ github.event.inputs.version }}"
              s3_prefix="private/${{ github.event.inputs.version }}"
            fi
          else
            ref="master"
            s3_prefix="private/latest"
          fi
          echo "ref=$ref" >> $GITHUB_OUTPUT
          echo "s3_prefix=$s3_prefix" >> $GITHUB_OUTPUT

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event_name == 'workflow_dispatch' && steps.values.outputs.ref || github.ref }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Build and Push Private UAPI Spec
        uses: ./.github/actions/build-uapi
        with:
          spec: private
          s3_prefix: ${{ steps.values.outputs.s3_prefix }}
          s3_bucket: ${{ vars.UAPI_PRIVATE_DOC_S3_BUCKET }}

      - name: Build and Push Public UAPI Spec
        uses: ./.github/actions/build-uapi
        with:
          spec: public
          s3_prefix: latest
          s3_bucket: ${{ vars.UAPI_PRIVATE_DOC_S3_BUCKET }}
