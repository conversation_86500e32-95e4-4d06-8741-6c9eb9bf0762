name: Run Flask Command

on:
  workflow_dispatch:
    inputs:
      environment:
        description: Environment
        required: true
        type: choice
        options:
          - stage
          - production
      version:
        description: Version To Deploy (ShortCommitSha_YYYY-MM-DD) or latest
      command:
        description: Command to run cli (E.g. [\"./cli.sh\",\"db\",\"upgrade\",\"head\"])
        required: true
      aws-region:
        description: AWS region
        required: true
        default: 'us-east-1'
      cpu:
        description: 'cpu of container workload'
        required: true
        type: choice
        options:
          - 1024
          - 4096
      memory:
        description: 'memory of container workload'
        required: true
        type: choice
        options:
          - 2048
          - 10240
      wait-for-response:
        description: 'get task data and wait for task to finish'
        required: true
        type: boolean
        default: true

jobs:
  initialize:
    name: Initialize
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ github.event.inputs.aws-region }}
    - name: Login to aws ecr
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
    - name: Set aws ecr image from input
      id: ecr-image-version
      if: github.event.inputs.version != ''
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: eventmobi/flux
        IMAGE_TAG: ${{ github.event.inputs.version }}
      run: echo "$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" > image_uri.txt
    - name: Get aws ecr image
      id: ecr-image
      if: github.event.inputs.version == ''
      run: |
        FLUX_IMAGE=$(aws ssm get-parameter --name /${{ github.event.inputs.environment }}/flux/image_uri --query Parameter.Value)
        echo ${FLUX_IMAGE:1:-1} > image_uri.txt
    - name: Get required variables from aws ssm
      id: ssm
      run: |
        AWS_FARGATE_TASK_IAM_ROLE_ARN=$(aws ssm get-parameter --name /${{ github.event.inputs.environment }}/terraform/iam/ecs/task_role_arn --query Parameter.Value)
        AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=$(aws ssm get-parameter --name /${{ github.event.inputs.environment }}/terraform/iam/ecs/task_execution_role_arn --query Parameter.Value)
        echo "AWS_FARGATE_TASK_IAM_ROLE_ARN=${AWS_FARGATE_TASK_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
        echo "AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=${AWS_FARGATE_EXECUTION_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
    - name: Update task definition
      uses: ./.github/actions/task-definition
      with:
        fargate-env: ${{ github.event.inputs.environment }}
        fargate-task-iam-role-arn: ${{ steps.ssm.outputs.AWS_FARGATE_TASK_IAM_ROLE_ARN }}
        fargate-execution-iam-role-arn: ${{ steps.ssm.outputs.AWS_FARGATE_EXECUTION_IAM_ROLE_ARN }}
        aws-region: ${{ github.event.inputs.aws-region }}
        app-version: ${{ github.event.inputs.version }}
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: one-off-artifacts
        path: |
          task_definitions/*.json
          image_uri.txt
  run_one_off:
    name: Run one off
    needs: initialize
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: one-off-artifacts
    - name: Run flask command
      uses: ./.github/actions/flask-command
      with:
        environment: ${{ github.event.inputs.environment }}
        command: ${{ github.event.inputs.command }}
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
        memory: ${{ github.event.inputs.memory }}
        cpu: ${{ github.event.inputs.cpu }}
        wait-for-response: ${{ github.event.inputs.wait-for-response }}
