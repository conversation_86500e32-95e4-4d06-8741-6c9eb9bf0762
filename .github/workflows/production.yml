name: Production

on:
  workflow_dispatch:

jobs:
  initialize:
    name: Initialize
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    - name: Get required configs from aws ssm
      id: ssm
      run: |
        FLUX_IMAGE=$(aws ssm get-parameter --name /stage/flux/image_uri --query Parameter.Value)
        NGINX_IMAGE=$(aws ssm get-parameter --name /stage/flux/nginx_image_uri --query Parameter.Value)
        AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=$(aws ssm get-parameter --name /production/terraform/iam/ecs/task_execution_role_arn --query Parameter.Value)
        AWS_FARGATE_TASK_IAM_ROLE_ARN=$(aws ssm get-parameter --name /production/terraform/iam/ecs/task_role_arn --query Parameter.Value)
        echo ${FLUX_IMAGE:1:-1} > image_uri.txt
        echo ${NGINX_IMAGE:1:-1} > nginx_image_uri.txt
        echo "AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=${AWS_FARGATE_EXECUTION_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
        echo "AWS_FARGATE_TASK_IAM_ROLE_ARN=${AWS_FARGATE_TASK_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
    - name: Set application version
      id: set-app-version
      run: |
        IMAGE_URI=`cat image_uri.txt`
        IMAGE_TAG=${IMAGE_URI##*:}
        echo "APP_VERSION=${IMAGE_TAG::-1}" >> $GITHUB_OUTPUT
    - name: Update task definition
      uses: ./.github/actions/task-definition
      with:
        fargate-env: production
        fargate-task-iam-role-arn: ${{ steps.ssm.outputs.AWS_FARGATE_TASK_IAM_ROLE_ARN }}
        fargate-execution-iam-role-arn: ${{ steps.ssm.outputs.AWS_FARGATE_EXECUTION_IAM_ROLE_ARN }}
        aws-region: us-east-1
        app-version: ${{ steps.set-app-version.outputs.APP_VERSION }}
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: production-artifacts
        path: |
          task_definitions/*.json
          *_uri.txt
  migration:
    name: Db migration
    needs: initialize
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: production-artifacts
    - name: Run db migration
      uses: ./.github/actions/flask-command
      with:
        environment: production
        command: '[\"./cli.sh\",\"db\",\"upgrade\",\"head\"]'
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
        cpu: 1024
        memory: 2048
        wait-for-response: true
  deploy:
    name: Deploy
    needs: migration
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: production-artifacts
    - name: Run deployment
      uses: ./.github/actions/deploy
      with:
        environment: production
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
