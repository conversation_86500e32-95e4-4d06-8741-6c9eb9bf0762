name: Public UAPI Spec

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version of the UAPI Spec'
        required: true
        default: 'latest'

jobs:
  build-and-push:
    name: Build & Push
    runs-on: ubuntu-latest
    steps:
      - name: Values
        id: values
        shell: bash
        run: |
          if [ "${{ github.event.inputs.version }}" == "latest" ]; then
            ref="master"
          else
            ref="uapi-${{ github.event.inputs.version }}"
          fi

          echo "ref=$ref" >> $GITHUB_OUTPUT

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ steps.values.outputs.ref }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Build and Push Public UAPI Spec
        uses: ./.github/actions/build-uapi
        with:
          spec: public
          s3_prefix: ${{ github.event.inputs.version }}
          s3_bucket: ${{ vars.UAPI_PUBLIC_DOC_S3_BUCKET }}

      - name: Copy the index file
        shell: bash
        run: |
          aws s3 cp uapi-spec/uapi-public-index.html s3://${{ vars.UAPI_PUBLIC_DOC_S3_BUCKET }}/index.html
