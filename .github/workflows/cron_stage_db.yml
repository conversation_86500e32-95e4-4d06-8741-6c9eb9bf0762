name: Stage DB Migration

on:
  workflow_dispatch:
  schedule:
    - cron: '30 8 * * *'

jobs:
  initialize:
    name: Initialize
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    - name: Login to aws ecr
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
    - name: Get aws ecr image info
      id: ecr-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: eventmobi/flux
        IMAGE_TAG: latest
      run: echo "$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" > image_uri.txt
    - name: Get required variables from aws ssm
      id: ssm
      run: |
        AWS_FARGATE_TASK_IAM_ROLE_ARN=$(aws ssm get-parameter --name /stage/terraform/iam/ecs/task_role_arn --query Parameter.Value)
        AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=$(aws ssm get-parameter --name /stage/terraform/iam/ecs/task_execution_role_arn --query Parameter.Value)
        echo "AWS_FARGATE_TASK_IAM_ROLE_ARN=${AWS_FARGATE_TASK_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
        echo "AWS_FARGATE_EXECUTION_IAM_ROLE_ARN=${AWS_FARGATE_EXECUTION_IAM_ROLE_ARN:1:-1}" >> $GITHUB_OUTPUT
    - name: Update task definition
      uses: ./.github/actions/task-definition
      with:
        fargate-env: stage
        fargate-task-iam-role-arn: ${{ steps.ssm.outputs.AWS_FARGATE_TASK_IAM_ROLE_ARN }}
        fargate-execution-iam-role-arn: ${{ steps.ssm.outputs.AWS_FARGATE_EXECUTION_IAM_ROLE_ARN }}
        aws-region: us-east-1
        app-version: latest
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: cron-stage-db-artifacts
        path: |
          task_definitions/*.json
          image_uri.txt
  run_db_migration:
    name: Run stage db migration
    needs: initialize
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: cron-stage-db-artifacts
    - name: Run flask command
      uses: ./.github/actions/flask-command
      with:
        environment: stage
        command: '[\"./cli.sh\",\"db\",\"upgrade\",\"head\"]'
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
        cpu: 1024
        memory: 2048
        wait-for-response: true
