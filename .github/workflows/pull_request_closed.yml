name: Delete UAPI PR Preview

on:
  pull_request:
    types: [closed]

jobs:
  delete-preview:
    name: Delete the UAPI Preview
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Delete UAPI PR previews
        run: |
          # Delete public preview folder
          aws s3 rm s3://${{ vars.UAPI_PRIVATE_DOC_S3_BUCKET }}/previews/${{ github.event.pull_request.number }} --recursive
          # Delete private preview folder
          aws s3 rm s3://${{ vars.UAPI_PRIVATE_DOC_S3_BUCKET }}/previews/private/${{ github.event.pull_request.number }} --recursive
