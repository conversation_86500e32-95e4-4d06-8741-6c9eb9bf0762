name: Requirements Check

on:
  workflow_dispatch:
  schedule:
    - cron: '0 8 * * 0'

jobs:
  check:
    name: Check Python Packages
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Install uv
      uses: astral-sh/setup-uv@v5

    - uses: actions/setup-python@v4
      with:
        python-version-file: 'pyproject.toml'

    - name: Check packages
      id: checker
      shell: bash
      run: |
        uv sync --frozen --no-install-project --no-managed-python --all-groups
        source .venv/bin/activate
        echo "create-pr=$(python requirements_check.py)" >> $GITHUB_OUTPUT
        uv sync --no-install-project --no-managed-python --all-groups

    - name: Generate github app token
      if: steps.checker.outputs.create-pr == 'true'
      id: generate-token
      uses: tibdex/github-app-token@v1
      with:
        app_id: ${{ secrets.AUTOMATED_PR_APP_ID }}
        private_key: ${{ secrets.AUTOMATED_PR_APP_PRIVATE_KEY }}
    - name: Create Pull Request
      if: steps.checker.outputs.create-pr == 'true'
      id: cpr
      uses: peter-evans/create-pull-request@v4
      with:
        commit-message: update dependencies
        committer: GitHub <<EMAIL>>
        author: ${{ github.actor }} <${{ github.actor }}@users.noreply.github.com>
        signoff: false
        base: master
        branch: dependency-check
        branch-suffix: random
        delete-branch: true
        token: ${{ steps.generate-token.outputs.token }}
        title: 'tech: update flux dependencies'
        body: |
            #### What do we need to know to understand these changes?
            - Flux dependencies are not update to date

            #### What does this PR do?
            - Update python packages to latest available version in pypi

            #### How can we manually test your changes?
            - Check unit tests and if there is any major upgrade make sure to test manually

            #### Are there any blockers to merging this ticket?
            - It shouldn't however if there is any major version upgrade and it cannot be done
              through this pull request, you can create a jira ticket for it and update the package
              with more testing and code updates

            #### What are you not sure of?
            --

            #### Is this PR Backwards Compatible?
            - If there is no code change for library updates it is backward compatible however if
              there is any code change it may not be backward compatible

            #### Relevant documentation:
            --
        labels: |
            dependencies
            automated pr
