name: Configuror

on:
  workflow_dispatch:
    inputs:
      environment:
        description: Environment
        required: true
        type: choice
        options:
          - dev
          - stage
          - production
      name:
        description: Config Name
        required: true
      value:
        description: Config Value (If you have "", it needs to have proper escaping e.g. \"test\")
        required: false
      type:
        description: Config Type
        required: true
        type: choice
        options:
            - String
            - SecureString
            - StringList
      action:
        description: Action
        required: true
        type: choice
        options:
            - create
            - update
            - delete
      aws-region:
        description: AWS region
        required: true
        type: choice
        options:
            - us-east-1

jobs:
  configuror:
    name: Flux configuror
    runs-on: ubuntu-latest
    steps:
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ github.event.inputs.aws-region }}
    - name: Create config
      if: github.event.inputs.action == 'create'
      run: |
        aws ssm put-parameter \
            --name "/${{ github.event.inputs.environment }}/flux/${{ github.event.inputs.name }}" \
            --type "${{ github.event.inputs.type }}" \
            --value "${{ github.event.inputs.value }}" \
            --tier Standard \
            --tags "[{\"Key\":\"Terraform\",\"Value\":\"false\"},{\"Key\":\"Environment\",\"Value\":\"${{ github.event.inputs.environment }}\"},{\"Key\":\"Application\",\"Value\":\"flux\"}]"
    - name: Update config
      if: github.event.inputs.action == 'update'
      run: |
        aws ssm put-parameter \
            --name "/${{ github.event.inputs.environment }}/flux/${{ github.event.inputs.name }}" \
            --type "${{ github.event.inputs.type }}" \
            --value "${{ github.event.inputs.value }}" \
            --tier Standard \
            --overwrite
    - name: Delete config
      if: github.event.inputs.action == 'delete'
      run: |
        aws ssm delete-parameter \
            --name "/${{ github.event.inputs.environment }}/flux/${{ github.event.inputs.name }}"
