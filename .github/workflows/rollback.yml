name: Rollback

on:
  workflow_dispatch:
    inputs:
      environment:
        description: Environment
        required: true
        type: choice
        options:
          - stage
          - production
      revision:
        description: Task Definition Revision (If revert to previous revision, you don't need to provide this field)
        required: false
      aws-region:
        description: AWS region
        required: true
        default: 'us-east-1'

jobs:
  rollback:
    name: Rollback
    runs-on: ubuntu-latest
    steps:
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ github.event.inputs.aws-region }}
    - name: Get current task definition arn
      id: task-definition
      if: github.event.inputs.revision == ''
      env:
        ENV: ${{ github.event.inputs.environment }}
      run: |
        echo "TASK_DEFINITION_ARN=$(aws ecs describe-services \
            --cluster ${ENV}-fargate \
            --services flux-${ENV} \
            --query 'services[0].taskDefinition')" >> $GITHUB_OUTPUT
    - name: Get current revision
      id: current-revision
      if: github.event.inputs.revision == ''
      env:
        TASK_DEFINITION_ARN: ${{ steps.task-definition.outputs.TASK_DEFINITION_ARN }}
      run: |
        REVISION_ID=${TASK_DEFINITION_ARN##*:}
        CURRENT_REVISION_ID=${REVISION_ID::-1}
        PREVIOUS_REVISION_ID=$((CURRENT_REVISION_ID - 1))
        echo "PREVIOUS_REVISION_ID=${PREVIOUS_REVISION_ID}" >> $GITHUB_OUTPUT
    - name: Set revision
      id: set-revision
      env:
        REVISION: ${{ github.event.inputs.revision }}
      run: |
        if [ -z "$REVISION" ]; then REVISION=${{ steps.current-revision.outputs.PREVIOUS_REVISION_ID }}; fi
        echo "REVISION_ID=${REVISION}" >> $GITHUB_OUTPUT
    - name: Update service
      env:
        ENV: ${{ github.event.inputs.environment }}
      run: |
        aws ecs update-service \
            --cluster ${ENV}-fargate \
            --service flux-${ENV} \
            --task-definition Flux${ENV^}:${{ steps.set-revision.outputs.REVISION_ID }}
    - name: Wait for service stability
      env:
        ENV: ${{ github.event.inputs.environment }}
      run: |
        aws ecs wait services-stable \
            --cluster ${ENV}-fargate \
            --services flux-${ENV}
