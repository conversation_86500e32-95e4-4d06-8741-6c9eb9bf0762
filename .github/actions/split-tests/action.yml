name: Split tests
description: Calculate jobs for the provided test directory
inputs:
  config:
    description: JSON configuration for test groups
    required: true
outputs:
  jobs:
    description: 'Jobs list'
    value: ${{ steps.chunk-dir.outputs.result }}
runs:
  using: 'composite'
  steps:
    - name: Chunk Directories
      id: chunk-dir
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs')
          const path = require('path')

          function chunkItems(directory, numChunks, ignorePaths) {
            const items = fs.readdirSync(directory, { withFileTypes: true })
              .filter(item => {
                const fullPath = path.join(directory, item.name);
                return !ignorePaths.includes(fullPath) && 
                  item.name !== '__init__.py' &&
                  (item.isDirectory() || (item.isFile() && item.name.startsWith('test_')));
              })
              .map(item => path.join(directory, item.name))

            const chunkSize = Math.ceil(items.length / numChunks)
            const chunks = []
            for (let i = 0; i < items.length; i += chunkSize) {
              let chunk = items.slice(i, i + chunkSize)
              chunks.push(chunk.join(' '))
            }

            return chunks
          }

          const config = ${{ inputs.config }}
          let matrix = []

          for (const group of config) {
            if (group.type === 'split') {
              const chunks = chunkItems(group.path, group.chunks, group.ignore || [])
              matrix.push(...chunks)
              console.log('Group ' + group.path)
              console.log(chunks)
            } else if (group.type === 'fixed') {
              matrix.push(group.path)
            }
          }

          return matrix
