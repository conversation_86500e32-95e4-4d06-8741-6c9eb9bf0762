name: 'Task definition'
description: 'Prepare task definition for respective environment'
inputs:
  fargate-env:
    description: 'Which environment is being used for this run'
    required: true
  fargate-task-iam-role-arn:
      description: 'Fargate task iam role to be used for this run'
      required: true
  fargate-execution-iam-role-arn:
      description: 'Fargate execution iam role to be used for this run'
      required: true
  aws-region:
    description: 'Which aws region is being used for this run'
    required: true
    default: 'us-east-1'
  app-version:
    description: 'Flux version'
    required: true
runs:
  using: 'composite'
  steps:
    - name: Update task definition
      env:
        FARGATE_ENV: ${{ inputs.fargate-env }}
      run: |
        sed -i.bak \
        -e '/"<ENVIRONMENT_VARIABLES>"/ r task_definitions/env.json' -e s/"\"<ENVIRONMENT_VARIABLES>\","// \
        -e '/"<ENVIRONMENT_SECRETS>"/ r task_definitions/secrets.json' -e s/"\"<ENVIRONMENT_SECRETS>\","// \
        task_definitions/*.json
        sed -i.bak \
        -e "s#<TASK_DEFINITION_ENVIRONMENT>#${FARGATE_ENV}#g" \
        -e "s#<TASK_DEFINITION_TASK_ROLE_ARN>#${{ inputs.fargate-task-iam-role-arn }}#g" \
        -e "s#<TASK_DEFINITION_EXECUTION_ROLE_ARN>#${{ inputs.fargate-execution-iam-role-arn }}#g" \
        -e "s#<AWS_REGION>#${{ inputs.aws-region }}#g" \
        -e "s#<APP_VERSION># ${{ inputs.app-version }}}#g" \
        task_definitions/*.json
        sed -i.bak \
        -e "s#<TASK_DEFINITION_FAMILY>#Flux${FARGATE_ENV^}#g" \
        -e "s#<TASK_DEFINITION_NGINX_CONTAINER>#Nginx${FARGATE_ENV^}Container#g" \
        -e "s#<TASK_DEFINITION_CONTAINER>#Flux${FARGATE_ENV^}Container#g" \
        -e "s#<TASK_DEFINITION_CELERY_CONTAINER>#Celery${FARGATE_ENV^}Container#g" \
        task_definitions/flux.json
        sed -i.bak \
        -e "s#<TASK_DEFINITION_FAMILY>#RunOneOff${FARGATE_ENV^}#g" \
        -e "s#<TASK_DEFINITION_CONTAINER>#RunOneOff${FARGATE_ENV^}Container#g" \
        task_definitions/run_one_off*.json
      shell: bash
