name: Build & Push Local DB
description: 'This action is used to build local db image'
inputs:
  aws-access-key-id:
    description: 'aws access key id to be used in this run'
    required: true
  aws-secret-access-key:
      description: 'aws secret access key to be used in this run'
      required: true
  aws-region:
    description: 'aws region to be used in this run'
    required: true
  local-mysql-root-password:
    description: 'local mysql root password'
    required: true
  local-mysql-database:
    description: 'local mysql database'
    required: true
  local-mysql-user:
    description: 'local mysql user'
    required: true
  local-mysql-password:
    description: 'local mysql password'
    required: true
  s3-bucket:
    description: 's3 bucket to store the db dump'
    required: true
runs: 
  using: 'composite'

  steps:
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        aws-region: ${{ inputs.aws-region }}

    - name: Create entrypoint
      shell: bash
      run: |
        mkdir entrypoint
        cd entrypoint
        aws s3 cp s3://${{ inputs.s3-bucket }}/db_dump_80.sql.gz .

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      id: buildx
      with:
        install: true
        driver: docker

    - name: Build MySQL Image
      id: build-base-image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.mysql
        builder: ${{ steps.buildx.outputs.name }}
        push: false
        build-args: |
          MYSQL_ROOT_PASSWORD=${{ inputs.local-mysql-root-password }}
          MYSQL_DATABASE=${{ inputs.local-mysql-database }}
          MYSQL_USER=${{ inputs.local-mysql-user }}
          MYSQL_PASSWORD=${{ inputs.local-mysql-password }}
        tags: eventmobi/mysql:8

    - name: Push to ECR
      shell: bash
      run: |
        echo "Pushing to ECR"
        account_id=$(aws sts get-caller-identity --query "Account" --output text)
        ecr_registry=${account_id}.dkr.ecr.${{ inputs.aws-region }}.amazonaws.com
        aws ecr get-login-password --region ${{ inputs.aws-region }} | docker login --username AWS --password-stdin ${ecr_registry}
        docker tag eventmobi/mysql:8 ${ecr_registry}/eventmobi/mysql:8.0.36-arm
        docker push ${ecr_registry}/eventmobi/mysql:8.0.36-arm
