name: 'Deploy'
description: 'Run deployment'
inputs:
  environment:
    description: 'Which environment is being used for this run'
    required: true
  aws-access-key-id:
    description: 'aws access key id to be used in this run'
    required: true
  aws-secret-access-key:
      description: 'aws secret access key to be used in this run'
      required: true
  aws-region:
    description: 'aws region to be used in this run'
    required: true
runs:
  using: 'composite'
  steps:
    - name: Set image uri and fargate environment
      id: set-var
      env:
          FARGATE_ENV: ${{ inputs.environment }}
      run: |
        IMAGE_URI=`cat image_uri.txt`
        NGINX_IMAGE_URI=`cat nginx_image_uri.txt`
        FARGATE_ENV=${FARGATE_ENV^}
        echo "IMAGE=$IMAGE_URI" >> $GITHUB_OUTPUT
        echo "NGINX_IMAGE=$NGINX_IMAGE_URI" >> $GITHUB_OUTPUT
        echo "FARGATE_ENV=$FARGATE_ENV" >> $GITHUB_OUTPUT
      shell: bash
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        aws-region: ${{ inputs.aws-region }}
    - name: Render aws ecs task definition for nginx container
      id: nginx-container
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task_definitions/flux.json
        container-name: Nginx${{ steps.set-var.outputs.FARGATE_ENV }}Container
        image: ${{ steps.set-var.outputs.NGINX_IMAGE }}
    - name: Modify aws ecs task definition for flux container
      id: flux-container
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: ${{ steps.nginx-container.outputs.task-definition }}
        container-name: Flux${{ steps.set-var.outputs.FARGATE_ENV }}Container
        image: ${{ steps.set-var.outputs.IMAGE }}
    - name: Modify aws ecs task definition for celery container
      id: celery-container
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: ${{ steps.flux-container.outputs.task-definition }}
        container-name: Celery${{ steps.set-var.outputs.FARGATE_ENV }}Container
        image: ${{ steps.set-var.outputs.IMAGE }}
    - name: Deploy aws ecs task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v2
      with:
        task-definition: ${{ steps.celery-container.outputs.task-definition }}
        service: flux-${{ inputs.environment }}
        cluster: ${{ inputs.environment }}-fargate
        wait-for-service-stability: true
    - name: Upload image uris
      run: |
        aws ssm put-parameter \
            --name "/${{ inputs.environment }}/flux/image_uri" \
            --type String \
            --value "${{ steps.set-var.outputs.IMAGE }}" \
            --tier Standard \
            --overwrite
          aws ssm put-parameter \
            --name "/${{ inputs.environment }}/flux/nginx_image_uri" \
            --type String \
            --value "${{ steps.set-var.outputs.NGINX_IMAGE }}" \
            --tier Standard \
            --overwrite
      shell: bash
