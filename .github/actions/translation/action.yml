name: 'Translation'
description: 'Prepare translation'
inputs:
  transifex-api-token:
    description: 'Transifex API token'
    required: true
  sendgrid-api-token:
      description: 'Sendgrid API token'
      required: true
runs:
  using: 'composite'
  steps:
    - name: Install uv
      uses: astral-sh/setup-uv@v5
    - name: Set up python
      uses: actions/setup-python@v4
      with:
        python-version-file: 'pyproject.toml'
    - name: Install dependencies
      run: |
        uv add babel Jinja2 --no-config
        sudo apt-get install gettext
        curl -o- https://raw.githubusercontent.com/transifex/cli/master/install.sh | bash
      shell: bash
    - name: Extract strings
      id: extract-string
      env:
          LOCALE_DIR: flux/locale
          TRANSLATIONS_TEMPLATE: translations_template.pot
      run: |
        mkdir -p "$LOCALE_DIR"
        pybabel extract -F flux/babel.cfg -o ${LOCALE_DIR}/${TRANSLATIONS_TEMPLATE} ./flux
        echo "TEMPLATE=${LOCALE_DIR}/${TRANSLATIONS_TEMPLATE}" >> $GITHUB_OUTPUT
      shell: bash
    - name: Upload translation template
      env:
          TX_TOKEN: ${{ inputs.transifex-api-token }}
      run: |
        test -n "$$(find ${{ steps.extract-string.outputs.TEMPLATE }} -size +1000c)"
        mv ${{ steps.extract-string.outputs.TEMPLATE }} ${{ steps.extract-string.outputs.TEMPLATE }}.new
        ./tx pull -s
        mv ${{ steps.extract-string.outputs.TEMPLATE }} ${{ steps.extract-string.outputs.TEMPLATE }}.old
        mv ${{ steps.extract-string.outputs.TEMPLATE }}.new ${{ steps.extract-string.outputs.TEMPLATE }}
        msgcomm -u ${{ steps.extract-string.outputs.TEMPLATE }} ${{ steps.extract-string.outputs.TEMPLATE }}.old --output-file=translations_template_diff.pot
        rm ${{ steps.extract-string.outputs.TEMPLATE }}.old
        ./tx push -s
      shell: bash
    - name: Notify translation
      env:
          SENDGRID_TOKEN: ${{ inputs.sendgrid-api-token }}
      run: |
        if [ -a translations_template_diff.pot ]; then \
            curl https://api.sendgrid.com/api/mail.send.json -F to=<EMAIL> \
            -F subject="Translatable strings updated on transifex" -F text="See attachment for changes." \
            -F from=<EMAIL> -F files\[strings.pot\]=@translations_template_diff.pot \
            --header 'Authorization: Bearer $SENDGRID_TOKEN'\
        ; fi;
      shell: bash
    - name: Pull translations
      env:
          TX_TOKEN: ${{ inputs.transifex-api-token }}
      run: ./tx pull -a
      shell: bash
    - name: Compile translations
      run: |
        make compile_translations
        rm -rf tx LICENSE
      shell: bash
