name: Local Migration
description: 'This action is used to run a local migration'
inputs:
  aws-access-key-id:
    description: 'aws access key id to be used in this run'
    required: true
  aws-secret-access-key:
      description: 'aws secret access key to be used in this run'
      required: true
  aws-region:
    description: 'aws region to be used in this run'
    required: true
  local-mysql-root-password:
    description: 'local mysql root password'
    required: true
  local-mysql-database:
    description: 'local mysql database'
    required: true
  local-mysql-user:
    description: 'local mysql user'
    required: true
  local-mysql-password:
    description: 'local mysql password'
    required: true
  s3-bucket:
    description: 's3 bucket to store the db dump'
    required: true
runs: 
  using: 'composite'

  steps:
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        aws-region: ${{ inputs.aws-region }}

    - name: Create entrypoint
      shell: bash
      run: |
        mkdir entrypoint
        cd entrypoint
        aws s3 cp s3://${{ inputs.s3-bucket }}/db_dump_80.sql.gz .

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      id: buildx
      with:
        install: true
        driver: docker

    - name: Build MySQL Image
      id: build-base-image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.mysql
        builder: ${{ steps.buildx.outputs.name }}
        push: false
        build-args: |
          MYSQL_ROOT_PASSWORD=${{ inputs.local-mysql-root-password }}
          MYSQL_DATABASE=${{ inputs.local-mysql-database }}
          MYSQL_USER=${{ inputs.local-mysql-user }}
          MYSQL_PASSWORD=${{ inputs.local-mysql-password }}
        tags: eventmobi/mysql:8

    - name: Run MySQL Container
      id: run-mysql-container
      shell: bash
      run: |
        docker network create application
        container_id=$(docker run --network application --name database -d eventmobi/mysql:8 --innodb-lock-wait-timeout=120 --lock-wait-timeout=120 --sql-mode=0 --character-set-server=utf8mb4)
        docker ps -a

        echo "Container ID: $container_id"
        echo "container_id=$container_id" >> $GITHUB_OUTPUT

    - name: Download artifact
      uses: actions/download-artifact@v4
      with:
        name: flux-base
        path: /tmp
   
    - name: Load image
      shell: bash
      run: |
        docker load --input /tmp/flux-base.tar

    - name: Build Local flux image
      uses: docker/build-push-action@v6
      env:
        DOCKER_BUILD_SUMMARY: false
      with:
        context: .
        push: false
        builder: ${{ steps.buildx.outputs.name }}
        target: build
        no-cache: true
        build-args: |
          BASE_IMAGE=localhost:5000/eventmobi/flux-base
        tags: eventmobi/flux:latest

    - name: Run migration
      shell: bash
      run: |
        docker run --rm --network application --env FLUX_SQLALCHEMY_DB_USERNAME=${{ inputs.local-mysql-user }} --env FLUX_SQLALCHEMY_DB_PASSWORD=${{ inputs.local-mysql-password }} --env FLUX_SQLALCHEMY_DB_HOST=database --env FLUX_SQLALCHEMY_DB_DATABASE=${{ inputs.local-mysql-database }} --env PYTHONUNBUFFERED=1 eventmobi/flux:latest bash -c "./cli.sh db current || exit 1"
        docker run --rm --network application --env FLUX_SQLALCHEMY_DB_USERNAME=${{ inputs.local-mysql-user }} --env FLUX_SQLALCHEMY_DB_PASSWORD=${{ inputs.local-mysql-password }} --env FLUX_SQLALCHEMY_DB_HOST=database --env FLUX_SQLALCHEMY_DB_DATABASE=${{ inputs.local-mysql-database }} --env PYTHONUNBUFFERED=1 --env FLUX_LOG_LEVEL=DEBUG eventmobi/flux:latest bash -c "./cli.sh db upgrade head || exit 1"
        docker run --rm --network application --env FLUX_SQLALCHEMY_DB_USERNAME=${{ inputs.local-mysql-user }} --env FLUX_SQLALCHEMY_DB_PASSWORD=${{ inputs.local-mysql-password }} --env FLUX_SQLALCHEMY_DB_HOST=database --env FLUX_SQLALCHEMY_DB_DATABASE=${{ inputs.local-mysql-database }} --env PYTHONUNBUFFERED=1 eventmobi/flux:latest bash -c "./cli.sh db current || exit 1"
        docker exec database mysqldump -u root -p${{ inputs.local-mysql-root-password }} ${{ inputs.local-mysql-database }} | gzip > db_dump_80.sql.gz
        aws s3 cp db_dump_80.sql.gz s3://${{ inputs.s3-bucket }}/db_dump_80.sql.gz
