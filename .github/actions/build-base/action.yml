name: Build flux base
description: Build the flux base image
inputs:
  aws-access-key-id:
    description: 'aws access key id to be used in this run'
    required: true
  aws-secret-access-key:
      description: 'aws secret access key to be used in this run'
      required: true
  aws-region:
    description: 'aws region to be used in this run'
    required: true
  s3-bucket-amd64:
    description: 'S3 Bucket for caching amd64 docker layers'
    required: true
runs:
  using: 'composite'
  steps:
    - name: Set up docker buildx
      id: buildx
      uses: docker/setup-buildx-action@v3
      with:
        install: true
        driver-opts: network=host

    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        aws-region: ${{ inputs.aws-region }}

    - name: Build flux base image
      id: build-base-image
      uses: docker/build-push-action@v6
      env:
        DOCKER_BUILD_SUMMARY: false
      with:
        context: .
        target: base
        build-args: |
          BASE_IMAGE=eventmobi/flux-base
        tags: localhost:5000/eventmobi/flux-base
        outputs: type=docker,dest=/tmp/flux-base.tar
        cache-from: type=s3,region=us-east-1,bucket=${{ inputs.s3-bucket-amd64 }},blobs_prefix=blobs/flux/,manifests_prefix=manifests/flux/
        cache-to: type=s3,region=us-east-1,bucket=${{ inputs.s3-bucket-amd64 }},mode=max,blobs_prefix=blobs/flux/,manifests_prefix=manifests/flux/

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: flux-base
        path: /tmp/flux-base.tar
