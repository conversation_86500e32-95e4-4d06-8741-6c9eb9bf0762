name: 'Flask command'
description: 'Run flask command'
inputs:
  environment:
    description: 'Which environment is being used for this run'
    required: true
  command:
    description: 'Flask command to run'
    required: true
  aws-access-key-id:
    description: 'aws access key id to be used in this run'
    required: true
  aws-secret-access-key:
      description: 'aws secret access key to be used in this run'
      required: true
  aws-region:
    description: 'aws region to be used in this run'
    required: true
  cpu:
    description: 'cpu of container workload'
    required: true
  memory:
    description: 'memory of container workload'
    required: true
  wait-for-response:
    description: 'get task data and wait for task to finish'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Set image uri and fargate environment
      id: set-var
      env:
          FARGATE_ENV: ${{ inputs.environment }}
      run: |
        IMAGE_URI=`cat image_uri.txt`
        FARGATE_ENV=${FARGATE_ENV^}
        echo "IMAGE=$IMAGE_URI" >> $GITHUB_OUTPUT
        echo "FARGATE_ENV=$FARGATE_ENV" >> $GITHUB_OUTPUT
      shell: bash
    - name: Add CPU and Memory size
      run: |
        sed -i.bak \
        -e "s#<CPU>#${{ inputs.cpu }}#g" \
        -e "s#<MEMORY>#${{ inputs.memory }}#g" \
        task_definitions/run_one_off.json
      shell: bash
    - name: Add flask command to task definition
      run: sed -i.bak -e "s#\"ONE_OFF_TASK_COMMAND\"#${{ inputs.command }}#g" task_definitions/run_one_off.json
      shell: bash
    - name: Configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        aws-region: ${{ inputs.aws-region }}
    - name: Render aws ecs task definition for one-off container
      id: one-off-container
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task_definitions/run_one_off.json
        container-name: RunOneOff${{ steps.set-var.outputs.FARGATE_ENV }}Container
        image: ${{ steps.set-var.outputs.IMAGE }}
    - name: Deploy aws ecs one-ff task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v2
      with:
        task-definition: ${{ steps.one-off-container.outputs.task-definition }}
        cluster: ${{ inputs.environment }}-fargate
    - name: Set required variables to run one-ff task
      id: task-variables
      run: |
        echo "CLUSTER_ARN=$(aws ssm get-parameter --name /${{ inputs.environment }}/run-one-off/cluster/arn --query Parameter.Value)" >> $GITHUB_OUTPUT
        echo "SECURITY_GROUP=$(aws ssm get-parameter --name /${{ inputs.environment }}/run-one-off/security_group/ids --query Parameter.Value)" >> $GITHUB_OUTPUT
        echo "SUBNETS=$(aws ssm get-parameter --name /${{ inputs.environment }}/run-one-off/subnet/ids --query Parameter.Value)" >> $GITHUB_OUTPUT
        echo "TASK_DEFINITION_ARN=$(aws ecs describe-task-definition --task-definition RunOneOff${{ steps.set-var.outputs.FARGATE_ENV }} --query taskDefinition.taskDefinitionArn)" >> $GITHUB_OUTPUT
      shell: bash
    - name: Start one-ff task
      id: run-one-off
      run: |
        echo "TASK_ARN=$(aws ecs run-task \
            --cluster ${{ steps.task-variables.outputs.CLUSTER_ARN }} \
            --count 1 \
            --launch-type FARGATE \
            --network-configuration "{\"awsvpcConfiguration\":{\"subnets\":${{ steps.task-variables.outputs.SUBNETS }},\"securityGroups\":${{ steps.task-variables.outputs.SECURITY_GROUP }},\"assignPublicIp\":\"DISABLED\"}}" \
            --task-definition ${{ steps.task-variables.outputs.TASK_DEFINITION_ARN }} \
            --query 'tasks[0].taskArn')" >> $GITHUB_OUTPUT
      shell: bash
    - name: Get task id
      id: one-off-task
      env:
        TASK_ARN: ${{ steps.run-one-off.outputs.TASK_ARN }}
      run: |
        TASK_ID=${TASK_ARN##*/}
        echo "TASK_ID=${TASK_ID::-1}" >> $GITHUB_OUTPUT
      shell: bash
    - name: Wait for task
      if: ${{ fromJSON(inputs.wait-for-response) }}
      run: |
        aws ecs wait tasks-stopped \
            --cluster ${{ steps.task-variables.outputs.CLUSTER_ARN }} \
            --tasks ${{ steps.run-one-off.outputs.TASK_ARN }}
      shell: bash
    - name: Check errors
      if: ${{ fromJSON(inputs.wait-for-response) }}
      run: |
        RESPONSE=$(aws logs filter-log-events \
            --log-group-name /ecs/run-one-off-${{ inputs.environment }} \
            --log-stream-names ecs/RunOneOff${{ steps.set-var.outputs.FARGATE_ENV }}Container/${{ steps.one-off-task.outputs.TASK_ID }} \
            --filter-pattern "?Error" \
            --query 'events')
        if [ "$RESPONSE" != "[]" ]; then echo $RESPONSE; exit 1; fi
      shell: bash
    - name: Check command outputs
      if: ${{ fromJSON(inputs.wait-for-response) }}
      run: |
        RESPONSE=$(aws logs filter-log-events \
            --log-group-name /ecs/run-one-off-${{ inputs.environment }} \
            --log-stream-names ecs/RunOneOff${{ steps.set-var.outputs.FARGATE_ENV }}Container/${{ steps.one-off-task.outputs.TASK_ID }} \
            --filter-pattern "?CommandOutput" \
            --query 'events')
        echo $RESPONSE
      shell: bash
