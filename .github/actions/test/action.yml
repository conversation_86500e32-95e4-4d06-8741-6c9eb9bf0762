name: 'Tests'
description: 'Run tests'
inputs:
  deepsource-dsn:
    description: 'Deepsource dsn to push tests coverage'
    required: true
  unit-tests-to-run:
    description: 'Unit tests to run'
    required: true
runs:
  using: 'composite'
  steps:
    - name: Set up docker buildx
      id: buildx
      uses: docker/setup-buildx-action@v3
      with:
        install: true
        driver: docker
    - name: Download artifact
      uses: actions/download-artifact@v4
      with:
        name: flux-base
        path: /tmp
    - name: Load image
      shell: bash
      run: |
        docker load --input /tmp/flux-base.tar
    - name: Unit tests
      uses: docker/build-push-action@v6
      env:
        DOCKER_BUILD_SUMMARY: false
      with:
        context: .
        push: false
        target: unit-test
        no-cache: true
        build-args: |
          BASE_IMAGE=localhost:5000/eventmobi/flux-base
          UNIT_TESTS_TO_RUN=${{ inputs.unit-tests-to-run }}
        tags: unit-test:latest
    - name: Report test results to DeepSource
      env:
        DEEPSOURCE_DSN: ${{ inputs.deepsource-dsn }}
      continue-on-error: true
      run: |
        docker run --name unit_test unit-test && docker cp unit_test:/app/coverage.xml . && docker rm unit_test
        curl https://deepsource.io/cli | sh
        ./bin/deepsource report --analyzer test-coverage --key python --value-file ./coverage.xml
      shell: bash
