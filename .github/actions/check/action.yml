name: 'Checks'
description: 'Run required checks'

inputs:
  target-branch:
    description: 'Branch to compare against'
    required: true
    default: 'master'

runs:
  using: 'composite'
  steps:

    - name: Install uv
      uses: astral-sh/setup-uv@v5

    - name: Set up python
      uses: actions/setup-python@v4
      with:
        python-version-file: 'pyproject.toml'

    - name: Style check
      run: |
        uv tool install ruff
        changed_files=$(git diff --diff-filter=ACMRT --name-only origin/${{ inputs.target-branch }} -- '*.py')
        if [ -z "$changed_files" ]; then
          echo "No Python files changed, skipping ruff check."
        else
          echo $changed_files | xargs ruff check
        fi
      shell: bash

    - name: Set up docker buildx
      id: buildx
      uses: docker/setup-buildx-action@v3
      with:
        install: true
        driver: docker
    - name: Download artifact
      uses: actions/download-artifact@v4
      with:
        name: flux-base
        path: /tmp
    - name: Load image
      shell: bash
      run: |
        docker load --input /tmp/flux-base.tar
    - name: Type check
      uses: docker/build-push-action@v6
      env:
        DOCKER_BUILD_SUMMARY: false
      with:
        context: .
        push: false
        target: type-check
        no-cache: true
        build-args: |
          BASE_IMAGE=localhost:5000/eventmobi/flux-base
        tags: type-check
    - name: Static check
      uses: docker/build-push-action@v6
      env:
        DOCKER_BUILD_SUMMARY: false
      with:
        context: .
        push: false
        target: static-check
        no-cache: true
        build-args: |
          BASE_IMAGE=localhost:5000/eventmobi/flux-base
        tags: static-check
      continue-on-error: true
    - name: Migration check
      uses: docker/build-push-action@v6
      env:
        DOCKER_BUILD_SUMMARY: false
      with:
        context: .
        push: false
        target: migration-test
        no-cache: true
        build-args: |
          BASE_IMAGE=localhost:5000/eventmobi/flux-base
        tags: migration-test
    - name: Build nginx test image
      id: build-nginx-test-image
      uses: docker/build-push-action@v6
      env:
        DOCKER_BUILD_SUMMARY: false
      with:
        context: nginx
        push: false
        load: true
        tags: nginx-local:latest
    - name: Nginx check
      run: docker run --rm -t -a stdout --name check-nginx --env-file nginx/env.check nginx-local:latest nginx -c /etc/nginx/nginx.conf -t
      shell: bash
