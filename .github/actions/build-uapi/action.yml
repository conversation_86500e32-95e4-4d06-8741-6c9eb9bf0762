name: B<PERSON> & Push UAPI
description: Generates Redoc documentation and uploads to S3

inputs:
  spec:
    description: The specification name to generate documentation for
    required: true
  s3_prefix:
    description: S3 prefix path where the documentation should be uploaded
    required: true
  s3_bucket:
    description: S3 bucket name where the documentation should be uploaded
    required: true

runs:
  using: "composite"
  steps:
    - name: Generate API Documentation
      shell: bash
      run: make redoc_${{ inputs.spec }}

    - name: Upload to S3
      shell: bash
      run: |
        aws s3 cp output/index.html s3://${{ inputs.s3_bucket }}/${{ inputs.s3_prefix }}/index.html
