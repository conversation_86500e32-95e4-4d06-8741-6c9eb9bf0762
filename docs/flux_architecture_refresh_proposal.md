# Flux Architecture Refresh Proposal

Scratchpad: https://docs.google.com/drawings/d/1puIEPW7SaeYhRuz7og13H5k7ND8luaJHd1xc9GPE86U/edit

# Context

## Historical References

Not necessary to read, but provides some existing context on similar past thinking around domains & technical vision:

- [**Domain Driven Design & Domain Model**](https://www.notion.so/Domain-Driven-Design-Domain-Model-4901dfccbc9948669ec4dbd68a72ddd0?pvs=21)
- [Foundation: Technical Vision](https://www.notion.so/Foundation-Technical-Vision-6996309c509b4d1fbfadf0da1055e837?pvs=21)

## Challenges

- Circular Dependencies - The approach we’re taking for dependency injection is prone to difficult to solve circular dependency issues
- Unclear Domain Boundaries - Subdivision of modules or domains within Flux exists, but at times is not very clearly defined or mapped out. Likewise we have loose rules for communication across these boundaries, but they should be more clearly defined and enforced.
- Inconsistent Code Organization - Some adapters are within “domains”, some aren’t. Endpoints are separate from domains, which may or may not be appropriate. Domains are organized by narrow slices which makes reuse and cohesion within the domain difficult.
- Endpoint to Entity Coupling - Use cases return entities directly, which is discouraged in Clean Architecture (vs using a DTO). Relatedly, Endpoints don’t define their own API level contracts, meaning the API format is tightly coupled to the Entity layer. This is convenient at times, but also poses challenges. Primarily, it could make it difficult to evolve entities while maintaining backward compatibility on old endpoint versions, leading to legacy code reaching deep into the domain layer where we want it least. It also makes it difficult to have any sort of auto-generated API docs.
- Usecase to Usecase Authz - External calls and internal calls frequently use the same use cases, which have within them granular Authz checks. The problem is that the Use Case doesn’t necessarily have the context to enforce specific authorization rules for all the possible cases its being called in. This has increasingly frequently lead to us using “internal context” when calling use cases, which has its own set of issues.
- Usecase Interface Pain Points - Use Case interface definitions have a couple pain points. It takes a few layers to step into them, which can be annoying when debugging. They also don’t work nicely with type hints, or direct code references. While these are somewhat minor things, it adds up when it’s the primary internal interface for calling code within the codebase.
- Missing Components? - There may be some missing components or patterns within our standard architecture. Namely some sort of Controller that sits above the Use Case, and some sort of Domain Service which coordinates complex interaction between entities.

## Goals

- Improve Dependency Injection Strategy - Find a cleaner approach to Dependency Injection, wiring Dependencies up at application startup instead of during module import, to avoid coupling and circular dependencies. Evaluate open source Dependency Injection libraries vs a native python solution.
- Refresh & Refine Architecture Vision - Taking into account where we’ve come from, and where we want to go, refresh our technical vision and refine our architecture patterns. This should be evolution not revolution, setting a new clearly defined goal post for the new few years of development, but without the need for major code rewrites.
- Map Subdomain Boundaries & Constraints - Establishing more clearly what these key subdomains are, what their relationship and communication with each other is, and what limitations must exist to keep them decoupled will aid the team in building and maintaining a clean modular monolith.
- File Organization Restructure - Reorganize the code files in a way that is consistent and clearly represents the domain architecture of our system. Promote discoverability, and high cohesion with low coupling.
- Better Define UAPI relationship to Domains - Establish organizational structure for UAPI now that it’s getting quite large, and determine what this structure’s relationship, communication patterns, and rules are when communicating with the Domain layer.

# Proposal

## Terms

### **Bounded Context**

A Bounded Context is an explicit boundary within which a domain model applies, with its own ubiquitous language and consistent meaning. In our system, we maintain a single bounded context focused on Event Conferences, where terms like "Session," "Attendee," and "Organization" have consistent meanings across our products.

### **Domain**

A domain is the overall subject area our software addresses - Event Conferences. This encompasses all the business activities, rules, processes, and knowledge needed to support conference events from creation through execution. It defines the complete problem space we're solving.

### **Subdomain**

A subdomain is a partitioned segment of our domain that focuses on a specific business capability. We've divided our Event Conferences domain into multiple subdomains that fall into several categories:

- **Knowledge Subdomains**: Critical business areas providing competitive advantage (e.g., Events)
- **Platform Subdomains**: Generic or shared stand-alone technical platform capabilities, with very limited knowledge or dependency on other Subdomains (e.g., Email, Payments)
- **Application Subdomains**: Areas that contain application-specific functionality and metadata that doesn’t belong in the more pure model of the Core Subdomains

## Overview & Terms

We presently have a single bounded context for Event Conferences, with multiple subdomains that model different aspects of this business domain. Our architecture follows Clean Architecture principles within each subdomain, while maintaining clear communication patterns between subdomains.

In terms of the Technical Implementation of this Architecture, it is developed and deployed as a single python project as a Modular Monolith. Each Subdomain (as well as some additional application / shared components) is it’s own “Module”, which should maintain clear boundaries from each other, with communication regulated through interfaces. This is a pragmatic approach which aims to strike a balance between low-coupling, without the overhead and added complexity of Microservices. Modules/Subdomains should be large enough to have high-cohesion internally, capturing a complete business area or technical capability. 

## Rules

- Subdomains should have high internal cohesion and loose external coupling
- Dependencies between subdomains should be unidirectional - preventing circular dependencies
- Communication between subdomains happens through interfaces at the adapter layer
- Each subdomain follows Clean Architecture with its own entities, use cases, and adapters
- Subdomains should not directly access databases or repositories from other subdomains
- Subdomains can provide role-specific use cases when necessary
- API endpoints are organized by role context rather than by subdomain
- Entities should not be directly exposed to the API layer - DTOs should be used instead
- Authorization logic should be contextual to the API layer, not embedded deeply in use cases

## Subdomains

Our subdomains are organized into three categories:

### Knowledge Subdomains

These represent the core of our business and value-added functionality. They should model our Domain are purely as possible, representing the major entities, relationships, invariants, and business rules within it.

Rules:

- Should not depend on Application Subdomains

Existing Knowledge Subdomains:

- **Events**: Core subdomain containing conference information, attendees, sessions, exhibitors
- **Organization**: Manages the administration and organization of events, contracts, and credits. Also has organization wide features like custom domains, email domains, payment processors, etc.
- **Registration**: Handles registration processes, orders, and promo codes
- **Survey**: Manages feedback collection and analysis
- **Gamification**: Handles engagement mechanics and rewards

### Platform Subdomains

Represent generic reusable technical capabilities within the system, with limited knowledge of the broader Domain. Think of them similar to something like a 3rd party service that you can register information into, but has no direct knowledge or dependencies back to it’s consumers.

Rules: 

- Should not depend on other domains (with the possible exception of some other Platform Subdomains)

Existing Platform Subdomains:

- **Configuration**: Manages system-wide settings and feature flags
- **Email**: Handles email communication and templates
- **File**: Manages file storage, retrieval, and processing
- Payment: Generic functionality to manage Payment Orders and Transactions and communication to 3rd party Payment Processors
- **Identity**: Handles authentication, authorization, and user identity

### Application Subdomains

Individual applications often need supporting metadata / concepts that don’t belong within the Core Knowledge Domains. For instance, “Sections” are an important aspect of the Event App, but they aren’t something you can physically find or interact with at a Conference. It’s a supporting structure for the software application itself.

Rules:

- Should have limited to no internal dependents, primarily is accessed by the API layer
- May have many dependancies, such as on Knowledge & Platform Domains

Existing Platform Subdomains:

- **Event App**: Contains metadata specific to the Event App experience

## UAPI

UAPI is organized into role based APIs. To pragmatically reuse endpoints, each of these APIs is an extension of the “core” API. Within each of these APIs endpoints may follow a lose subdomain organization, but aren’t bound by that.

**APIs**:

- core - Used by all products (at least for now), common endpoints live here
- attendee - Primarily used by Event App, Registration, and Attendee Portal
- organizer - Primarily used by Exp manager and maybe Onsite App
- staff - Primarily used by Exp Manager and internal tools

Example:

```json
Core API:
- /events/<id>/people       # Access restricted for attendees
- /events/<id>/sessions     # Access restricted for attendees
- /events/<id>/companies    # Accessible to all roles

Attendee API:
- /attendee/events/<id>/people    # Attendee-appropriate version
- /attendee/events/<id>/sessions  # Attendee-appropriate version
```

**How to handle shared endpoints:**

Based on our starting point (most APIs being in a shared core context) and to avoid too much duplication, we will likely need some form of shared “core” APIs. There can be a couple different ways to handle this however:

1. Core API as a concept made clear to API consumers
    1. We present each API to consumers, but also make it clear there is a Core API that handles multiple roles simultaneously. This is probably simplest and closest to the truth, but it also may be confusing and not-helpful for API consumers
2. Core API is an implementation detail not exposed to API consumers
    1. Documentation is totally separate for each API, and the documentation does duplicate endpoint info for shared endpoints
    2. E.g. Attendees API may have a `/attendee/events/<id>/companies` endpoint, which internally just goes to the shared implementation (perhaps just through a whitelist/mapping)

**Endpoints:**

Endpoints are the entrypoints to the API, including information such as routing, method and version. They handle and interface with low-level http and RESTful details as needed. There may be multiple similar implementations of the same endpoint to support UAPI versions.

**Controllers:**

Controllers serve as intermediaries between endpoints and the Subdomain layer. They should not have as much dependency on technical details as the Endpoints, but still have knowledge that they are serving a RESTful API. They are responsible for translating API concepts to internal Domain representations and vice-versa. They can orchestrate communication across several Subdomains when needed, allowing those Subdomains to be less coupled to each other. As part of the API layer, they exist in a context aware part of the system, designed to be used by specific roles (Attendee API vs Organizer API for instance). They handle the high level authorization checks, allowing Subdomains to be less concerned with context-aware authorization logic. Similarly, they can handle certain Application Business Rules.

These controllers will call Subdomain Usecases directly, sometimes from multiple subdomains in order orchestrate communication across them. They will receive back DTOs, that they will then need to map into an API appropriate data format such as an API Contract.

**Endpoint to Subdomain Communication:**

Subdomains are organized by concepts, not roles. So each Role based API may depend on multiple subdomains to fulfill its requests. Use cases within subdomains may situationally be role specific when beneficial. 

## Flux Folder Structure

- uapi-spec
- docs
- test
- flux
    - subdomains
        - knowledge
            - event
                - use_cases
                    - sessions_usecases.py
                    - people_usecases.py
                - interfaces
                    - organizers_gateway.py
                    - sessions_repo.py
                - entities
                    - sessions_entities.py
                    - people_entities.py
                - services
                    - people_schedule_service.py
                - adapters
                    - subdomain_gateways
                        - organizations
                            - organizers_gateway_impl.py
                            - organizers_processor.py
                    - db_repositories
                    - external_adapters
            - registration
            - survey
            - gamification
            - organization
        - platform
            - configuration
            - email
            - file
            - identity
        - product
            - event_app
            - registration
    - uapi
        - core
            - endpoints
                - core_people_endpoints.py
            - controllers
                - core_people_controller.py
        - attendee
            - endpoints
                - attendee_people_endpoints.py
            - controllers
                - attendee_people_controller.py
        - organizer
        - staff
    - framework
    - clients
    - application
        - startup.py
        - apps.py
        - app_config.py
        - commands
    - infrastructure
        - database
        - cache
        - logging
    - utilities

## Structure Within Subdomains

Each subdomain follows Clean Architecture principles with the following layers:

### Entity Layer

- Contains the core business objects with business rules
- No dependencies on other layers or external systems
- Represents the heart of the subdomain's model
- New Optional Pattern: Domain Services
    - These orchestrate complex interactions, modifications, or business rule validation that involves multiple entities within a Domain. Like Entities, this service should not have direct dependencies, and should generally require the required information to be injected inside of it

### Use Case Layer

- Contains application-specific business rules
- Orchestrates the flow of data to and from entities
- Enforces business rules specific to application use cases
- May be organized by role (attendee use cases, organizer use cases) when beneficial

### Adapter Layer

- Contains interfaces to external systems and other subdomains
- Implements repository interfaces defined in the use case layer
- Provides gateways to other subdomains through well-defined interfaces

### Framework Layer

- Contains code that interfaces with external frameworks and libraries
- Implements the adapter interfaces
- Handles technical concerns (database, HTTP, messaging)

## Communication Between Subdomains

### Diagram

![Screenshot 2025-04-08 at 2.32.10 PM.png](Flux%20Architecture%20Refresh%20Proposal%201c29088c1f9880318b6cd1f41ded34d5/Screenshot_2025-04-08_at_2.32.10_PM.png)

### Rules for Communication

1. Dependencies should point in one direction only
2. Lower-level subdomains can depend on higher-level ones, but not vice versa
    - Higher-level subdomains (like core domains) should not depend on lower-level ones
    - Lower-level subdomains (like product-specific domains) can depend on higher-level ones
3. Core subdomains should not depend on supporting or product subdomains
4. Communication happens through well-defined interfaces
5. When needed, subdomains can communicate via domain events

### Mechanisms for Communication

### Direct Synchronous Call through Subdomain Gateway

![Screenshot 2025-04-08 at 2.51.16 PM.png](Flux%20Architecture%20Refresh%20Proposal%201c29088c1f9880318b6cd1f41ded34d5/Screenshot_2025-04-08_at_2.51.16_PM.png)

**Definition**: One subdomain directly calls another subdomain's use cases through a well-defined interface, with responses returned immediately.

**Implementation**:

- The calling subdomain defines an interface representing the operations it needs from the target subdomain
- An adapter (Subdomain Gateway) implements this interface by calling the target subdomain's use cases
- The adapter translates between the data models of both subdomains

**Example implementation**:

```python
# In Registration subdomain
class EventPeopleGateway:
    """Interface defining what Registration needs from Events"""
    @abstractmethod
    def create_person(self, person_dto: PersonCreationDTO) -> PersonDTO:
        pass

    @abstractmethod
    def get_person(self, person_id: str) -> Optional[PersonDTO]:
        pass

# In Registration adapter layer
class EventPeopleGatewayImpl(EventPeopleGateway):
    def __init__(self, event_person_use_case: EventPersonUseCase):
        self.event_person_use_case = event_person_use_case

    def create_person(self, person_dto: PersonCreationDTO) -> PersonDTO:
				# Translate from Registration's DTO to Events' DTO
        event_person_dto = self._map_to_event_person(person_dto)
				# Call Events use case
        result = self.event_person_use_case.create_person(event_person_dto)
				# Translate back to Registration's model
        return self._map_from_event_person(result)

```

**When to use**:

- When immediate response is required
- For operations that must succeed atomically
- When the calling subdomain needs data from another subdomain to complete its work

**Considerations**:

- Creates explicit compile-time dependencies
- Dependencies must be unidirectional to avoid cycles
- Gateway interfaces should be owned by the calling subdomain
- Can increase coupling if overused

### Direct Dependency Asynchronous through Subdomain Gateway

- Likely can work through the same Subdomain Gateway’s as above
- Ideally we’d have a simple way that any Usecase can be called asynchronously without having a direct dependency on Celery and get a Job back
- These would be fire-and-forget from the caller

### Published Event

- This is a mechanism for an upstream Subdomain to be notified of a change by a downstream one.
    - For example, Events Subdomain should not call Registration Subdomain directly, but it can publish a Person Updated event, which Registration Subdomain can subscribe and take action based upon
- Utilizes existing or updated realtime / processor system
- Listeners/Processors should exist in the Adapter layer of the Subdomain that is doing the listening

### DB Layer

**Definition**: Subdomains access each other's data directly at the database level, typically for read-only operations.

**Implementation**:

- Subdomains share database schemas or have read-access to other subdomains' tables
- Usually implemented as read-only views or projections

**When to use**:

- For read-only operations where performance is critical

**Considerations**:

- Creates tight coupling at the database level
- Changes to schema can impact multiple subdomains
- Violates subdomain encapsulation
- Should generally be limited to read-only access
- Best used with database views or materialized views

## Implementation Details

### Dependency Injection Improvements

- Determine a way to wire up all Dependency Injection on application start or find a suitable dependency injection framework
- Will require SPIKE / POC

### Folder & File Restructure

- Convert a single Subdomain as a POC
- Then proceed to reorganize one Subdomain at a time
- At this point its just reorganizing files, it doesn’t require enforcing all of the rules below

### Subdomain Improvements

To address the issues with our current use case pattern:

1. **Use DTOs instead of Entities**: Use cases should return DTOs rather than entities to:
    - Decouple the API layer from entity changes
    - Allow for versioning and backward compatibility
    - Support auto-generated API documentation
2. **Context-Aware Authorization**: Move basic authorization logic up to the API layer Controllers:
    - Use cases should focus on business logic
    - API layer Controllers provide role context for authorization decisions
    - Create role-specific use cases when necessary that can be called by these Controllers
3. **Controller Pattern**: Introduce a controller layer above use cases to:
    - Handle API-specific concerns
    - Manage authorization in context
    - Transform between API models and domain models
4. **Domain Services**: Introduce domain services to:
    - Coordinate complex interactions between entities
    - Encapsulate domain logic that doesn't belong to a single entity
    - Support complex business rules that span multiple entities
5. Subdomain to Subdomain Adapters & Interfaces:
    - Introduce a new pattern of Adapter for communication between Subdomains.
    - These subdomain adapters will call the Usecases of the target Subdomain, and then translate the retrieved data (DTOs) to the internal structure needed by the calling Subdomain, likewise they can push data into the target Subdomain
    - Generally these Adapters will handle Authz if necessary for the given context
    

## Questions & Unknowns

- When to use Direct DB Access Across Subdomains?
    - For very common shared dependencies, and areas where performance is key, it may be practical to allow joins across Subdomain boundaries, ideally to a stripped down readonly view instead of the original table. Candidates for this:
        - SimplePerson
        - AssociatedImage