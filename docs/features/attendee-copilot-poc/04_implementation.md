# Implementation Plan: Attendee Co-Pilot (PoC)

## Implementation Strategy

This implementation plan follows an iterative, test-driven approach where each milestone delivers working functionality that can be demonstrated and tested. We'll start with the core capabilities and progressively enhance them, ensuring we have testable components at each step.

## Milestone 1: Basic API Setup & Feature Flag

### Task 1.1: Create Feature Flag
- [x] Add event-level feature flag `ai_copilot_enabled` to the configuration system
- [x] Implement feature flag check logic in a utility function

### Task 1.2: Create API Endpoint Structure
- [x] Set up basic endpoint files and routes:
  ```
  flux/uapi/attendee/endpoints/attendee_ai_endpoints.py
  ```
- [x] Implement endpoint routes with mock responses:
  - `GET /attendee/events/<event_id>/ai/conversations` - Return empty array
  - `POST /attendee/events/<event_id>/ai/conversations` - Return mock conversation
- [x] Add request validation for required parameters
- [x] Write tests validating basic endpoint functionality

### Task 1.3: Create Minimal Controller with Feature Flag
- [x] Create essential directories for the GenAI subdomain:
  ```
  flux/subdomains/application/genai/
  ```
- [x] Create initial controller with feature flag check:
  ```
  flux/uapi/attendee/controllers/attendee_ai_controller.py
  ```
- [x] Implement feature flag check to reject requests if disabled
- [x] Connect endpoints to controller (still using mocked data)
- [x] Test controller with feature flag enabled/disabled

## Milestone 2: Core LLM Integration

### Task 2.1: Basic LLM Integration
- [x] Set up AWS Bedrock credentials and connection
- [x] Create simple LLM wrapper class:
  ```
  flux/subdomains/application/genai/adapters/external_adapters/llm_service_bedrock_impl.py
  ```
- [x] Implement basic prompting without conversation memory
- [x] Update controller to use real LLM responses instead of mocks
- [x] Add tests with stubbed LLM responses

### Task 2.2: Add LangChain Framework
- [ ] Integrate LangChain for improved prompt management
- [ ] Set up system prompt templates for attendee assistance
- [ ] Update LLM service to use LangChain
- [ ] Test with various user queries

### Task 2.3: Add Conversation Context
- [x] Implement in-memory conversation tracking (no DB yet)
- [x] Update controller to maintain conversation context between requests
- [x] Test multi-turn conversation capabilities

## Milestone 3: Tool Integration

### Task 3.1: Event Sessions Tool
- [x] Create session data gateway interface:
  ```
  flux/subdomains/application/genai/interfaces/session_data_gateway.py
  ```
- [x] Implement stub implementation with mock data
- [ ] Create LangChain tool using this gateway
- [ ] Update LLM service to use tool for session queries
- [ ] Test session queries like "What sessions are tomorrow?"

### Task 3.2: Schedule Management Tool
- [x] Create schedule gateway interface
- [x] Implement stub implementation with mock data
- [ ] Create LangChain tool for schedule management
- [ ] Test schedule operations like "Add session X to my schedule"

### Task 3.3: Connect to Real Data
- [x] Implement real gateway to event sessions data:
  ```
  flux/subdomains/application/genai/adapters/subdomain_gateways/events/session_data_gateway_impl.py
  ```
- [x] Implement real gateway to attendee schedules
- [ ] Test with real event data
- [ ] Validate correct answers to session queries

## Milestone 4: Persistence & Complete Flow

### Task 4.1: Add Domain Entities
- [x] Create conversation and message entities:
  ```
  flux/subdomains/application/genai/entities/copilot_entities.py
  ```
- [x] Implement use cases for conversation management:
  ```
  flux/subdomains/application/genai/use_cases/copilot_usecases.py
  ```
- [x] Define required DTOs for data transfer

### Task 4.2: Add Database Persistence
- [x] Create database models for conversations and messages
- [x] Implement repository for data access
- [x] Update controller to use database persistence
- [x] Test conversation history retrieval

### Task 4.3: Complete Messaging Flow
- [x] Implement remaining endpoints:
  - `GET /attendee/events/<event_id>/ai/conversations/<conversation_id>`
  - `GET /attendee/events/<event_id>/ai/conversations/<conversation_id>/messages`
  - `POST /attendee/events/<event_id>/ai/conversations/<conversation_id>/messages`
  - `POST /attendee/events/<event_id>/ai/conversations/<conversation_id>/messages/<message_id>/referenced_entities`
- [x] Connect full request/response flow with in-memory persistence
- [x] Add comprehensive tests for all endpoints

## Milestone 5: Enhancements (Optional)

### Task 5.1: Streaming Responses
- [x] Update LLM service to support streaming
- [x] Modify controller and endpoint for streaming
- [x] Test streaming performance

### Task 5.2: RAG Capabilities
- [ ] Create document repository for event data
- [ ] Implement embedding and retrieval
- [ ] Add RAG to LLM service
- [ ] Test enhanced contextual responses

### Task 5.3: Analytics & Monitoring
- [ ] Add request/response logging
- [ ] Implement usage tracking
- [ ] Create monitoring dashboard
- [ ] Test logging and metrics collection

## Key Components Structure

```
# API Layer
flux/uapi/attendee/endpoints/attendee_ai_endpoints.py
  - REST endpoints for conversations and messages
  - Request validation and parameter checking

flux/uapi/attendee/controllers/attendee_ai_controller.py
  - Authorization logic
  - Feature flag enforcement
  - Orchestration between API and domain layer

# Domain Layer
flux/subdomains/application/genai/
  - entities/
    - conversation.py
    - message.py
  - use_cases/
    - conversation_usecases.py
    - message_usecases.py
  - interfaces/
    - llm_service.py
    - session_data_gateway.py
    - schedule_gateway.py
  - services/
    - copilot_service.py

# Adapter Layer
flux/subdomains/application/genai/adapters/
  - repositories/
    - conversation_repository.py
  - subdomain_gateways/
    - events/
      - session_data_gateway_impl.py
      - schedule_gateway_impl.py
  - external_adapters/
    - bedrock/
      - llm_service_impl.py
```

## Testing Strategy

Each milestone should include:
- Unit tests for components
- Integration tests for end-to-end flow
- Mocks for external dependencies
- Feature flag validation

The implementation should follow a "working software at each step" approach, where even the early milestones produce functional (if limited) results that can be demonstrated and tested.
