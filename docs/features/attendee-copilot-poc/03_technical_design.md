# Technical Design: Attendee Co-Pilot (PoC)

## Domain Model Changes

### New Models/Entities

#### Core Persistent Entities

##### 1. `Conversation` Entity
**Purpose:** Represents a complete conversation session between an attendee and the AI assistant.

**Key attributes:**
- `id`: Unique identifier
- `event_id`: The event this conversation relates to
- `attendee_id`: The attendee who owns this conversation
- `created_at`: When the conversation started
- `updated_at`: When the conversation was last active
- `title`: Optional auto-generated title (could be generated by the AI based on content)

**Why needed:** 
- Provides a container for organizing related messages
- Enables retrieving conversation history
- Allows tracking of conversation-level metadata
- Maps directly to our API resource structure

##### 2. `Message` Entity
**Purpose:** Represents individual utterances within a conversation.

**Key attributes:**
- `id`: Unique identifier
- `conversation_id`: The conversation this message belongs to
- `role`: Whether it's from the 'user' or 'assistant'
- `content`: The actual message text
- `created_at`: Timestamp of the message
- `metadata`: Additional structured data (for AI responses)

**Why needed:**
- Stores the actual conversation content
- Tracks the sequence of interactions
- Contains metadata for AI responses (referenced sessions, suggested actions)
- Maps directly to our API message resources

#### Internal Processing Entities

These entities wouldn't necessarily need persistence but represent important domain concepts:

##### 3. `Query` Entity (Internal)
**Purpose:** Represents a processed user query with identified intent and extracted parameters.

**Key attributes:**
- `raw_text`: The original user message
- `intent`: Identified purpose (e.g., "SESSION_SEARCH", "SCHEDULE_ADD")
- `parameters`: Extracted structured data (dates, topics, session IDs)

**Why needed:**
- Decouples intent recognition from response generation
- Makes the query processing pipeline more testable
- Provides clear structure for tool selection logic

##### 4. `Response` Entity (Internal)
**Purpose:** Represents a structured response before formatting for delivery.

**Key attributes:**
- `content`: The main text response
- `referenced_entities`: Related domain objects (sessions, speakers)
- `suggested_actions`: Possible next actions for the user

**Why needed:**
- Separates response generation logic from delivery format
- Allows for structured data to be included with responses
- Makes testing easier by separating concerns

#### Entity Relationships
```
Conversation (1) ───► Messages (Many)
       │                   │
       │                   │
       ▼                   ▼
  (Optional)           (Process using)
       │                   │
       │                   │
       ▼                   ▼
   Analytics           Query/Response
```

### Data Transfer Objects (DTOs)
- **`ConversationDTO`**: For transferring conversation data between layers
- **`MessageDTO`**: For transferring message data between layers
- **`QueryDTO`**: For passing query information to use cases
- **`ResponseDTO`**: For returning structured responses from use cases
- **`SessionInfoDTO`**: For transferring session data from event subdomain
- **`ScheduleItemDTO`**: For transferring schedule data from event subdomain

### Database Schema Changes
- New tables for conversations and messages
- JSON columns for storing metadata, referenced entities, and suggested actions
- Foreign key relationships to events and attendees

### Database vs. Entity vs. API Considerations
- **Database Tables** might be more normalized than entities
  - We might have separate tables for metadata, referenced_sessions, etc.
  - Metadata might be stored as JSON or in related tables depending on query needs

- **API Resources** are aligned with the primary entities (Conversation, Message)
  - The API exposes a clean RESTful hierarchy
  - Internal processing entities (Query, Response) are not directly exposed

- **Domain Entities** represent the conceptual model
  - Focus on behavior and business rules
  - May include methods beyond simple data storage

## Component Design

### Subdomain Structure
- New Application Subdomain: `GenAI`
  - Contains AI-related components that can be shared across multiple AI features

### Domain Layer Components
- **`CopilotService`**: Domain service for coordinating complex operations
- **`QueryProcessor`**: Process and understand user queries
- **`ResponseGenerator`**: Generate natural language responses

### Use Case Layer Components
- **`CopilotUseCase`**: Core use case for handling chat interactions
- **`ConversationHistoryUseCase`**: Manage conversation context

### Interface Layer Components
- **`SessionDataGateway`**: Interface for accessing event session data
- **`ScheduleGateway`**: Interface for accessing attendee schedules
- **`LLMService`**: Interface for LLM communication

### Adapter Layer Components
- **Subdomain Gateways**:
  - **`SessionDataGatewayImpl`**: Implementation of session data access
  - **`ScheduleGatewayImpl`**: Implementation of schedule management
- **External Adapters**:
  - **`LLMServiceImpl`**: Implementation of LLM service using AWS Bedrock

### API Layer Components
- **Controllers**:
  - **`AttendeeCopilotController`**: Handle authorization and orchestration
- **Endpoints**:
  - **`AttendeeCopilotEndpoint`**: REST endpoint for chat interactions

## Component Relationships
```
API Layer:
AttendeeCopilotEndpoint -> AttendeeCopilotController

Domain Layer:
AttendeeCopilotController -> CopilotUseCase -> CopilotService
                                            -> Interfaces (SessionDataGateway, ScheduleGateway, LLMService)

Adapter Layer:
SessionDataGateway <- SessionDataGatewayImpl -> [Events Subdomain]
ScheduleGateway <- ScheduleGatewayImpl -> [Events Subdomain]
LLMService <- LLMServiceImpl -> AWS Bedrock
```

## API Design
- **REST endpoints**:
  - `GET /attendee/events/<event_id>/ai/conversations`: Retrieve all conversations for the current attendee
  - `POST /attendee/events/<event_id>/ai/conversations`: Create a new conversation with optional initial message
  - `GET /attendee/events/<event_id>/ai/conversations/<conversation_id>`: Retrieve a specific conversation
  - `GET /attendee/events/<event_id>/ai/conversations/<conversation_id>/messages`: Get all messages for a conversation
  - `POST /attendee/events/<event_id>/ai/conversations/<conversation_id>/messages`: Add a message to a conversation

- **Request/response formats**: 
  ```json
  // POST /attendee/events/<event_id>/ai/conversations
  // Request:
  {
    "message": "What sessions are happening tomorrow?"
  }
  
  // Response:
  {
    "id": "conv-123",
    "event_id": "event-456",
    "created_at": "2023-05-01T12:00:00Z", 
    "updated_at": "2023-05-01T12:00:00Z",
    "messages": [
      {
        "id": "msg-789",
        "role": "user",
        "content": "What sessions are happening tomorrow?",
        "created_at": "2023-05-01T12:00:00Z"
      },
      {
        "id": "msg-790",
        "role": "assistant",
        "content": "Tomorrow you have 3 sessions...",
        "created_at": "2023-05-01T12:00:05Z",
        "metadata": {
          "referenced_sessions": [
            {"id": "session-001", "title": "AI Workshop", "time": "10:00 AM"}
          ],
          "suggested_actions": [
            {"type": "add_to_schedule", "payload": {"session_id": "session-001"}}
          ]
        }
      }
    ]
  }
  
  // POST /attendee/events/<event_id>/ai/conversations/<conversation_id>/messages
  // Request:
  {
    "content": "Add the AI Workshop to my schedule"
  }
  
  // Response:
  {
    "id": "msg-791",
    "conversation_id": "conv-123",
    "role": "user",
    "content": "Add the AI Workshop to my schedule",
    "created_at": "2023-05-01T12:05:00Z"
  }
  
  // GET /attendee/events/<event_id>/ai/conversations
  // Response:
  {
    "data": [
      {
        "id": "conv-123",
        "event_id": "event-456",
        "created_at": "2023-05-01T12:00:00Z",
        "updated_at": "2023-05-01T12:05:10Z",
        "last_message": {
          "content": "I've added the AI Workshop to your schedule.",
          "created_at": "2023-05-01T12:05:10Z"
        },
        "message_count": 4
      },
      // Additional conversations...
    ],
    "meta": {
      "total_count": 5,
      "limit": 10,
      "offset": 0
    }
  }
  ```

- **Authentication/authorization**: Standard API auth with attendee and event context

## Data Flow

### Chat Request Processing
```mermaid
sequenceDiagram
  participant Client
  participant Endpoint
  participant Controller
  participant UseCase
  participant Service
  participant LLMAdapter
  participant SessionGateway
  participant EventsSubdomain
  
  Client->>Endpoint: POST /attendee/events/<id>/ai/conversations/<conv_id>/messages
  Endpoint->>Controller: processMessage(event_id, conv_id, content)
  Controller->>Controller: validateAndAuthorize(user_id, event_id)
  Controller->>UseCase: handleChatMessage(queryDTO)
  
  UseCase->>Service: processQuery(query)
  Service->>LLMAdapter: determineIntent(query)
  LLMAdapter-->>Service: intent + parameters
  
  alt Session Query Intent
    Service->>SessionGateway: getSessions(parameters)
    SessionGateway->>EventsSubdomain: getSessionsUseCase.execute(params)
    EventsSubdomain-->>SessionGateway: sessionEntities
    SessionGateway-->>Service: sessionDTOs
    Service->>LLMAdapter: generateResponse(intent, sessionDTOs)
  else Schedule Management Intent
    Service->>ScheduleGateway: [schedule operations]
    ScheduleGateway->>EventsSubdomain: [schedule usecase calls]
    EventsSubdomain-->>ScheduleGateway: result
    ScheduleGateway-->>Service: scheduleDTO
    Service->>LLMAdapter: generateResponse(intent, scheduleDTO)
  else Direct Response
    Service->>LLMAdapter: generateDirectResponse(query)
  end
  
  LLMAdapter-->>Service: generatedResponse
  Service-->>UseCase: responseDTO
  UseCase-->>Controller: responseDTO
  Controller-->>Endpoint: formatted API response
  Endpoint-->>Client: HTTP Response
```

## Tech Stack
- **AWS Bedrock** for LLM hosting
- **LangChain Python** for orchestration and tool calling
- **Flask** for API endpoints (via existing Flux infrastructure)
- **Gunicorn** with gthread worker class for potential streaming
- **SQLAlchemy** for data persistence (via existing Flux infrastructure)

## Design Patterns
- **Clean Architecture**: Separation of concerns with distinct layers
- **Dependency Inversion**: Using interfaces for dependencies
- **Repository Pattern**: For data access abstraction
- **Gateway Pattern**: For cross-subdomain communication
- **Strategy Pattern**: For different AI processing strategies
- **Factory Pattern**: For creating complex objects like LLM agents 