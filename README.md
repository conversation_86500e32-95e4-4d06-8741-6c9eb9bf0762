# Flux

[![Pull Request](https://github.com/EventMobi/Flux/actions/workflows/pull_request.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/pull_request.yml)
[![Stage](https://github.com/EventMobi/Flux/actions/workflows/stage.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/stage.yml)
[![Production](https://github.com/EventMobi/Flux/actions/workflows/production.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/production.yml)
[![Run Flask Command](https://github.com/EventMobi/Flux/actions/workflows/run_one_off.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/run_one_off.yml)
[![Rollback](https://github.com/EventMobi/Flux/actions/workflows/rollback.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/rollback.yml)
[![Stage DB Migration](https://github.com/EventMobi/Flux/actions/workflows/cron_stage_db.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/cron_stage_db.yml)
[![Deploy Cron Server](https://github.com/EventMobi/Flux/actions/workflows/cron_server.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/cron_server.yml)
[![Manual Deployment](https://github.com/EventMobi/Flux/actions/workflows/manual.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/manual.yml)
[![Configuror](https://github.com/EventMobi/Flux/actions/workflows/config.yml/badge.svg)](https://github.com/EventMobi/Flux/actions/workflows/config.yml)


A Python API for access to all EventMobi services built ontop of [Flask](http://flask.pocoo.org/).

## Setup Development Environment

Flux uses python 3.+ (_currently 3.6_). One recommended way for installing
any given version of Python independently of your OS is to use [PyEnv](https://github.com/pyenv/pyenv).

After installing the right version of Python3 using pyenv you can use `make`
to setup your virtual environment:

    PYTHON3=/<your home directory>/.pyenv/versions/<python version>/bin/python make virtualenv

This command will create and configure a virtualenv in the `$FLUX_ROOT/venv` folder.
To activate your virtual environment, from `$FLUX_ROOT` folder run

    source venv/bin/activate

### Running Tests

To run all the unit tests just run

    make fasttest

You can also do

    pytest test/

Which will also run all the static checks targets, such as `stylecheck`,
`typecheck` and `staticcheck`.

Both commands will run **ALL** the unittests, what can take a while. You can
choose which test to run by doing:

    TESTS=test.unit.namespaces.organizations.test_rest_organizations make unittest

### Setup Git Pre-Commit Hook
Foundation team enforces the [conventional commits](https://www.conventionalcommits.org/) as style 
guides for commit messages. 

We can set up these style enforcements as a git pre-commit hook in your repo to raise a warning at 
the time of commit instead of after the code has been pushed. 

To setup this pre-commit hook locally, perform the following steps:

1. Install the [pre-commit](https://pre-commit.com/) package
   </br>
   - If you are on macOS, you can install it via Homebrew or Pip.  `brew install pre-commit` or
   `pip install pre-commit` respectively.
   </br>
   - For Windows/Linux you can install via `pip install pre-commit`.
   </br>
   - For all installation options, visit pre-commit's 
   [installation page](https://pre-commit.com/#install).
2. `CD` into your repo
3. Run `pre-commit install --hook-type commit-msg`
4. Repeat step `2` and `3` for all the foundation repositories you are going to work on

You're all set!

Please note that on the first commit, it will take a couple of minutes to set up the 
pre-commit hook environment which will be reused every time afterwards.

## Migrations

### Creating a New Migration

    ./cli.sh db revision -m "Comment"

### Creating new migration with migration file content
    
    ./cli.sh db migrate -x target_tables=table1,table2 -m "Comment"

This command automatically discovers changes in model and adds these changes in the migration file
- Pleas always provide the target tables as comma separated list, if you don't provide the tables 
it might create an empty migration or migration with 1000s of lines targeting unwanted tables
- **The changes that it adds might not be 100% correct, it is always recommended to review and fix 
these changes according to preferences and followed 
practices**

### Checking Migrations status

    make migration_status

### Applying Migrations to DB

    make migrate

## Debugging

Please see [Confluence page](https://eventmobi.atlassian.net/wiki/spaces/ENG/pages/721386/Flux)

## Generating the API docs

To generate the API docs you need to have [npx](https://github.com/zkat/npx)
installed on your machine.

After ensuring you have it installed, run:

    make apidocs

## Streaming AI Responses

The Attendee Copilot endpoints support optional streaming of AI replies. Pass
`stream=true` as a query parameter when creating or replying to a conversation
to receive the response as a streamed HTTP body.

Example:

```bash
curl -N -X POST \
  "https://example.com/uapi/attendee/events/123/ai/conversations?stream=true" \
  -H "Accept: application/vnd.eventmobi.public+json;version=p.9" \
  -d '{"content": "Hello"}'
```

The conversation ID will be returned in the `X-Conversation-ID` response header.
When streaming, the AI message ID is also provided via the `X-Message-ID` header.
