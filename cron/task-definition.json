{"family": "<TASK_DEFINITION_FAMILY>", "taskRoleArn": "<TASK_DEFINITION_TASK_ROLE_ARN>", "executionRoleArn": "<TASK_DEFINITION_EXECUTION_ROLE_ARN>", "networkMode": "awsvpc", "containerDefinitions": [{"name": "<TASK_DEFINITION_CONTAINER>", "image": "", "cpu": 256, "memoryReservation": 512, "portMappings": [], "essential": true, "environment": [], "secrets": [], "startTimeout": 180, "stopTimeout": 60, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/cron-<TASK_DEFINITION_ENVIRONMENT>", "awslogs-stream-prefix": "ecs", "awslogs-region": "<AWS_REGION>"}}}], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "tags": [{"key": "terraform", "value": "false"}, {"key": "application", "value": "cron"}]}