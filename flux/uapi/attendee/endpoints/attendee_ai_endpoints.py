"""
Attendee AI Endpoints for the Attendee Co-Pilot feature
"""
from uuid import UUID

from flux.adapters.uapi.common import ApiB<PERSON>print, PrivateVersionRange
from flux.adapters.auth import authn_methods
from flux.uapi.attendee.controllers.attendee_ai_controller import (
    AttendeeAIController,
)

attendee_ai_api = ApiBlueprint('attendee_ai', __name__)
PRIVATE_VERSION = [PrivateVersionRange(first=9)]  # Using version 9
AUTHN_ATTENDEE = (
    authn_methods.Cookie('attendee'),
)


@attendee_ai_api.endpoint(
    method='GET',
    rule='/attendee/events/<event_id>/ai/conversations/',
    versions=PRIVATE_VERSION,
    authn_methods=AUTHN_ATTENDEE,
    paginated=True,
)
def get_conversations(context, event_id, pagination=None):
    """
    Get all conversations for the current attendee in the given event

    Args:
        context: Request context
        event_id (str): The event ID
        pagination: Pagination parameters

    Returns:
        list: List of conversations
    """
    controller = AttendeeAIController(context)
    conversations = controller.get_conversations(int(event_id), pagination)
    return conversations


@attendee_ai_api.endpoint(
    method='GET',
    rule='/attendee/events/<event_id>/ai/conversations/<conversation_id>/',
    versions=PRIVATE_VERSION,
    authn_methods=AUTHN_ATTENDEE,
)
def get_conversation(context, event_id, conversation_id):
    """
    Get a specific conversation with its messages

    Args:
        context: Request context
        event_id (str): The event ID
        conversation_id (str): The conversation ID

    Returns:
        dict: The conversation object with messages or 404 if not found
    """
    controller = AttendeeAIController(context)
    conversation = controller.get_conversation(int(event_id), UUID(conversation_id))

    if not conversation:
        return None, 404

    return conversation


@attendee_ai_api.endpoint(
    method='POST',
    rule='/attendee/events/<event_id>/ai/conversations/',
    versions=PRIVATE_VERSION,
    authn_methods=AUTHN_ATTENDEE,
    success_status=201,
    streamable=True,
)
def create_conversation(context, stream_response, event_id, data, response_headers):
    """
    Create a new conversation with an initial message

    Args:
        context: Request context
        stream_response (bool): Whether to stream the response
        event_id (str): The event ID
        data (dict): Request data containing the message

    Returns:
        dict: The created conversation object with the AI response
    """
    content = data.get('content')
    controller = AttendeeAIController(context)
    if stream_response:
        conv_id, msg_id, generator = controller.send_message(int(event_id), content, stream=True)
        response_headers.append(('X-Conversation-ID', str(conv_id)))
        response_headers.append(('X-Message-ID', str(msg_id)))
        return generator
    response = controller.send_message(int(event_id), content)
    return response


@attendee_ai_api.endpoint(
    method='POST',
    rule='/attendee/events/<event_id>/ai/conversations/<conversation_id>/messages/',
    versions=PRIVATE_VERSION,
    authn_methods=AUTHN_ATTENDEE,
    streamable=True,
)
def send_message_to_conversation(context, stream_response, event_id, conversation_id, data,
                                 response_headers):
    """
    Send a message to an existing conversation

    Args:
        context: Request context
        stream_response (bool): Whether to stream the response
        event_id (str): The event ID
        conversation_id (str): The conversation ID
        data (dict): Request data containing the message

    Returns:
        dict: The updated conversation object with the AI response
    """
    content = data.get('content')
    if not content:
        return {'error': 'Content is required'}, 400

    controller = AttendeeAIController(context)
    if stream_response:
        _, msg_id, generator = controller.send_message(
            int(event_id), content, UUID(conversation_id), stream=True
        )
        response_headers.append(('X-Message-ID', str(msg_id)))
        return generator

    response = controller.send_message(int(event_id), content, UUID(conversation_id))
    return response


@attendee_ai_api.endpoint(
    method='GET',
    rule='/attendee/events/<event_id>/ai/conversations/<conversation_id>/messages/',
    versions=PRIVATE_VERSION,
    authn_methods=AUTHN_ATTENDEE,
    paginated=True,
    sortable_fields=['created_at'],
)
def get_conversation_messages(context, event_id, conversation_id, pagination=None):
    """
    Get messages for a specific conversation with pagination

    Args:
        context: Request context
        event_id (str): The event ID
        conversation_id (str): The conversation ID
        pagination: Pagination parameters (includes sorting)

    Returns:
        list: The messages in the conversation
    """
    controller = AttendeeAIController(context)
    messages = controller.get_messages(int(event_id), UUID(conversation_id), pagination)

    if not messages:
        return [], 404

    return messages


@attendee_ai_api.endpoint(
    method='GET',
    rule='/attendee/events/<event_id>/ai/conversations/<conversation_id>/messages/<message_id>/',
    versions=PRIVATE_VERSION,
    authn_methods=AUTHN_ATTENDEE,
)
def get_message(context, event_id, conversation_id, message_id):
    """Get a single message"""
    controller = AttendeeAIController(context)
    message = controller.get_message(int(event_id), UUID(conversation_id), UUID(message_id))
    if not message:
        return None, 404
    return message


@attendee_ai_api.endpoint(
    method='POST',
    rule='/attendee/events/<event_id>/ai/messages/<message_id>/referenced_entities/',
    versions=PRIVATE_VERSION,
    authn_methods=AUTHN_ATTENDEE,
)
def add_referenced_entities(context, event_id, message_id, data):
    """Add referenced entities for a specific message"""
    controller = AttendeeAIController(context)
    entities = data.get('referenced_entities', [])
    controller.add_referenced_entities(
        event_id=int(event_id),
        message_id=UUID(message_id),
        entities=entities,
    )
