"""
Controller for handling attendee AI interactions
"""
from typing import Dict, List, Optional
from uuid import UUID

from flux.domains.common.errors import AuthorizationError
from flux.subdomains.application.genai.use_cases.copilot_usecases import CopilotUseCase
from flux.subdomains.application.genai.entities.copilot_entities import Conversation, Message
from flux.subdomains.application.genai.factory import create_copilot_use_case_with_tools
from flux.domains.common.pagination import PaginationInfo
from flux.utils.feature_flags import is_ff_enabled


class AttendeeAIController:
    """
    Controller for handling attendee AI interactions
    """

    FEATURE_FLAG_KEY = 'ai_copilot_enabled'

    def __init__(self, context):
        self._context = context
        self._event_id = None
        self._copilot_use_case = None  # Lazy loaded

    def _get_copilot_use_case(self, event_id: int) -> CopilotUseCase:
        """
        Get the copilot use case instance, optionally configured for a specific event

        Args:
            event_id: Optional event ID to include in context

        Returns:
            CopilotUseCase instance
        """
        if self._copilot_use_case is None or (event_id and self._event_id != event_id):
            self._event_id = event_id
            self._copilot_use_case = create_copilot_use_case_with_tools(
                context=self._context,
                event_id=event_id,
            )
        return self._copilot_use_case

    def _get_user_id(self) -> int:
        """
        Get the user ID from the request context

        Returns:
            The user ID from context
        """
        if not self._context or not self._context.user or not self._context.user.id:
            raise AuthorizationError
        return self._context.user.id

    def check_feature_enabled(self, event_id: int) -> None:
        """
        Check if the AI feature is enabled for the event

        Args:
            event_id: The event ID

        Raises:
            AuthorizationError: If the feature is not enabled
        """
        is_enabled = is_ff_enabled(
            key=self.FEATURE_FLAG_KEY,
            resource_id=str(event_id),
            resource_type='events'
        )

        if not is_enabled:
            raise AuthorizationError

    def check_event_access(self, event_id: int) -> None:
        """
        Check if the user has access to the event

        Args:
            event_id: The event ID

        Raises:
            AuthorizationError: If the user doesn't have access to the event
        """
        if not self._context or not self._context.user:
            raise AuthorizationError("User not authenticated")

        # Assert that the user has access to the event
        self._context.user.assert_has_event_access(event_id)

    @staticmethod
    def _assert_conversation_owner(conversation, user_id):
        """
        Raises AuthorizationError if the conversation does not belong to the user.
        """
        if not conversation or getattr(conversation, 'user_id', None) != user_id:
            raise AuthorizationError("User does not own this conversation")

    def get_conversations(
        self, event_id: int, pagination: Optional[PaginationInfo] = None
    ) -> List[Dict]:
        """
        Get all conversations for the current user in an event

        Args:
            event_id: The event ID

        Returns:
            List of conversation dictionaries
        """
        user_id = self._get_user_id()
        self.check_event_access(event_id)
        self.check_feature_enabled(event_id)
        conversations = self._get_copilot_use_case(event_id).get_conversations(
            event_id,
            user_id,
            page_info=pagination,
        )
        return [self._map_conversation_to_api(conv) for conv in conversations]

    def get_conversation(self, event_id: int, conversation_id: UUID) -> Optional[Dict]:
        """
        Get a specific conversation

        Args:
            event_id: The event ID
            conversation_id: The conversation ID

        Returns:
            Conversation dictionary if found, None otherwise
        """
        user_id = self._get_user_id()
        self.check_event_access(event_id)
        self.check_feature_enabled(event_id)
        conversation = self._get_copilot_use_case(event_id).get_conversation(
            conversation_id,
            event_id,
            user_id,
        )
        if not conversation:
            return None
        self._assert_conversation_owner(conversation, user_id)
        return self._map_conversation_to_api(conversation)

    def send_message(
        self,
        event_id: int,
        content: str,
        conversation_id: Optional[UUID] = None,
        stream: bool = False,
    ):
        """
        Send a message and get a response

        Args:
            event_id: The event ID
            content: The message content to send
            conversation_id: Optional conversation ID to continue
            stream: Whether to return a streaming response

        Returns:
            Response dictionary containing the AI's response
        """
        user_id = self._get_user_id()
        self.check_event_access(event_id)
        self.check_feature_enabled(event_id)
        if conversation_id:
            conversation = self._get_copilot_use_case(event_id).get_conversation(
                conversation_id,
                event_id,
                user_id,
            )
            self._assert_conversation_owner(conversation, user_id)
        if stream:
            conv_id, msg_id, generator = (
                self._get_copilot_use_case(event_id).handle_chat_message_stream(
                    event_id=event_id,
                    user_id=user_id,
                    content=content,
                    conversation_id=conversation_id,
                )
            )
            return conv_id, msg_id, generator
        response = self._get_copilot_use_case(event_id).handle_chat_message(
            event_id=event_id,
            user_id=user_id,
            content=content,
            conversation_id=conversation_id,
        )
        return self._map_response_to_api(response)

    def get_messages(self, event_id: int, conversation_id: UUID,
                     pagination: Optional[PaginationInfo] = None) -> List[Dict]:
        """
        Get all messages for a conversation with pagination support

        Args:
            event_id: The event ID
            conversation_id: The conversation ID
            pagination: Pagination information (includes sorting)

        Returns:
            List of message dictionaries
        """
        user_id = self._get_user_id()
        self.check_event_access(event_id)
        self.check_feature_enabled(event_id)

        # Verify the conversation exists and belongs to the user
        conversation = self._get_copilot_use_case(event_id).get_conversation(
            conversation_id,
            event_id,
            user_id,
        )
        if not conversation:
            return []
        self._assert_conversation_owner(conversation, user_id)

        # Get messages with pagination
        messages = self._get_copilot_use_case(event_id).get_messages(
            conversation_id=conversation_id,
            page_info=pagination
        )

        # Map messages to API format
        return [self._map_message_to_api(msg) for msg in messages]

    def get_message(self, event_id: int, conversation_id: UUID, message_id: UUID
                    ) -> Optional[Dict]:
        """Retrieve a single message if it exists"""
        self.check_event_access(event_id)
        self.check_feature_enabled(event_id)

        message = self._get_copilot_use_case(event_id).get_message(conversation_id, message_id)
        if not message or message.conversation_id != conversation_id:
            return None

        return self._map_message_to_api(message)

    def add_referenced_entities(self, event_id: int, message_id: UUID, entities: List[dict]):
        self.check_event_access(event_id)
        self._get_copilot_use_case(event_id).add_referenced_entities(message_id, entities)

    @staticmethod
    def _map_conversation_to_api(conversation: Conversation) -> Dict:
        """
        Map a conversation entity to an API response

        Args:
            conversation: The conversation entity

        Returns:
            API response dictionary
        """
        return {
            "id": str(conversation.id),
            "event_id": conversation.event_id,
            "title": conversation.title,
            "created_at": conversation.created_at.isoformat() if conversation.created_at else None,
            "message_count": len(conversation.messages) if conversation.messages else 0
        }

    @staticmethod
    def _map_message_to_api(message: Message) -> Dict:
        """
        Map a message entity to an API response

        Args:
            message: The message entity

        Returns:
            API response dictionary
        """
        return {
            "id": str(message.id),
            "conversation_id": str(message.conversation_id),
            "role": message.role,
            "content": message.content,
            "created_at": message.created_at.isoformat() if message.created_at else None,
            "referenced_entities": message.referenced_entities,
        }

    @staticmethod
    def _map_response_to_api(response: Dict) -> Dict:
        """
        Map a response from the use case to an API response

        Args:
            response: The response from the use case

        Returns:
            API response dictionary
        """
        return {
            "title": response.get("title", None),
            "conversation_id": str(response["conversation_id"]),
            "conversation_created_at": response.get("conversation_created_at", None),
            "message_id": str(response["message_id"]) if "message_id" in response else None,
            "message_created_at": response.get("message_created_at", None),
            "content": response["content"],
            "suggested_actions": response.get("suggested_actions", []),
            "referenced_entities": response.get("referenced_entities", [])
        }
