import re
import base64
import binascii
from datetime import datetime, timedelta
import dateutil.parser
import yaml

from flask import Blueprint, Response, request as flask_request
from oauthlib.oauth2 import RequestValidator, Server

from flux.utils.dot_dict import DotDict
from flux.flux_services.common import get_internal_flux_client
from flux.flux_services.common import errors

from flux.adapters.db import db
from flux.adapters.cache import redis
from flux.adapters.logging import get_logger

from flux.domains.auth import usecases as auth_cases
from flux.domains.auth.exceptions import (
    UserNotFound,
    RefreshTokenNotFoundError,
    OAuth2ClientNotFound, AccessTokenNotFoundError)
from flux.domains.common.errors import AuthenticationError
from flux.domains.common.validation.validate import ValidationError

log = get_logger(__name__)


class AuthError(Exception):
    pass


class BearerTokenNotFound(Exception):
    pass


class BearerTokenExpired(Exception):
    pass


mod = Blueprint('oauth2', __name__, url_prefix='/oauth2')

_basic_auth_pattern = re.compile('^Basic (.*)$')


@mod.route('/token/', methods=['POST'])
def token_endpoint():
    uri, http_method, body, headers = extract_params(flask_request)
    data = {
        'uri': uri,
        'http_method': http_method,
        'body': body,
        'headers': headers,
    }
    log.debug('Token Data:', **data)
    headers, body, status = oauth2_server.create_token_response(
        uri, http_method, body, headers
    )
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        raise e
    return Response(
        response=body,
        status=status,
        headers=headers,
        content_type='application/json'
    )


def extract_params(request):
    """Extract request params."""
    # `request.url` contains URL-decoded query string which oauthlib chokes on
    # if special chars are present. Recreate a non-decoded URL by hand
    uri = request.base_url
    query_string = request.query_string.decode('utf-8')

    if query_string:
        uri = uri + '?' + query_string

    http_method = request.method
    headers = dict(request.headers)
    if 'wsgi.input' in headers:
        del headers['wsgi.input']
    if 'wsgi.errors' in headers:
        del headers['wsgi.errors']

    body = request.form.to_dict()
    return uri, http_method, body, headers


def get_authorization_code(client_id, code):
    key = redis.keys.OAUTH_AUTH_CODE.format(client_id=client_id, code=code)
    serialized_data = redis.clients.flux.get(key)
    auth_code = yaml.safe_load(serialized_data)
    return auth_code


def delete_authorization_code(client_id, code):
    key = redis.keys.OAUTH_AUTH_CODE.format(client_id=client_id, code=code)
    redis.clients.flux.delete(key)
    return True


# Docs here: http://oauthlib.readthedocs.org/en/latest/oauth2/validator.html
# Note:
# Many methods have been filled in with rough code, but currently
#   only the ones for "Resource Owner Password Credentials Grant"
#   are complete and tested
# Note:
# Consider improving performance by caching more info
#   such as the entire user within the bearer token and the
#   client in it's own hash
class EMAuthValidator(RequestValidator):
    context_instance = None

    def get_code_challenge_method(self, code, request):
        return None

    def introspect_token(self, token, token_type_hint, request, *args, **kwargs):
        raise NotImplementedError('Subclasses must implement this method.')

    def revoke_token(self, token, token_type_hint, request, *args, **kwargs):
        raise NotImplementedError('Subclasses must implement this method.')

    @property
    def context(self):
        if self.context_instance:
            return self.context_instance

        ud = get_internal_flux_client(origin='oauth2_view')
        self.context_instance = ud.context
        return self.context_instance

    def validate_client_id(self, client_id, request, *args, **kwargs):
        # Simple validity check, does client exist? Not banned?
        client = self._get_client(client_id)
        if not client or not client.active:
            return False
        request.client = client
        return True

    def validate_redirect_uri(self, client_id,
                              redirect_uri, request, *args, **kwargs):
        # Is the client allowed to use the supplied redirect_uri? i.e. has
        # the client previously registered this EXACT redirect uri.
        client = self._get_client(client_id, request)
        return redirect_uri in client.redirect_uris if client else False

    def get_default_redirect_uri(self, client_id, request, *args, **kwargs):
        # The redirect used if none has been supplied.
        # Prefer your clients to pre register a redirect uri rather than
        # supplying one on each authorization request.
        client = self._get_client(client_id, request)
        return client.default_redirect_uri if client else False

    def validate_scopes(self, client_id, scopes, client,
                        request, *args, **kwargs):
        # Is the client allowed to access the requested scopes?
        return set(scopes).issubset(client.scopes.split(' '))

    def get_default_scopes(self, client_id, request, *args, **kwargs):
        # Scopes a client will authorize for if none are supplied in the
        # authorization request.
        client = self._get_client(client_id, request)
        return client.default_scopes if client else False

    def validate_response_type(self, client_id, response_type,
                               client, request, *args, **kwargs):
        # Clients should only be allowed to use one type of response type, the
        # one associated with their one allowed grant type.
        # In this case it must be "code".
        return response_type == client.grant_type

    # Post-authorization

    def save_authorization_code(self, client_id, code,
                                request, *args, **kwargs):
        # Remember to associate it with request.scopes, request.redirect_uri
        # request.client, request.state and request.user (the last is passed in
        # post_authorization credentials, i.e. { 'user': request.user}.
        client = self._get_client(client_id, request)
        if not client:
            return False
        #TODO: save auth code
        #save_authorization_code()
        return client.default_redirect_uri

    # Token request

    def authenticate_client(self, request, *args, **kwargs):
        try:
            # Whichever authentication method suits you, HTTP Basic might work

            # get client_id and secret
            # get Authorization header with http basic auth
            data = request.headers.get('Authorization')
            if not data:
                raise AuthError('Missing Authorization header')

            # ensure basic auth pattern & extract base64 encoded data
            m = _basic_auth_pattern.match(data)
            if not m:
                raise AuthError('Failed to parse auth data')
            base64data = m.group(1)

            # convert python string to byte string
            byte_data = base64data.encode()

            # decode base64 into byte string, return false if incorrect format
            try:
                decoded_data = base64.decodebytes(byte_data)
            except binascii.Error:
                raise AuthError('Failed to decode client data')

            # convert back to python string from byte string
            str_data = decoded_data.decode()

            # unpack data into client_id and client_secret
            try:
                client_id, client_secret = str_data.split(':')
            except ValueError:
                raise AuthError('Invalid client data')

            # get client and authenticate credentials
            client = self._get_client(client_id, request)
            if not client or client.secret != client_secret:
                raise AuthError('Invalid client secret')

            # save client to request and return
            request.client = client

        except AuthError:
            # TODO: Can't raise exception since that will break the interface
            #       but we have no way of logging the issue.
            return False
        return True

    def authenticate_client_id(self, client_id, request, *args, **kwargs):
        # Don't allow public (non-authenticated) clients
        return False

    def validate_code(self, client_id, code, client, request, *args, **kwargs):
        # Validate the code belongs to the client. Add associated scopes,
        # state and user to request.scopes, request.state and request.user.
        auth_code = get_authorization_code(client_id, code)
        if not auth_code:
            return False
        request.scopes = auth_code.scopes
        request.state = None  # WHAT DO WE PUT HERE?
        request.user = auth_code.user
        return True

    def confirm_redirect_uri(self, client_id, code, redirect_uri,
                             client, request, *args, **kwargs):
        # You did save the redirect uri with the authorization code right?
        auth_code = get_authorization_code(client_id, code)
        return auth_code.redirect_uri == redirect_uri

    def validate_grant_type(self, client_id, grant_type, client,
                            request, *args, **kwargs):
        # Clients should only be allowed to use one type of grant.
        # Refresh token is treated seperately
        return client.grant_type == grant_type or (
            grant_type == 'refresh_token' and
            client.allow_refresh
        )

    def save_bearer_token(self, bearer_token, request, *args, **kwargs):
        # Remember to associate it with request.scopes, request.user and
        # request.client. The two former will be set when you validate
        # the authorization code. Don't forget to save both the
        # access_token and the refresh_token and set expiration for the
        # access_token to now + expires_in seconds.

        # client_credentials has no user
        if request.client.grant_type == 'client_credentials':
            user_id = None
        else:
            user_id = request.user.id

        # determine token scopes - This is the union of user available scopes
        #   and client default scopes, with non-requested scopes and scopes
        #   not available to the client excluded.
        u_as = set()
        if request.user and request.user.available_scopes:
            u_as = set(request.user.available_scopes)
        r_s = set(request.scopes)
        c_ds = set(request.client.default_scopes.split(' '))
        c_s = set(request.client.scopes.split(' '))
        scopes = (u_as | c_ds) & c_s & r_s
        scopes = ' '.join(scopes)

        expires_at = datetime.utcnow() + timedelta(
            seconds=bearer_token['expires_in']
        )

        refresh_token = bearer_token.get('refresh_token')
        access_token = bearer_token['access_token']

        bearer_token_data = {
            'refresh_token': refresh_token,
            'access_token': access_token,
            'client_id': request.client.id,
            'user_id': user_id,
            'expires_at': expires_at,
            'scopes': scopes,
        }

        # Update existing bearer token by refresh token if possible, otherwise
        # create a new bearer token.
        if refresh_token:
            try:
                auth_cases.refresh_bearer_token(
                    self.context,
                    refresh_token=refresh_token,
                    access_token=access_token,
                )
            except RefreshTokenNotFoundError:
                self._create_bearer_token(bearer_token_data)
        else:
            self._create_bearer_token(bearer_token_data)

        # update bearer_token for response
        bearer_token['scope'] = scopes
        bearer_token['client_id'] = request.client.id
        bearer_token['user_id'] = user_id
        return request.client.default_redirect_uri

    def _create_bearer_token(self, data):
        auth_cases.create_bearer_token(
            self.context,
            refresh_token=data['refresh_token'],
            access_token=data['access_token'],
            client_id=data['client_id'],
            user_id=data['user_id'],
            expires_at=data['expires_at'],
            refresh_expires_at=None,
            scopes=data['scopes'],
        )

    def invalidate_authorization_code(self, client_id, code, request, *args, **kwargs):
        # Authorization codes are use once, invalidate it when a Bearer token
        # has been acquired.
        return delete_authorization_code(client_id, code)

    # Protected resource request

    def validate_bearer_token(self, access_token, scopes, request):
        # Ensure the Bearer token is valid and authorized access to scopes.
        # Remember to check expiration and scope membership

        if not access_token:
            return False

        try:
            bearer_token = self._get_bearer_token_from_access_token(
                access_token=access_token,
            )
        except (BearerTokenNotFound, BearerTokenExpired):
            return False

        # Ensure that all scopes required by the resource are present in the token
        if not scopes.issubset(set(bearer_token['scope'].split(' '))):
            return False

        request.user = None
        if bearer_token['user_id']:
            try:
                request.user = DotDict(auth_cases.get_user_session(
                    self.context,
                    user_id=bearer_token['user_id']
                ))
            except UserNotFound:
                return False

        request.client = self._get_client(bearer_token['client_id'], request)
        request.scopes = bearer_token['scope']

        return True

    # Token refresh request

    def get_original_scopes(self, refresh_token, request, *args, **kwargs):
        """
        Obtain the token associated with the given refresh_token and
            return its scopes, these will be passed on to the refreshed
            access token if the client did not specify a scope during the
            request.
        """
        oauth2_token = auth_cases.get_token_by_refresh_token(
            self.context,
            refresh_token=refresh_token,
        )
        return oauth2_token['scopes']

    def validate_user(self, username, password, client, request, *args, **kwargs):
        """
        Ensure username and password are valid
        """
        try:
            user = auth_cases.verify_password(
                self.context,
                email=username,
                password=password,
                login_type='organizer',
            )
        except (AuthenticationError, ValidationError):
            return False

        request.user = DotDict(auth_cases.get_user_session(
            self.context,
            user_id=user.id
        ))

        return True

    def client_authentication_required(self, request, *args, **kwargs):
        """
        Determine if client authentication is required for current request.
        """
        return True

    def validate_refresh_token(self, refresh_token, client, request, *args, **kwargs):
        try:
            oauth2_token = auth_cases.get_token_by_refresh_token(
                self.context,
                refresh_token=refresh_token,
            )

            if oauth2_token['client_id'] != client.id:
                return False

        except errors.DepResourceNotFound:
            return False

        request.user = DotDict(auth_cases.get_user_session(
            self.context,
            user_id=oauth2_token['user_id']
        ))
        return True

    def rotate_refresh_token(self, request):
        # Determine whether to rotate the refresh token. Default, yes.
        return False

    def _get_client(self, client_id, request=None):
        if request and request.client:
            return request.client
        else:
            try:
                return DotDict(auth_cases.get_client_by_id(
                    self.context,
                    client_id=client_id
                ))
            except OAuth2ClientNotFound:
                return False

    def _get_bearer_token_from_access_token(self, access_token):
        try:
            oauth2_token = DotDict(auth_cases.get_token_by_access_token(
                self.context,
                access_token=access_token,
            ))
        except AccessTokenNotFoundError:
            raise BearerTokenNotFound(
                f'Bearer token matching access_token: {access_token} was not found.'
            )

        expires_at = dateutil.parser.parse(oauth2_token.expires_at)

        if expires_at < datetime.utcnow():
            raise BearerTokenExpired(
                f'Bearer token matching access_token: {access_token} has expired.'
            )

        bearer_token = {
            'client_id': oauth2_token.client_id,
            'user_id': oauth2_token.user_id,
            'token_type': 'Bearer',
            'access_token': oauth2_token.access_token,
            'expires_in': (expires_at - datetime.utcnow())
                          .total_seconds(),
            'scope': oauth2_token.scopes,
            'refresh_token': oauth2_token.refresh_token,
            'state': '',  # if supplied by client
        }
        return bearer_token


validator = EMAuthValidator()
oauth2_server = Server(validator)
