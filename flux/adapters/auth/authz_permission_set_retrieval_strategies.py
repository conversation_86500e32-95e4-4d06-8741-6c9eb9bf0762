from collections import defaultdict

from flask import has_app_context, g as request_cache

from flux.adapters.cache.revision_cache import user_permissions_cache
from flux.domains.common.entity import ContextBoundaryTypes
from flux.domains.identity.authorization import principals
from flux.domains.identity.authorization.user_permission_set_retrieval_strategies import (
    UserPermissionSetRetrievalStrategy,
)


class RequestContextUserPermissionSetRetrievalStrategy(
    UserPermissionSetRetrievalStrategy
):
    """
    A strategy for retrieving user permission sets from the request context.

    If the permission sets are not found in the request context, this strategy falls back to
    another (Redis) strategy.
    """
    def get_user_permission_sets(
        self,
        context,
        user_id: int,
        context_type: ContextBoundaryTypes,
        context_id: str,
    ) -> dict:
        """
        Load and return user permission sets from request cache (flask g).
        If permission sets are not found in the request cache, fall back to Redis cache.
        """
        cached_user_permissions = {}

        # Try to read user permission sets from the request context first
        if has_app_context():
            cached_user_permissions = request_cache.get('user_permissions', {})
            if (
                user_id in cached_user_permissions
                and context_type in cached_user_permissions[user_id]
                and context_id in cached_user_permissions[user_id][context_type]
            ):
                return cached_user_permissions[user_id][context_type][context_id]

        # Read from fallback strategy (Redis cache)
        user_permission_sets = self.get_user_permission_sets_from_fallback_strategy(
            context=context,
            user_id=user_id,
            context_type=context_type,
            context_id=context_id,
        )

        if has_app_context():
            # Cache the user permissions in the request context.
            cached_user_permissions.setdefault(user_id, {}).setdefault(context_type, {})[
                context_id
            ] = user_permission_sets
            request_cache.user_permissions = cached_user_permissions

        return user_permission_sets

    def invalidate_user_permission_sets_cache(
        self,
        user_id: str,
        context_type: ContextBoundaryTypes,
        context_id: str,
    ):
        """
        Invalidate a user's permission sets from Request cache and fall back (Redis) strategy.
        """

        if has_app_context():
            request_cache.user_permissions = {}

        if self.fallback_strategy:
            self.fallback_strategy.invalidate_user_permission_sets_cache(
                user_id=user_id,
                context_type=context_type,
                context_id=context_id,
            )


def _user_permission_sets_cache_key_builder(*args, user_id, context_type, context_id, **kwargs):
    """
    Build a cache key for user permission sets.
    """
    return {
        'service': 'user_permissions_cache',
        'user_id': user_id,
        'context_type': context_type,
        'context_id': context_id,
    }


class RedisUserPermissionSetRetrievalStrategy(UserPermissionSetRetrievalStrategy):
    """
    A strategy for retrieving user permission sets from a Redis cache.

    If the permission sets are not found in the Redis cache, this strategy falls back to
    another (DB) strategy.
    """
    def get_user_permission_sets(
        self,
        context,
        user_id: int,
        context_type: ContextBoundaryTypes,
        context_id: str,
    ) -> dict:
        return self._load_from_cache(
            context=context,
            user_id=user_id,
            context_type=context_type,
            context_id=context_id,
        ).get_or_create()

    @user_permissions_cache.cache_on_arguments(
        key_builder=_user_permission_sets_cache_key_builder,
    )
    def _load_from_cache(
        self,
        context,
        user_id: int,
        context_type: ContextBoundaryTypes,
        context_id: str,
    ):
        """
        Load user permission sets from the cache.

        This method is decorated with the `user_permissions_cache.cache_on_arguments` decorator,
        which caches the result of the method in a Redis cache. The cache key is built using the
        `_user_permission_sets_cache_key_builder` function, and caching is enabled or disabled
        based on the `_user_permission_sets_cache_is_enabled` function.

        The `serializer` and `deserializer` arguments are used to serialize and deserialize the
        cached data.

        If the permission sets are not found in the cache, this method falls back to the
        `get_user_permission_sets_from_fallback_strategy` method.
        """
        return self.get_user_permission_sets_from_fallback_strategy(
            context=context,
            user_id=user_id,
            context_type=context_type,
            context_id=context_id,
        )

    def invalidate_user_permission_sets_cache(
        self,
        user_id: str,
        context_type: ContextBoundaryTypes,
        context_id: str,
    ):
        """Invalidate a user's permission sets from Redis cache and fall back (DB) strategy."""
        user_permissions_cache.invalidate(
            key=_user_permission_sets_cache_key_builder(
                user_id=user_id,
                context_type=context_type,
                context_id=context_id,
            )
        )

        if self.fallback_strategy:
            self.fallback_strategy.invalidate_user_permission_sets_cache(
                user_id=user_id,
                context_type=context_type,
                context_id=context_id,
            )


class DBUserPermissionSetRetrievalStrategy(UserPermissionSetRetrievalStrategy):
    """
    A strategy for retrieving user permission sets from a database.

    This strategy does not fall back to another strategy if the permission sets are not found
    in the database.
    """
    def get_user_permission_sets(
        self,
        context,
        user_id: int,
        context_type: ContextBoundaryTypes,
        context_id: str,
    ) -> dict:
        user_permission_sets = principals.get_user_permissions(
            context,
            user_id=user_id,
            context_type=context_type,
            context_id=context_id,
        )
        return self._optimize_user_permission_sets(user_permission_sets)

    @staticmethod
    def _optimize_user_permission_sets(user_permission_sets):
        """
        This method transforms the original representation of the permission sets into an
        optimized representation for faster access.

        Original Representation: UserPermissionSetCollection
            Which can be represented like this:
            [
                {
                    'permission_set_id': PermissionSets.read_organizers,
                    'access_grants': [
                        {
                            'aget': AccessGrantEntityTypes.events,
                            'ageids': ['all']
                        }
                    ]
                }
            ]

        Optimized Representation:
        {
            'read_organizers': {
                'events': ['all']
            }
        }
        """
        optimized_ps = {}
        for ps in user_permission_sets:
            permission_set_str = ps.permission_set_id.value
            if permission_set_str not in optimized_ps:
                optimized_ps[permission_set_str] = defaultdict(list)

            for ag in ps.access_grants:
                access_grant_entity_type_str = ag['aget'].value
                access_grant_entity_ids = ag['ageids']
                optimized_ps[permission_set_str][access_grant_entity_type_str].extend(
                    access_grant_entity_ids
                )

        return optimized_ps

    def invalidate_user_permission_sets_cache(
        self,
        user_id: str,
        context_type: ContextBoundaryTypes,
        context_id: str,
    ):
        """
        DB strategy does not cache user permissions.
        So there's nothing to invalidate.
        """
