import base64
import binas<PERSON><PERSON>

import newrelic.agent

from urllib.parse import unquote

from flask import (
    request as flask_request,
    has_request_context,
    g as app_context_storage,
)

from flux.adapters.auth.authz_context import (
    InternalContext,
    AttendeeContext,
    AnonymousContext,
    OrganizerContext,
    CheckinManagerContext,
    ClientContext,
    StaffContext,
    ApiKeyContext,
    SelfEditContext,
    CompanyRepresentativeContext,
    UnifiedContext,
)
from flux.adapters.db import ObjectNotFoundError
from flux.adapters.logging import logging
from flux.domains.auth.exceptions import AccessTokenNotFoundError
from flux.domains.auth.usecases import (
    authenticate_login_token,
    authenticate_client_credentials,
    authenticate_access_token,
    authenticate_self_edit_token,
    _get_oauth2_client_by_id,
    get_token_scopes,
)
from flux.domains.auth.constants import UserRole
from flux.domains.common.errors import AuthenticationError, InternalError
from flux.domains.common.consts import (
    ANONYMOUS_ATTENDEE_CLIENT,
    <PERSON>lientAccessType,
    INTERNAL_FLUX_CLIENT,
)
from flux.apps.config import app_config
from flux.utils import feature_flags

CLIENT_CONTEXT_MAPPING = {
    'organizer': OrganizerContext,
    'checkin_manager': CheckinManagerContext,
    'company_representative': CompanyRepresentativeContext,
    'attendee': AttendeeContext,
    'anonymous': AnonymousContext,
    'staff': StaffContext,
    'self_edit': SelfEditContext,
    'api_key': ApiKeyContext,
    'internal': InternalContext,
    'unified_auth': UnifiedContext,
    None: ClientContext,
}

CLIENT_ROLE_MAPPING = {
    'organizer': UserRole.organizer,
    'checkin_manager': UserRole.checkin_manager,
    'staff': UserRole.staff,
    'internal': UserRole.sys_admin,
    'anonymous': UserRole.kiosk_anonymous,
    'attendee': UserRole.attendee,
    'api_key': UserRole.organizer,
    'unified_auth': UserRole.unified_user,
    'self_edit': None,
    None: None,
}

LOGIN_TYPE_TO_CLIENT_ROLE = {
    'lead_capture_staff_onsite_app': UserRole.lead_capture_staff,
    'company_admin': UserRole.company_admin,
    # TODO: remove this when lc access code login is removed
    'onsite_lead_capture_staff': UserRole.lead_capture_staff,
}


class AuthenticationMethod:

    def authenticate(self, uc_context, **kwargs):
        raise NotImplementedError()  # pragma: no cover

    def serialize(self):
        return dict(type=type(self).__name__)

    @staticmethod
    def create_authorization_context(uc_context, token):
        context_class = CLIENT_CONTEXT_MAPPING[token.get('client_context')]
        scoped_entity_type = token.get('scoped_entity_type', None)
        scoped_entity_id = token.get('scoped_entity_id', None)
        login_type = token.get('login_type', None)
        client_context = token.get('client_context', None)

        if login_type and login_type in LOGIN_TYPE_TO_CLIENT_ROLE:
            user_role = LOGIN_TYPE_TO_CLIENT_ROLE[login_type]
        else:
            user_role = CLIENT_ROLE_MAPPING[client_context]

        return context_class(
            context=uc_context,
            user_id=token.get('user_id', None),
            user_role=user_role,
            access_token=token.get('access_token', None),
            expires_at=token.get('expires_at', None),
            login_token=token.get('login_token', None),
            login_type=token.get('login_type', None),
            client_id=token.get('client_id', None),
            login_event_id=token.get('login_event_id', None),
            scoped_entity_type=scoped_entity_type,
            scoped_entity_id=scoped_entity_id,
        )

    @staticmethod
    def _create_unified_auth_context(uc_context, token):
        scope = None
        access_token = token.get('access_token', None)
        if access_token:
            scope = get_token_scopes(uc_context, access_token=access_token)

        return UnifiedContext(
            context=uc_context,
            user_id=token.get('user_id', None),
            scope=scope,
            user_role=UserRole.unified_user,
            access_token=access_token,
            expires_at=token.get('expires_at', None),
            login_token=token.get('login_token', None),
            login_type=token.get('login_type', None),
            client_id=token.get('client_id', None),
        )

    @staticmethod
    def deserialize(dict_):
        dict_ = dict(dict_)
        type_name = dict_.pop('type')
        if type_name not in AUTHN_NAME_TO_TYPE_MAPPING:
            raise AssertionError(
                f'Unknown Authentication type: `{type_name}`'
            )
        return AUTHN_NAME_TO_TYPE_MAPPING[type_name](**dict_)


class AuthenticationMethodWithId(AuthenticationMethod):

    def __init__(self, id=None):
        self.id = id

    def authenticate(self, uc_context, **kwargs):
        raise NotImplementedError()

    def serialize(self):
        return dict(type=type(self).__name__, id=self.id)

    def __eq__(self, other):
        return type(self) is type(other) and hasattr(other, 'id') and self.id == other.id

    def __hash__(self):
        return None


class Cookie(AuthenticationMethodWithId):

    def __str__(self):
        return 'Cookie Authentication Method'

    @property
    def cookie_name(self):
        cookie_name = 'em-login-' + self.id.replace('_', '-')
        if not app_config.is_production_environment():
            cookie_name = f'{app_config.ENV}-{cookie_name}'
        return cookie_name

    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        cookies = kwargs.get('cookies') or []
        try:
            if self.id == 'attendee':
                return self._get_attendee_authorization_context(uc_context, cookies)
            if self.id == 'organizer':
                return self._get_organizer_authorization_context(uc_context, cookies)
            return self._authenticate_cookie(uc_context, cookies)
        except AuthenticationError as e:

            if 'em-login-attendee' in self.cookie_name:
                # remove both attendee and unified cookie
                unified_cookie_name = self.cookie_name.replace('-attendee', '')
                clear_cookies = [self.cookie_name, unified_cookie_name]
            elif 'em-login-organizer' in self.cookie_name:
                # remove both organizer and unified cookie
                unified_cookie_name = self.cookie_name.replace('-organizer', '')
                clear_cookies = [self.cookie_name, unified_cookie_name]
            else:
                clear_cookies = [self.cookie_name]

            raise AuthenticationError(clear_cookies=clear_cookies) from e

    def _authenticate_cookie(self, uc_context, cookies):

        if self.cookie_name not in cookies:
            return None

        access_token = cookies[self.cookie_name]
        if not access_token:
            return None

        token = authenticate_access_token(
            uc_context,
            access_token=access_token,
        )

        authz_context = self.create_authorization_context(
            uc_context,
            token,
        )
        return authz_context

    def _get_attendee_authorization_context(self, uc_context, cookies):
        # get unified auth token first
        token = _get_unified_auth_token(uc_context, cookies=cookies)
        if token:
            return self._create_unified_auth_context(uc_context, token)

        # if no unified auth token, get attendee token
        token = _get_attendee_token(uc_context, cookies=cookies)
        if not token:
            return None

        return self.create_authorization_context(
            uc_context,
            token,
        )

    def _get_organizer_authorization_context(self, uc_context, cookies):
        # check unified auth token first
        is_unified_auth = feature_flags.is_ff_enabled(
            feature_flags.UNIFIED_AUTH_EXP,
            resource_type='sys',
        )
        if is_unified_auth:
            token = _get_unified_auth_token(uc_context, cookies=cookies)
            if (
                token and
                any(scope.client_access_type == ClientAccessType.experience_manager_login
                    for scope in token.token_scopes)
            ):
                return self._create_unified_auth_context(uc_context, token)

        return self._authenticate_cookie(uc_context, cookies)


class ClientCredentials(AuthenticationMethodWithId):

    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        headers = kwargs.get('headers')
        basic_auth_hash = kwargs.get('basic_auth_hash')
        auth_header = headers and headers.get('Authorization')
        if auth_header and auth_header.startswith('Basic '):
            client_hash = auth_header.split('Basic ').pop()
        elif basic_auth_hash:
            client_hash = basic_auth_hash
        else:
            return None

        # decode base64 into byte string, return false if incorrect format
        try:
            decoded_data = base64.decodebytes(client_hash.encode())
        except binascii.Error:
            raise AuthenticationError

        # convert back to python string from byte string
        str_data = decoded_data.decode()

        # unpack data into client_id and client_secret
        try:
            _client_id, _client_secret = str_data.split(':')
        except ValueError:
            raise AuthenticationError

        if _client_id != self.id:
            raise AuthenticationError

        token = authenticate_client_credentials(
            uc_context,
            client_id=_client_id,
            client_secret=_client_secret,
        )
        if not token:
            return None

        authz_context = self.create_authorization_context(
            uc_context,
            token,
        )
        return authz_context

    def __str__(self):
        return 'Client Credentials Authentication Method'


class ApiKey(AuthenticationMethod):

    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        headers = kwargs.get('headers') or {}
        access_token = kwargs.get('access_token')

        if not access_token:
            access_token = headers.get('X-API-KEY', None)
            if not access_token:
                return None

        try:
            # NOTE: We currently accept login tokens and access tokens as Bearer Tokens in the
            # request's authorization header. Both types of token are parsed as 'access_token' by
            # parse_request.py in the AuthorizationHeader class. This affects Token Authentication
            # and ApiKey Authentication. As a fix, if the token is not found in the following query
            # we will assume that the token is the wrong kind for this authentication method, and
            # we will simply return False.
            token = authenticate_login_token(
                uc_context,
                login_type='api_key',
                login_token=access_token,
            )
        except AuthenticationError:
            return False

        if not token:
            return None

        is_unified_auth = feature_flags.is_ff_enabled(
            feature_flags.UNIFIED_AUTH_EXP,
            resource_type='sys',
        )
        if is_unified_auth:
            authz_context = self._create_unified_auth_context(uc_context, token)
        else:
            authz_context = self.create_authorization_context(
                uc_context,
                token,
            )

        return authz_context

    def __str__(self):
        return 'API Key Authentication Method'


class Token(AuthenticationMethodWithId):

    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        headers = kwargs.get('headers')
        access_token = kwargs.get('access_token')
        # If we don't have a token passed in, check for one in the
        # Authorization header.
        if not access_token and headers:
            auth_header = headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                access_token = auth_header.split('Bearer ')[1]

        if not access_token:
            return False

        try:
            token = authenticate_access_token(
                uc_context,
                access_token=access_token,
            )
        except AccessTokenNotFoundError:
            return False

        if self.id and token.get('client_id') != self.id:
            raise AuthenticationError

        authz_context = self.create_authorization_context(
            uc_context,
            token,
        )

        return authz_context

    def __str__(self):
        return 'Access Token Authentication Method'


class ZoomToken(AuthenticationMethod):

    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        headers = kwargs.get('headers')

        zoom_token = headers.get('authorization') if headers else None

        if not zoom_token:
            return False

        token = authenticate_client_credentials(
            uc_context,
            client_id='zoom',
            client_secret=zoom_token,
        )
        if not token:
            return False

        authz_context = self.create_authorization_context(
            uc_context,
            token,
        )
        return authz_context

    def __str__(self):
        return 'Zoom Token Authentication Method'


class HubspotAuth(AuthenticationMethod):

    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        headers = kwargs.get('headers')
        HUBSPOT_CLIENT_ID = 'hubspot'
        hubspot_key = headers.get('authorization') if headers else None
        try:
            client = _get_oauth2_client_by_id(HUBSPOT_CLIENT_ID)
        except ObjectNotFoundError as error:
            raise InternalError from error

        if not hubspot_key or client.get('secret', None) != hubspot_key:
            return False

        hubspot_token = {
            'client_context': 'internal',
            'user_id': 0
        }
        authz_context = self.create_authorization_context(
            uc_context,
            hubspot_token,
        )
        return authz_context

    def __str__(self):
        return 'Hubspot Authentication Method'


# Temporary solution to allow self-edit pages to call GET UAPI Images
class SelfEditToken(AuthenticationMethod):

    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        headers = kwargs.get('headers')
        self_edit_token = kwargs.get('self_edit_token')
        if not self_edit_token and headers:
            auth_header = headers.get('Authorization')
            if auth_header and auth_header.startswith('self-edit '):
                self_edit_token = auth_header.split('self-edit ')[1]

        if not self_edit_token:
            return False
        self_edit_metadata = authenticate_self_edit_token(
            uc_context,
            self_edit_token=self_edit_token,
        )

        authz_context = SelfEditContext(
            context=uc_context,
            user_id=None,
            access_token=None,
            expires_at=None,
            login_token=None,
            login_type=None,
            client_id=None,
            login_event_id=self_edit_metadata['event_id'],
            self_edit_metadata=self_edit_metadata,
        )
        return authz_context

    def __str__(self):
        return 'Self Edit Token Authentication Method'


class UseParent(AuthenticationMethod):
    def __str__(self):
        return 'Parent authentication method'

    def authenticate(self, uc_context, **kwargs):
        raise NotImplementedError()


class RestrictedNoAuthentication(AuthenticationMethod):
    """
    Similar to NoAuthentication, but returns an anonymous attendee context with limited permissions
    """
    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        # Authentication not required, create anonymous attendee token
        token = authenticate_client_credentials(
            uc_context,
            **ANONYMOUS_ATTENDEE_CLIENT,
        )
        if not token:
            return None

        authz_context = self.create_authorization_context(
            uc_context,
            token,
        )

        return authz_context

    def __str__(self):
        return 'No authentication required (restricted permissions)'


class NoAuthentication(AuthenticationMethod):
    """
    No authentication, but provides an internal context with elevated permissions
    """
    __hash__ = None  # type: ignore

    @newrelic.agent.function_trace()
    def authenticate(self, uc_context, **kwargs):
        # Authentication not required, create internal access_token token
        token = authenticate_client_credentials(
            uc_context,
            **INTERNAL_FLUX_CLIENT,
        )
        if not token:
            return None

        authz_context = self.create_authorization_context(
            uc_context,
            token,
        )

        return authz_context

    def __str__(self):
        return 'No authentication required'

    def __eq__(self, other):
        return type(self) is type(other)


AUTHN_NAME_TO_TYPE_MAPPING = {
    method.__name__: method
    for method in (
        Cookie,
        ClientCredentials,
        ApiKey,
        Token,
        UseParent,
        NoAuthentication,
    )
}


def _update_logging_context(user_session):
    if user_session and user_session.user_id:
        logging.update_context(user_id=user_session.user_id)


def _get_attendee_token(uc_context, cookies=None):  # noqa: C901
    """
    Validates attendee login for eventapp authentication if
    those features are enabled based on configuration.
    """

    # Probably we won't support this kind of authentication outside
    # request context, so for now just skip it
    from flux.adapters.uapi.common.cookies import CookieNames

    if not has_request_context():
        return None

    # Temporary solution to bypass auth for flux client remote requests
    # such as ASAP, skips cookie auth and falls back to access token auth.
    url_rule = _get_current_url_rule()
    if str(url_rule) == '/services/<service_name>':
        return None

    cookies = cookies or {}

    # Attempt to find passcode cookie, place its value in app_context_storage
    passcode_cookie = cookies.get(CookieNames.EventSpacePasscode.cookie_name, None)
    if passcode_cookie:
        app_context_storage.passcode = passcode_cookie

    # Attempt to find login cookie and authenticate user
    attendee_cookie_value = cookies.get(CookieNames.Attendee.cookie_name, None)
    if not attendee_cookie_value:
        return None

    # Ensure the cookie value is not urlencoded
    attendee_cookie_value = unquote(attendee_cookie_value)
    return authenticate_access_token(
        uc_context,
        access_token=attendee_cookie_value,
    )


def _get_unified_auth_token(uc_context, cookies=None):
    # copy of _get_attendee_token for unified auth

    from flux.adapters.uapi.common.cookies import CookieNames

    if not has_request_context():
        return None

    # Temporary solution to bypass auth for flux client remote requests
    # such as ASAP, skips cookie auth and falls back to access token auth.
    url_rule = _get_current_url_rule()
    if str(url_rule) == '/services/<service_name>':
        return None

    cookies = cookies or {}

    # Attempt to find passcode cookie, place its value in app_context_storage
    passcode_cookie = cookies.get(CookieNames.EventSpacePasscode.cookie_name, None)
    if passcode_cookie:
        app_context_storage.passcode = passcode_cookie

    # Attempt to find login cookie and authenticate user
    cookie_value = cookies.get(CookieNames.Unified.cookie_name, None)
    if not cookie_value:
        return None

    # Ensure the cookie value is not urlencoded
    cookie_value = unquote(cookie_value)
    return authenticate_access_token(
        uc_context,
        access_token=cookie_value,
    )


def _get_current_url_rule():
    return flask_request.url_rule
