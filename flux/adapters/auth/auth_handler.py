import newrelic.agent

from flux.adapters.auth.authz_context import AuthorizationContext
from flux.adapters.logging import get_logger, logging

from flux.domains.common.errors import (
    AuthenticationError,
)


logger = get_logger(__name__)


@newrelic.agent.function_trace()
def authenticate(context, authn_methods, authn_header=None,
                 cookies=None) -> AuthorizationContext:
    auth_context = {'cookies': cookies or {}}
    if authn_header:
        auth_context.update(authn_header)

    for am in authn_methods:
        authz_context = am.authenticate(context, **auth_context)
        if authz_context:
            if authz_context.user_session and authz_context.user_session.user_id:
                logging.update_context(user_id=authz_context.user_session.user_id)
            return authz_context

    logger.info(
        'Failed to authenticate: '
        'Could not find access_token in message, header, cookie, '
        'or from an api key. '
        '(a035cd0a-6c2a-4bc9-ba2c-1ed008070b35)'
    )
    raise AuthenticationError()
