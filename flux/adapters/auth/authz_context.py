import enum
from abc import ABC
from datetime import datetime
from typing import Optional, Any
from uuid import UUID

from dataclasses import dataclass
import newrelic.agent

from flux.domains.event.commons.entities import PersonNotFoundError, EventNotFoundError
from flux.adapters.db import ObjectNotFoundError
from flux.domains.common.errors import AuthorizationError, EventAuthenticationError

from flux.domains.event.events import (
    get_event_by_id_unsafe,
)
from flux.domains.eventapp.common import eventapp_auth
from flux.domains.organization import organizers as organizer_uc
from flux.domains.common.entity import ContextBoundaryTypes, EntityTypes
from flux.domains.event import checkin_managers
from flux.domains.event import companies, people
from flux.domains.event import company_representatives
from flux.domains.identity.authorization import principals
from flux.domains.identity.authorization.permissions import permissions
from flux.domains.identity.models.models import UserAccounts

from flux.flux_services.common.flux_client.errors import (
    DepResourceNotFound,
    ResourceNotFound,
)
from flux.flux_services.knowledge.staff._models.staff_models import Staff
from flux.utils.dot_dict import DotDict
from flux.utils.enum import StringEnum


class ResourceType(StringEnum):
    profile_image = enum.auto()
    document = enum.auto()


@dataclass(frozen=True)
class OrganizerSessionScope:
    """
    Represents the scope of the valid session, considering that all necessary
    checks have passed. Actual authorization check can use the session scope to
    validate the requirements against it.
    """
    organization_ids: set  # Manageable organizations. Note that it is not
    # affected by the fact whether user is staff or not


class AuthorizationContext(ABC):
    """
    User authorization context ABC.
    This is the base class where all authorization checks should be done
    regarding the current logged-in user.
    Please set the user_id in order to identify the logged-in user to the
    authorization context (`set_user_id`)
    """
    from flux.domains.auth.constants import UserRole

    _user_id: Optional[int]

    _user_role: Optional[UserRole]

    _scope:  Optional[dict]

    _scoped_entity_type: Optional[EntityTypes]

    _scoped_entity_id: Optional[UUID]

    _access_token: Optional[str]

    _expires_at: Optional[datetime]

    _login_type: Optional[str]

    _login_token: Optional[str]

    _first_name: Optional[str]

    _last_name: Optional[str]

    _email: Optional[str]

    _client_id: Optional[str]

    _person_id: Optional[UUID]

    _name: Optional[str]

    _login_event_id: Optional[int]

    _is_staff = False

    _is_checkin_manager = False

    _is_lead_capture_staff = False

    _is_unified_auth = False

    def __init__(self, context, user_id=None, user_role=None, access_token=None,
                 expires_at=None, login_type=None, login_token=None,
                 client_id=None, login_event_id=None, scoped_entity_type=None,
                 scoped_entity_id=None, scope=None
                 ):
        self._context = context
        context.set_user(self)
        self._user_id = user_id
        self._user_role = user_role
        self._access_token = access_token
        self._expires_at = expires_at
        self._login_token = login_token
        self._login_type = login_type
        self._email = None
        self._first_name = None
        self._last_name = None
        self._client_id = client_id
        self._login_event_id = login_event_id
        self._scoped_entity_type = scoped_entity_type
        self._scoped_entity_id = scoped_entity_id
        self._scope = scope
        self._person_id = self._load_people_id()
        self._load_user_info()

    def _load_user_info(self):
        if self._user_id:
            try:
                user = UserAccounts.get_by_id(self._user_id)
                self._email = user.email
                self._first_name = user.first_name
                self._last_name = user.last_name
            except ObjectNotFoundError:
                self._email = None
                self._first_name = None
                self._last_name = None

    def _load_people_id(self):
        if self._user_id and self._login_event_id:
            person_entity = eventapp_auth.get_person_entity(
                self._context.ud,
                self._login_event_id,
                self._user_id,
            )
            person = eventapp_auth.get_event_profile_by_user(
                person_entity,
            )
            if person:
                return person.people_id
        return None

    def set_user_id(self, user_id: int):
        self._user_id = user_id

    def set_user_role(self, user_role: UserRole):
        self._user_role = user_role

    def set_access_token(self, access_token: str):
        self._access_token = access_token

    @property
    def name(self) -> Optional[str]:
        """
        :return: name if any is set, None otherwise.
        """
        return self._name

    @property
    def user_session(self):
        user_session = DotDict()
        user_session.user_id = self._user_id
        user_session.user_role = self._user_role
        user_session.access_token = self._access_token
        user_session.expires_at = self._expires_at
        user_session.login_type = self._login_type
        user_session.login_token = self._login_token
        user_session.client_id = self._client_id
        user_session.client_context = self._name
        user_session.login_event_id = self._login_event_id
        return user_session

    @property
    def id(self) -> Optional[int]:
        """
        :return: user_id if any is set, None otherwise.
        """
        return self._user_id

    @property
    def user_role(self) -> Optional[UserRole]:
        """
        :return: user_role if any is set, None otherwise.
        """
        return self._user_role

    @property
    def scope(self) -> Optional[dict]:
        """
        :return: scope if any is set, None otherwise.
        """
        return self._scope

    @property
    def scoped_entity_type(self) -> Optional[EntityTypes]:
        """
        :return: scoped_entity_type if any is set, None otherwise.
        """
        return self._scoped_entity_type

    @property
    def scoped_entity_id(self) -> Optional[UUID]:
        """
        :return: scoped_entity_id if any is set, None otherwise.
        """
        return self._scoped_entity_id

    @property
    def login_event_id(self) -> Optional[int]:
        """
        :return: login_event_id if any is set, None otherwise.
        """
        return self._login_event_id

    @property
    def person_id(self) -> Optional[UUID]:
        """
        :return: person_id if any is set, None otherwise.
        """
        return self._person_id

    @property
    def expires_at(self) -> Optional[datetime]:
        """
        :return: expires_at if any is set, None otherwise.
        """
        return self._expires_at

    @property
    def login_token(self) -> Optional[str]:
        """
        :return: login_token if any is set, None otherwise.
        """
        return self._login_token

    @property
    def login_type(self) -> Optional[str]:
        """
        :return: login_type if any is set, None otherwise.
        """
        return self._login_type

    @property
    def organizer_session_scope(self) -> Optional[OrganizerSessionScope]:
        """
        Returns the OrganizerSessionScope for the given user. If the user
        isn't logged in as organizer, it returns `None`
        """
        return None

    @property
    def access_token(self) -> Optional[str]:
        """
        :return: access_token if any is set, None otherwise.
        """
        return self._access_token

    @property
    def first_name(self) -> Optional[str]:
        """
        :return: first_name if any is set, None otherwise.
        """
        return self._first_name

    @property
    def last_name(self) -> Optional[str]:
        """
        :return: last_name if any is set, None otherwise.
        """
        return self._last_name

    @property
    def email(self) -> Optional[str]:
        """
        :return: email if any is set, None otherwise.
        """
        return self._email

    @property
    def is_staff(self) -> bool:
        """
        :return: if the user is staff.
        """
        return self._is_staff

    @property
    def is_checkin_manager(self) -> bool:
        """
        :return: if the user is checkin manager.
        """
        return self._is_checkin_manager

    @property
    def is_lead_capture_staff(self) -> bool:
        """
        :return: if the user is lead capture staff
        """
        return self._is_lead_capture_staff

    @property
    def is_unified_auth(self) -> bool:
        """
        :return: if the user is globally logged in
        """
        return self._is_unified_auth

    @newrelic.agent.function_trace()
    def people_id(self, event_id=None) -> Optional[UUID]:
        """
        Returns the people id of the current user for the given event_id, if
        the user has a profile for the given event, None otherwise
        """
        if event_id is None or event_id == self._login_event_id:
            return self._person_id
        login_status = self._get_login_status(event_id)
        if login_status and login_status.profile:
            return login_status.profile.people_id

        return None

    @newrelic.agent.function_trace()
    def has_organizer_rights(self, event_id=None) -> bool:
        """
        Checks if the given user is either the internal user, or member of
        em staff or is an organizer with access to the given event.
        If no event_id is given, the check will instead check if the given user
        has organizer rights within at least one organization.
        """
        return False

    @newrelic.agent.function_trace()
    def has_organizer_rights_in_organization(self, organization_id) -> bool:
        """
        Checks if the given user is either the internal user, or member of
        em staff or is an organizer with access to the given organization.
        """
        return False

    @newrelic.agent.function_trace()
    def has_lead_capture_staff_rights(
        self,
        event_id=None,
        company_id=None,
        is_active=False,
    ) -> bool:
        """
        This check confirms that the given user can access to the given
        event, company as lead capture staff .
        Returns True or False based on whether the user has access.
        """
        return False

    @newrelic.agent.function_trace()
    def has_company_admin_rights(
        self,
        event_id=None,
        company_id=None,
        is_admin_invitation_accepted=False,
    ):
        """
        Return True or False based on whether the given user has company admin access.
        """
        return False

    @newrelic.agent.function_trace()
    def has_checkin_manager_rights(self, event_id) -> bool:
        """
        This check confirms that the given user can access to the given
        event as checkin manager .
        Returns True or False based on whether the user has access.
        """
        return False

    @newrelic.agent.function_trace()
    def has_login_event_rights(self, event_id) -> bool:
        """
        This check confirms that the given user can access to the given
        event as checkin manager .
        Returns True or False based on whether the user has access.
        """
        return False

    def is_anonymous_context(self) -> bool:
        """
        This check confirms that the given user is an anonymous user.
        Returns True or False based on whether the user is anonymous.
        """
        return self.id is None

    @newrelic.agent.function_trace()
    def is_organizer_for_event(self, event_id) -> bool:
        """
        This check confirms that the given user is a logged-in attendee who is also an organizer.

        This check is used to allow organizers to perform certain actions despite being logged in
        as an attendee. This check is not the same as has_organizer_rights().
        """
        return False

    @newrelic.agent.function_trace()
    def has_attendee_rights(self, event_id, force_login=False) -> bool:
        """
        This check confirms that the given user can access the event as an
        attendee based on its security settings.

        Note that anonymous users are supported if event settings allow it.

        `force_login` flag will require the user to have a valid login to the
        system regardless of the event security settings. Note that for modern
        events we assume the user to have a valid profile if they are logged
        in.

        Returns True or False based on whether the user has access.
        """
        return False

    @newrelic.agent.function_trace()
    def is_resource_owner(self, event_id: int, resource_id: Any,
                          resource_type: ResourceType) -> bool:
        return False

    @newrelic.agent.function_trace()
    def is_event_person_owner(self, event_id, people_id) -> bool:
        """
        Check if the currently authenticated User owns an Event Person record.
        """
        if not people_id:
            return False

        if not self.has_attendee_rights(event_id):
            return False

        login_status = self._get_login_status(event_id)

        if login_status and login_status.profile:
            return login_status.profile.people_id == people_id
        return False

    @newrelic.agent.function_trace()
    def has_event_access(self, event_id, force_login=False):
        """ Checks if the current user has basic event access rights.

        This checks if the user is either an organizer, checkin manager, lead capture
        or an attendee for a given event. This is useful for basic event
        content that is available to all people interacting with that event.
        """
        return (
            self.has_organizer_rights(event_id) or
            self.has_attendee_rights(event_id, force_login=force_login) or
            self.has_checkin_manager_rights(event_id) or
            self.has_login_event_rights(event_id)
        )

    def assert_is_resource_owner(self, event_id, resource_id,
                                 resource_type: ResourceType) -> None:
        if not self.is_resource_owner(event_id, resource_id, resource_type):
            raise AuthorizationError()

    def assert_has_organizer_rights(self, event_id=None) -> None:
        if not self.has_organizer_rights(event_id):
            raise AuthorizationError()

    def assert_has_organizer_rights_in_organization(self, organization_id) -> None:
        if not self.has_organizer_rights_in_organization(organization_id):
            raise AuthorizationError()

    def assert_is_organizer_for_event(self, event_id) -> None:
        if not self.is_organizer_for_event(event_id):
            raise AuthorizationError()

    def assert_has_attendee_rights(self, event_id, force_login=False) -> None:
        if not self.has_attendee_rights(event_id, force_login=force_login):
            raise AuthorizationError()

    def assert_has_checkin_manager_rights(self, event_id) -> None:
        if not self.has_checkin_manager_rights(event_id):
            raise AuthorizationError()

    def assert_has_lead_capture_staff_rights(self, event_id, company_id) -> None:
        if not self.has_lead_capture_staff_rights(event_id, company_id):
            raise AuthorizationError()

    def assert_has_company_admin_rights(self, event_id=None, company_id=None) -> None:
        if not self.has_company_admin_rights(event_id, company_id):
            raise AuthorizationError()

    def assert_has_login_event_rights(self, event_id) -> None:
        if not self.has_login_event_rights(event_id):
            raise AuthorizationError()

    def assert_has_organizer_or_checkin_manager_rights(self, event_id) -> None:
        if not (
            self.has_organizer_rights(event_id) or
            self.has_checkin_manager_rights(event_id)
        ):
            raise AuthorizationError()

    def assert_is_event_person_owner(self, event_id, people_id) -> None:
        if not self.is_event_person_owner(event_id, people_id):
            raise AuthorizationError()

    def assert_has_event_access(self, event_id, force_login=False):
        if not self.has_event_access(event_id, force_login=force_login):

            # This slack thread explains the reason for this change
            # https://eventmobi.slack.com/archives/C97QLKS86/p1717013613999379
            try:
                get_event_by_id_unsafe(
                    self._context, event_id=event_id
                )
            except EventNotFoundError:
                raise EventAuthenticationError()
            raise AuthorizationError()

    def assert_is_staff(self):
        if not self.is_staff:
            raise AuthorizationError()

    def clear_user_session(self):
        self._user_id = None
        self._access_token = None
        self._user_role = None
        self._expires_at = None
        self._login_type = None
        self._login_token = None
        self._first_name = None
        self._last_name = None
        self._email = None

    def _get_login_status(self, event_id):
        ud = self._context.ud
        try:
            return eventapp_auth.login_status(ud, event_id, self.id)
        except (
            DepResourceNotFound,
            ResourceNotFound,
        ):
            return None

    def remove_references(self):
        self._context = None

    def _has_manage_event_permission(
        self,
        event_id,
        organization_id,
        require_permission_in_scope: bool = False
    ):
        """
        Check if the user has the MANAGE_EVENTS permission along with the access grant
        of the event_id.
        """
        return self._context.authz_manager_v2.is_user_allowed(
            user_id=self._user_id,
            permission=permissions.MANAGE_EVENTS,
            context_type=ContextBoundaryTypes.organizations,
            context_id=str(organization_id),
            required_access_grants={principals.AccessGrantEntityTypes.events: str(event_id)},
            require_permission_in_scope=require_permission_in_scope,
        )


class OrganizerContext(AuthorizationContext):
    """
    This implementation should be used when a user with organizer rights
    logs in.
    """
    _organizer_session_scope: Optional[OrganizerSessionScope] = None

    _name = 'organizer'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._load_organizer_scope()
        if self._user_id:
            self._is_staff = user_exists_as_staff(self._user_id)

    def set_user_id(self, user_id: int):
        super().set_user_id(user_id)
        self._load_organizer_scope()
        self._is_staff = user_exists_as_staff(self._user_id)

    @property
    def organizer_session_scope(self) -> Optional[OrganizerSessionScope]:
        return self._organizer_session_scope

    def has_organizer_rights(self, event_id=None) -> bool:
        if event_id:
            try:
                event = get_event_by_id_unsafe(
                    self._context, event_id=event_id
                )
            except EventNotFoundError:
                return False

            return self._has_manage_event_permission(
                event_id=event_id, organization_id=event.organization_id,
            )

        # TODO: supporting not having an event_id in this method is messy

        # TODO: We should port this to new authorization system.

        # We want to limit the usage of API keys within the organizations they are created
        # So we only return 'True' if this method is called by an instance of OrganizerContext
        if self.is_staff and self._name == 'organizer':
            return True

        session_scope = self.organizer_session_scope
        if not session_scope:
            return False

        return True

    def has_organizer_rights_in_organization(self, organization_id) -> bool:

        # We want to limit the usage of API keys within the organizations they are created
        # So we only return 'True' if this method is called by an instance of OrganizerContext
        if self.is_staff and self._name == 'organizer':
            return True

        if not self.organizer_session_scope:
            return False

        return organization_id in self.organizer_session_scope.organization_ids

    def has_checkin_manager_rights(self, event_id) -> bool:
        try:
            event = get_event_by_id_unsafe(
                self._context, event_id=event_id
            )
        except EventNotFoundError:
            return False

        return self._has_manage_event_permission(
            event_id=event_id, organization_id=event.organization_id,
        )

    def is_resource_owner(
        self,
        event_id: int,
        resource_id: Any,
        resource_type: ResourceType
    ) -> bool:
        return self.has_organizer_rights(event_id)

    def _load_organizer_scope(self):
        if self._user_id:
            with self._context.use_internal_context():
                organizers = organizer_uc.get_organizers_by_user_id_cls(
                    self._context,
                    user_id=self._user_id
                )
            allowed_org_ids = set()
            for organizer in organizers:
                allowed_org_ids.add(organizer.organization_id)
                if not self._email:
                    self._email = organizer.email
            self._organizer_session_scope = OrganizerSessionScope(
                set(allowed_org_ids)
            )
        else:
            self._organizer_session_scope = None


class ApiKeyContext(OrganizerContext):
    _name = 'api_key'

    def _load_organizer_scope(self):
        if self._login_token:
            key = organizer_uc.get_api_key_by_key_cls(
                self._context,
                key=self._login_token
            )

            self._organizer_session_scope = OrganizerSessionScope(
                {key.organization_id}
            )

        else:
            self._organizer_session_scope = None


class InternalContext(AuthorizationContext):
    """
    This implementation is for internal usage only.
    Generally used for flux_async
    tasks.
    """
    _name = 'internal'

    def has_organizer_rights(self, event_id=None) -> bool:
        return True

    def has_organizer_rights_in_organization(self, organization_id) -> bool:
        return True

    def has_checkin_manager_rights(self, event_id) -> bool:
        return True

    def is_organizer_for_event(self, event_id) -> bool:
        return True

    def has_attendee_rights(self, event_id, force_login=False) -> bool:
        return True

    def is_resource_owner(self, event_id: int, resource_id: Any,
                          resource_type: ResourceType) -> bool:
        return True

    @property
    def is_staff(self) -> bool:
        return True


class AttendeeContext(AuthorizationContext):
    _name = 'attendee'

    def __init__(self, *args, client_id='reactor_user', **kwargs):
        super().__init__(*args, client_id=client_id, **kwargs)

        self.owner_validators = {
            ResourceType.profile_image: self.profile_image_owner_validator,
            ResourceType.document: self.document_owner_validator,
        }

    def has_organizer_rights(self, event_id=None) -> bool:
        return False

    def is_organizer_for_event(self, event_id) -> bool:
        """
        This check confirms that the given user is a logged-in attendee who is also an organizer.

        This check is used to allow organizers to perform certain actions despite being logged in
        as an attendee. This check is not the same as has_organizer_rights().
        """
        login_status = self._get_login_status(event_id)

        if (
            not (login_status and login_status.can_access_event and login_status.has_valid_login)
            or login_status.role != 'organizer'
        ):
            return False

        return True

    def has_attendee_rights(self, event_id, force_login=False) -> bool:
        """
        This check confirms that the given user can access the event as an
        attendee based on its security settings.

        Note that anonymous users are supported if event settings allow it.

        `force_login` flag will require the user to have a valid login to the
        system regardless of the event security settings. Note that for modern
        events we assume the user to have a valid profile if they are logged
        in.

        Returns True or False based on whether the user has access.
        """

        login_status = self._get_login_status(event_id)

        if not login_status or not login_status.can_access_event:
            return False

        if force_login and not login_status.has_valid_login:
            return False

        return True

    def is_resource_owner(self, event_id: int, resource_id: Any,
                          resource_type: ResourceType) -> bool:
        validator = self.owner_validators.get(resource_type)
        if validator is None:
            return False
        return validator(event_id, resource_id)

    def _common_validator(self, event_id, resource_id):
        if resource_id is None:
            return False
        if not self.has_attendee_rights(event_id):
            return False
        if self.people_id(event_id) is None:
            return False
        return None

    def profile_image_owner_validator(self, event_id, image_id):
        self._common_validator(event_id, image_id)
        people_id = self.people_id(event_id)
        person = people.get_by_id(
            self._context,
            event_id=event_id,
            people_id=people_id,
        )
        if not person.profile_image:
            return False
        return str(person.profile_image.id) == str(image_id)

    def document_owner_validator(self, event_id, document_id):
        self._common_validator(event_id, document_id)
        people_id = self.people_id(event_id)
        document = self._context.ud.documents.single(
            params={'id': str(document_id), 'event_id': event_id}
        )
        return people_id in document.people_ids


class AnonymousContext(AuthorizationContext):
    _name = 'anonymous'

    def has_attendee_rights(self, event_id, force_login=False) -> bool:
        """
        This check confirms that the given anonymous user can access the event based on the event
        settings.

        The `force_login` flag will require the user to have a valid login to the
        system regardless of the event security settings. This prevents anonymous users by
        definition.

        Returns True or False based on whether the anonymous user has access.
        """
        if force_login:
            return False

        login_status = self._get_login_status(event_id)
        if not login_status or not login_status.can_access_event:
            return False

        return True


class CheckinManagerContext(AuthorizationContext):
    """
    This implementation should be used when a user with checkin_manager rights
    logs in.
    """

    _name = 'checkin_manager'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self._email:
            self._load_email()
        self._is_checkin_manager = True

    def has_checkin_manager_rights(self, event_id) -> bool:
        try:
            get_event_by_id_unsafe(
                self._context, event_id=event_id
            )
        except EventNotFoundError:
            return False

        try:
            checkin_managers.get_manager_by_user_id(
                self._context,
                user_id=self._user_id,
                event_id=event_id,
            )
        except ObjectNotFoundError:
            return False

        return True

    def has_login_event_rights(self, event_id=None) -> bool:
        return event_id == self._login_event_id

    def _load_email(self):
        if self._user_id:
            try:
                user = UserAccounts.get_by_id(self._user_id)
                self._email = user.email
            except ObjectNotFoundError:
                self._email = None


class CompanyRepresentativeContext(AuthorizationContext):
    """
    This implementation should be used when a user with lead_capture_staff rights
    logs in.
    """

    _name = 'company_representative'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self._email:
            self._load_email()
        self._is_lead_capture_staff = True
        self._load_organizer_scope()

    @property
    def organizer_session_scope(self) -> Optional[OrganizerSessionScope]:
        return self._organizer_session_scope

    def _load_organizer_scope(self):
        organizer_session_scope = None
        if self._user_id:
            with self._context.use_internal_context():
                organizers = organizer_uc.get_organizers_by_user_id_cls(
                    self._context,
                    user_id=self._user_id
                )
                allowed_org_ids = {org.organization_id for org in organizers}
                if allowed_org_ids:
                    organizer_session_scope = OrganizerSessionScope(
                        set(allowed_org_ids)
                    )
        self._organizer_session_scope = organizer_session_scope

    def has_organizer_rights(self, event_id=None) -> bool:
        if event_id:
            return self._has_organizer_access(event_id)

        return bool(
            self.organizer_session_scope and
            self.organizer_session_scope.organization_ids
        )

    def has_event_access(self, event_id, force_login=False):
        with self._context.use_internal_context():
            person = people.get(
                self._context,
                event_id=event_id,
                user_id=self._user_id,
                registration_status={'registered'},
            )
            if person:
                return True

        return self._has_organizer_access(event_id)

    def has_lead_capture_staff_rights(
        self,
        event_id=None,
        company_id=None,
        is_active=False,
    ) -> bool:

        query = {'is_lead_capture_staff': True}
        if event_id:
            query['event_id'] = event_id
        if company_id:
            query['company_id'] = company_id
        lead_capture_staff = company_representatives.get_all_by_user_id(
            self._context,
            user_id=self._user_id,
            query=query,
        )

        return bool(lead_capture_staff)

    def has_company_admin_rights(
        self,
        event_id=None,
        company_id=None,
        is_admin_invitation_accepted=False,
    ):

        query = {'is_admin': True}
        if event_id:
            query['event_id'] = event_id
        if company_id:
            query['company_id'] = company_id

        company_admins = company_representatives.get_all_by_user_id(
            self._context,
            user_id=self._user_id,
            query=query,
        )
        if company_admins:
            return True

        return self.has_organizer_rights(event_id)

    def _has_organizer_access(self, event_id):
        event = get_event_by_id_unsafe(self._context, event_id=event_id)
        return self._has_manage_event_permission(
                event_id=event_id, organization_id=event.organization_id,
            )

    def has_login_event_rights(self, event_id=None) -> bool:
        return event_id == self._login_event_id

    def _load_email(self):
        if self._user_id:
            try:
                user = UserAccounts.get_by_id(self._user_id)
                self._email = user.email
            except ObjectNotFoundError:
                self._email = None


class StaffContext(OrganizerContext):
    _name = 'staff'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self._email:
            self._load_email()
        self._is_staff = True

    def has_organizer_rights_in_organization(self, organization_id) -> bool:
        return True

    def _load_email(self):
        if self._user_id:
            try:
                user = UserAccounts.get_by_id(self._user_id)
                self._email = user.email
            except ObjectNotFoundError:
                self._email = None


class ClientContext(AuthorizationContext):
    _name = None


class SelfEditContext(AuthorizationContext):
    _name = 'self_edit'
    _self_edit_metadata = None
    _managed_entity = None

    def __init__(self, *args, self_edit_metadata=None, **kwargs):
        super().__init__(*args, **kwargs)
        self._self_edit_metadata = self_edit_metadata
        self._load_managed_entity()

    def _load_managed_entity(self):
        if self._self_edit_metadata:
            try:
                if self._self_edit_metadata['resource_type'] == 'people':
                    self._managed_entity = people.get_by_id(
                        self._context,
                        event_id=self._login_event_id,
                        people_id=self._self_edit_metadata['entity_id'],
                    )
                elif self._self_edit_metadata['resource_type'] == 'companies':
                    self._managed_entity = companies.get_by_id(
                        self._context,
                        event_id=self._login_event_id,
                        company_id=self._self_edit_metadata['entity_id'],
                    )
            except (companies.CompanyNotFoundError, PersonNotFoundError):
                self._managed_entity = None

    def has_attendee_rights(self, event_id=None, force_login=False) -> bool:
        return event_id == self._login_event_id

    def is_resource_owner(self, event_id: int, resource_id: Any,
                          resource_type: ResourceType) -> bool:

        if resource_type != ResourceType.profile_image:
            return False

        if not self._managed_entity:
            return False

        if not self._self_edit_metadata:
            return False

        if self._self_edit_metadata['resource_type'] == 'people':
            return str(self._managed_entity.profile_image.id) == str(resource_id)
        if self._self_edit_metadata['resource_type'] == 'companies':
            return str(self._managed_entity.image_id) == str(resource_id)

        return False


class UnifiedContext(AuthorizationContext):
    _name = 'unified'
    _organizer_session_scope: Optional[OrganizerSessionScope] = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._is_unified_auth = True

    @property
    def organizer_session_scope(self) -> Optional[OrganizerSessionScope]:
        org_scopes = self.scope.get('organization_scopes') if self.scope else None
        if not self._user_id or not org_scopes:
            return None

        if self._organizer_session_scope is None:
            self._load_organizer_scope()
        return self._organizer_session_scope

    def _load_organizer_scope(self):
        with self._context.use_internal_context():
            organizers = organizer_uc.get_organizers_by_user_id_cls(
                self._context,
                user_id=self._user_id
            )
        allowed_org_ids = {organizer.organization_id for organizer in organizers}
        allowed_org_ids = self._filter_org_ids_not_in_scope(allowed_org_ids)
        self._organizer_session_scope = OrganizerSessionScope(set(allowed_org_ids))

    def _filter_org_ids_not_in_scope(self, allowed_org_ids):
        # filter out the org_ids that are not in the scope
        org_ids = self.scope.get('organization_scopes', []) if self.scope else []
        if '*' in org_ids:
            return allowed_org_ids

        return {org_id for org_id in allowed_org_ids if str(org_id) in org_ids}

    def has_organizer_rights(self, event_id=None) -> bool:
        if not self._user_id:
            return False

        if event_id:
            try:
                event = get_event_by_id_unsafe(
                    self._context, event_id=event_id
                )
            except EventNotFoundError:
                return False

            return self._has_manage_event_permission(
                event_id=event_id,
                organization_id=event.organization_id,
                require_permission_in_scope=True,
            )

        org_scopes = self.scope.get('organization_scopes') if self.scope else None
        if org_scopes:
            return True

        return False

    def is_organizer_for_event(self, event_id) -> bool:
        if not self._user_id:
            return False

        return self._context.authz_manager_v2.is_user_allowed(
            user_id=self._user_id,
            permission=permissions.MANAGE_EVENTS,
            context_type=ContextBoundaryTypes.events,
            context_id=str(event_id),
            required_access_grants={principals.AccessGrantEntityTypes.events: str(event_id)},
            require_permission_in_scope=True,
        )

    def has_attendee_rights(self, event_id, force_login=False) -> bool:
        required_permission = (
            permissions.ACCESS_EVENTS if force_login else permissions.ACCESS_EVENTS_BASICS
        )

        return self._context.authz_manager_v2.is_user_allowed(
            user_id=self._user_id,
            permission=required_permission,
            context_type=ContextBoundaryTypes.events,
            context_id=str(event_id),
            required_access_grants={principals.AccessGrantEntityTypes.events: str(event_id)},
            require_permission_in_scope=True,
        )

    def has_event_access(self, event_id, force_login=False):
        return self.has_attendee_rights(event_id, force_login=force_login)

    def people_id(self, event_id=None) -> Optional[UUID]:
        if not self._user_id and not event_id:
            return None

        if self.has_attendee_rights(event_id, force_login=True):
            person_entity = eventapp_auth.get_person_entity(
                self._context.ud,
                event_id,
                self._user_id,
            )
            person = eventapp_auth.get_event_profile_by_user(
                person_entity,
            )
            if person:
                return person.people_id

        return None

    def is_event_person_owner(self, event_id, people_id) -> bool:
        """
        Check if the currently authenticated User owns the Event Person record.
        """
        if not people_id:
            return False

        return self.people_id(event_id) == people_id

    def is_resource_owner(self, event_id: int, resource_id: Any,
                          resource_type: ResourceType) -> bool:
        # TODO: consider removing this as this is not required anywhere
        return False


def user_exists_as_staff(user_id):
    try:
        return Staff.get_by_user_id(user_id) is not None
    except ObjectNotFoundError:
        return False
