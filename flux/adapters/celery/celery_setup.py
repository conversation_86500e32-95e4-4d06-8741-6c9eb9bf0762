from celery import Celery
from celery.signals import celeryd_after_setup
from flask import g as app_context_storage, request as flask_request
from flask import has_app_context
from kombu import Queue
from kombu.serialization import register

from flux.adapters import logging
from flux.adapters.celery.celery_metrics import timed_task
from flux.adapters.celery.errors import FluxAsyncRetryException
from flux.adapters.logging import get_async_logger
from flux.adapters.logging.logging import setup_celery_logging
from flux.utils.serializer import ExtendedEncoder, JSONDecoder
from flux.utils.chromium_utils import download_chromium

# See complete initialization in flux.init.extensions
FLUX_ORIGIN = 'flux_request_origin'
FLUX_REQUEST_ID = 'flux_request_id'
celery = Celery(__name__, autofinalize=False)
flask_app = None


# Task Priorities (1-3)
TASK_PRIORITY_HIGH = 3
TASK_PRIORITY_MEDIUM = 2
TASK_PRIORITY_LOW = 1


def _configure_celery(app):
    # Register serializer
    register(
        name='json',
        encoder=ExtendedEncoder().encode,
        decoder=JSONDecoder().decode,
        content_type='application/json',
        content_encoding='utf-8',
    )

    # List the celery queues we want to create. Missing queues will be created automatically
    # because 'task_create_missing_queues' celery config is enabled by default.
    # We are creating a priority queue with 1-3 levels of priority.
    # Note that changing the priority of an existing queue will not work.
    # You need to delete the queue and recreate it with the new priority.
    celery.conf.task_queues = (
        Queue(
            name=app.config.get('RABBITMQ_PRIORITY_QUEUE'),
            queue_arguments={'x-max-priority': 3},
        ),
    )

    # Set the default queue, this is the queue where celery will route the tasks by default.
    celery.conf.task_default_queue = app.config.get('RABBITMQ_PRIORITY_QUEUE')

    # Set the default task priority. Each task can choose to override this value.
    celery.conf.task_default_priority = 2  # TASK_PRIORITY_MEDIUM

    celery.config_from_object(app.config)
    async_logger = get_async_logger(__name__)
    TaskBase = celery.Task

    class ContextTask(TaskBase):
        abstract = True

        def __call__(self, *args, **kwargs):
            if self.request:
                # during tests the TaskBase.__call__ code is
                # adding another request on top of current one
                # this new one, only has the params passed to the original
                # function making the function lose access to the headers
                # I am adding this here so we can restore access to headers
                # these scenarios
                self._initial_request = self.request
            self.init_logging_context()
            async_logger.info(f'Celery Task Starting - {self.name}')
            with timed_task(self.name):
                # Note: Flask-SQLAlchemy takes care of session handling
                # within application context. As a result we can create
                # a context withing celery worker to get proper session
                # handling.
                try:
                    if has_app_context():
                        result = TaskBase.__call__(self, *args, **kwargs)
                    else:
                        with app.app_context():
                            result = TaskBase.__call__(self, *args, **kwargs)
                except FluxAsyncRetryException as exc:
                    async_logger.exception(
                        f'Celery Task Retry Triggered - {self.name} - Retry reason: {exc}',
                        retry_reason=exc,
                        eta=exc.eta,
                        countdown=exc.countdown,
                        current_retries=self.request.retries,
                        max_retries=exc.max_retries,
                        additional_context=exc.additional_context,
                    )
                    raise self.retry(
                        eta=exc.eta,
                        countdown=exc.countdown,
                        max_retries=exc.max_retries,
                    )
            async_logger.info(f'Celery Task Completed - {self.name}')
            return result

        def apply_async(self, args=None, kwargs=None, task_id=None,
                        producer=None, link=None, link_error=None, **options):
            """
            Invoked either directly or via .delay() to fork a task from the
            main process
            """
            if not options.get('headers'):
                options['headers'] = {}

            if celery.current_task:
                previous_headers = celery.current_task.request.headers

                if previous_headers:
                    for header in previous_headers:
                        options['headers'][header] = previous_headers[header]
            else:
                request_id = _get_request_id_from_uapi_context()
                if not request_id:
                    request_id = _get_request_id_from_flask_request()

                options['headers'].update({
                    FLUX_REQUEST_ID: request_id,
                    FLUX_ORIGIN: _get_request_origin_from_flask_request(),
                })

            # https://github.com/celery/celery/issues/4875
            options['headers'] = {
                'headers': options['headers']
            }

            return super().apply_async(
                args, kwargs, task_id, producer, link, link_error, **options
            )

        def init_logging_context(self):
            headers = self.request.headers or {}

            # https://github.com/celery/celery/issues/4875 (in case they fix)
            headers = headers['headers'] if 'headers' in headers else headers

            request_id = headers.get(FLUX_REQUEST_ID)
            async_task_id = self.request.id
            logging.init_context(
                request_uuid=request_id,
                async_task_id=async_task_id,
            )

        def on_failure(self, exc, task_id, args, kwargs, einfo):
            async_logger.error(
                'Celery Task Failed - %s: %s', self.name, str(exc),
                exc_info=exc,
            )

        def on_retry(self, exc, task_id, args, kwargs, einfo):
            async_logger.warn(f'Celery Task Retry - {self.name}')

        @property
        def headers(self):
            '''
            this attribute will search for the headers dict
            in the request or in the _initial_request if there is any
            it will return None in case of not found
            it will check if the headers are wrapped inside a 'headers' key
            :return: the headers dictionary
            '''
            if self.request and self.request.headers:
                temp = self.request.headers
            elif self._initial_request and self._initial_request.headers:
                temp = self._initial_request.headers
            else:
                return None
            inner_header = temp.get('headers')
            if inner_header and FLUX_REQUEST_ID in inner_header:
                return inner_header
            return temp

    celery.Task = ContextTask

    # Perform delayed initialization
    celery.finalize()


def _get_request_id_from_uapi_context():
    if hasattr(app_context_storage, 'context'):
        return app_context_storage.context.request_id
    return None


def _get_request_id_from_flask_request():
    request_id = 'undefined'
    try:
        return flask_request.environ.get(
            'HTTP_X_REQUEST_GUID', request_id
        )
    except RuntimeError:
        # Indicates we are working outside a request context,
        # meaning this is either a test or a command.
        # Nothing to do here!
        return request_id


def _get_request_origin_from_flask_request():
    request_origin = 'undefined'
    try:
        return flask_request.environ.get(
            'HTTP_X_REQUEST_ORIGIN', request_origin
        )
    except RuntimeError:
        # Indicates we are working outside a request context,
        # meaning this is either a test or a command.
        # Nothing to do here!
        return request_origin


@celeryd_after_setup.connect
def setup_worker(sender, instance, **kwargs):
    """
    Setup worker pre reqs
    """
    with flask_app.app_context():
        download_chromium()


def setup_celery_flask_app(app):
    """
    Set flask app to a global variable


    To make sure binaries are only downloaded on celery workers
    we use celery worker signal celeryd_after_setup which doesn't
    have access to flask app context, However logger and S3Lib needs flask app context

    Quick way to pass the context is setting flask app to a global var
    and access it in signal

    todo explore better solution to this issue as this is anti pattern
    """
    global flask_app  # skipcq: PYL-W0603
    flask_app = app


def init_celery(app):
    _configure_celery(app)
    setup_celery_logging(celery)
    setup_celery_flask_app(app)
    return celery
