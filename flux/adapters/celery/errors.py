from datetime import datetime, timedelta


RETRY_MSG = 'Async task failed - triggering retry.'


class FluxAsyncRetryException(Exception):
    """
    A container to pass retry information to celery.
    Note: setting max_retries to None will result in default retries number.
    """
    def __init__(self, *args, retry_reason=None, delay=None, countdown=None, max_retries=None,
                 **kwargs):
        eta = None
        if delay is not None:
            eta = datetime.utcnow() + timedelta(seconds=delay)
        self.retry_reason = retry_reason if retry_reason else RETRY_MSG
        self.eta = eta
        self.countdown = countdown
        self.max_retries = max_retries
        self.additional_context = kwargs
        super().__init__(*args)
