import time
from contextlib import contextmanager

from celery.signals import (
    after_task_publish, task_prerun, task_retry, task_success, task_failure
)

from flux.adapters.logging import get_metrics_logger

metrics = get_metrics_logger()


@contextmanager
def timed_task(task_name):
    start = time.time()
    yield
    elapsed_time = time.time() - start
    metrics.log(task_name, duration_secs=elapsed_time)


@after_task_publish.connect
def task_sent_handler(sender=None, *args, **kwargs):
    # Sender is the name of the task being sent.
    metrics.log(sender, published=1)


@task_prerun.connect
def task_received_handler(sender=None, *args, **kwargs):
    # Sender is the task object being executed.
    metrics.log(sender.name, received=1)


@task_retry.connect
def task_retry_handler(sender=None, *args, **kwargs):
    # Sender is the task object being executed.
    metrics.log(sender.name, retried=1)


@task_success.connect
def task_success_handler(sender=None, *args, **kwargs):
    # Sender is the task object being executed.
    metrics.log(sender.name, succeed=1)


@task_failure.connect
def task_failure_handler(sender=None, *args, **kwargs):
    metrics.log(sender.name, failed=1)
