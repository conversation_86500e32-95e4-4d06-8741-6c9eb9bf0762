import uuid
from typing import List

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from sqlalchemy import func, and_, case
from sqlalchemy.sql.expression import text

from flux.utils import timeutils

from flux.adapters.db import db
from flux.adapters.logging import get_metrics_logger, get_logger

from flux.domains.common import EntityTypes
from flux.domains.event import config
from flux.domains.event.analytics.adapter import AnalyticsAdapter
from flux.domains.event.analytics.entities import (
    Collections,
    GroupingPeriods,
    EventContextTypeAndIDRequried,
)
from flux.domains.event.analytics.exceptions import IncompatibleEntityType, InvalidEntityId
from flux.domains.event.checkins.models import EventCheckins
from flux.domains.event.checkins.entities import CheckinAction, CheckinType

from .analytics_common import ENTITY_TYPE_TO_ENTITY_FUNC

logger = get_logger(__name__)
metrics = get_metrics_logger()


CHECKINS_MODEL_MAP = {
    'events': EventCheckins.event_id,
    'sessions': EventCheckins.session_id,
}

GROUPING_PERIOD = {
    GroupingPeriods.hour: '%Y-%m-%dT%H',
    GroupingPeriods.day: '%Y-%m-%d',
    GroupingPeriods.month: '%Y-%m',
    GroupingPeriods.year: '%Y'
}

GROUPING_PERIOD_TO_TIME_DELTA = {
    GroupingPeriods.hour: timedelta(hours=1),
    GroupingPeriods.day: timedelta(days=1),
    GroupingPeriods.month: relativedelta(months=1),
    GroupingPeriods.year: relativedelta(years=1)
}


class LocalAnalyticsAdapter(AnalyticsAdapter):

    def __init__(self, context, event_id=None, organization_id=None):
        if not event_id:
            raise EventContextTypeAndIDRequried
        self.context = context
        self.event_id = event_id
        self.cc = config.ConfigCursor(self.context.ud, event_id)
        self.timezone = self.cc.get('timezone')

    def get_ad_impressions(self, start_date, end_date):
        raise NotImplementedError

    def get_ad_clicks(self, start_date, end_date):
        raise NotImplementedError

    def delete_analytics_data(self, event_id: int = None, user_id: int = None):
        raise NotImplementedError

    def get_unique_users(self, start_date, end_date):
        raise NotImplementedError

    def get_app_usage(self, start_date, end_date):
        raise NotImplementedError

    def get_top_modules(self, start_date, end_date):
        raise NotImplementedError

    def get_mobile_breakdown(self, start_date, end_date):
        raise NotImplementedError

    def get_top_documents(self, start_date, end_date):
        raise NotImplementedError

    def get_top_external_links(self, start_date, end_date):
        raise NotImplementedError

    def create_analytics_data(self, data):
        raise NotImplementedError

    def execute_analytics_operation(self, collection: Collections,
                                    start_datetime: datetime, end_datetime: datetime,
                                    limit: int = None, offset: int = None,
                                    grouping_period: GroupingPeriods = None,
                                    sort: str = None, group_by: List[str] = None, **params):
        """
        This function assumes that the incoming datetimes are naive but represent the timezone of
        the event. Since checkins are saved in UTC time, the queries will be adjusted based on the
        event's timezone. A UTC timezone will be assumed if no event timezone is present.
        """
        group_by_str = group_by[0] if group_by else None

        entity_type_args = params.get('entity_type')
        entity_type = entity_type_args[0] if entity_type_args else None

        entity_id_args = params.get('entity_id')
        entity_id = entity_id_args[0] if entity_id_args else None

        if entity_type:
            try:
                CheckinType[entity_type]
            except KeyError as e:
                raise IncompatibleEntityType from e

        # Incoming datetimes are assumed to be naive. We must strip any timezone info from them
        start_datetime = timeutils.convert_datetime_to_offset_naive(start_datetime)
        end_datetime = timeutils.convert_datetime_to_offset_naive(end_datetime)

        # Calculate timezone offset to adjust the timesteps
        tz_offset = timeutils.get_utc_offset(start_datetime, self.timezone)

        if entity_id:
            if entity_id.isdigit():
                entity_id = int(entity_id)
            else:
                try:
                    entity_id = uuid.UUID(entity_id)
                except (TypeError, ValueError, AttributeError) as e:
                    raise InvalidEntityId from e

        total_checkins = func.sum(
            case(
                (EventCheckins.action == CheckinAction.checkin, 1),
                else_=0
            ).label('total_checkins')
        )
        total_checkouts = func.sum(
            case(
                (EventCheckins.action == CheckinAction.checkout, 1),
                else_=0
            ).label('total_checkouts')
        )
        unique_checkins = func.count(
            case(
                (EventCheckins.action == CheckinAction.checkin, EventCheckins.people_id)
            ).distinct().label('unique_people_checkins')
        )
        unique_checkouts = func.count(
            case(
                (EventCheckins.action == CheckinAction.checkout, EventCheckins.people_id)
            ).distinct().label('unique_people_checkouts')
        )

        query_entities = [
            total_checkins,
            total_checkouts,
            unique_checkins,
            unique_checkouts,
        ]
        query_group_by = []  # type: ignore
        query = EventCheckins.query.filter(
            and_(
                EventCheckins.event_id == self.event_id,
                EventCheckins.checkin_at >= start_datetime,
                EventCheckins.checkin_at < end_datetime,
            )
        )

        if grouping_period:
            # Timeline query
            query = self._build_timeline_query(
                query,
                query_entities,
                query_group_by,
                group_by_str,
                grouping_period,
                entity_type,
                entity_id,
                tz_offset,
            )
        else:
            # Count query
            query = self._build_count_query(
                query, query_entities, query_group_by, group_by_str, entity_type, entity_id
            )

        query = self._sort_and_limit(query, sort, limit, total_checkins)
        results = query.all()

        if grouping_period:
            self._convert_strings_to_datetimes(results, grouping_period)
            self._adjust_datetimes_to_include_timezone(results, grouping_period, tz_offset)
            expanded_results = self._expand_timeseries_results(
                results,
                tz_offset,
                grouping_period,
                start_datetime,
                end_datetime,
                group_by_str,
                entity_type,
                entity_id,
                limit
            )
        else:
            expanded_results = self._expand_count_results(results, group_by_str, entity_type)
        return expanded_results

    @staticmethod
    def _get_timeline_date_function_by_database(grouping_period, tz_offset):
        """
        Sets the date function depending on the database. Unit tests currently use SQLite and
        production uses MySQL. This requires strftime and date_format respectively.

        One major difference between strftime and date_format is the order of arguments. Although
        they share formatting styles, strftime puts the format string first and date_format puts
        it second.

        Another difference is the way that SQLite and MySQL adjust the grouped datetimes. SQLite
        uses DATETIME with a string-based adjustment. MySQL uses timestampadd with a specified
        interval.
        """
        # Set the date function depending on the database that is used
        grouping_format_str = GROUPING_PERIOD[grouping_period]
        tz_offset_hours = int(tz_offset.total_seconds() // 3600)

        if db.engine.dialect.name == 'sqlite':
            if grouping_period == GroupingPeriods.hour:
                date_function = func.strftime(grouping_format_str, EventCheckins.checkin_at)
            else:
                # Offset the results by the timezone so they will be in the correct grouping period
                # Sqlite uses a string-based offset (i.e. '-6 hours')
                sqlite_tz_offset_string = f'{tz_offset_hours} hours'
                date_function = func.strftime(
                    grouping_format_str,
                    func.DATETIME(EventCheckins.checkin_at, sqlite_tz_offset_string)
                )
        else:
            if grouping_period == GroupingPeriods.hour:
                date_function = func.date_format(EventCheckins.checkin_at, grouping_format_str)
            else:
                # Offset the results by the timezone so they will be in the correct grouping period
                date_function = func.date_format(
                    func.timestampadd(
                        text('hour'), tz_offset_hours, EventCheckins.checkin_at
                    ),
                    grouping_format_str,
                )

        return date_function

    def _build_timeline_query(
        self,
        query,
        query_entities,
        query_group_by,
        group_by,
        grouping_period,
        entity_type,
        entity_id,
        tz_offset,
    ):
        """
        Builds the timeline query. This requires grouping by date strings, which are handled
        differently in SQLite (testing database) and MySQL (production/staging database).

        This limitation means that the unit tests do not test the query as it runs in production.
        The assumption is that the query result will be equivalent between both databases, so the
        rest of the usecase can be tested.

        Revisions should be made carefully.
        """
        date_function = self._get_timeline_date_function_by_database(grouping_period, tz_offset)

        query_entities.append(date_function)
        query_group_by.append(date_function)
        subquery = EventCheckins.query.with_entities(date_function).group_by(
            date_function).subquery()
        query = query.filter(date_function.in_(subquery))

        # Allow timeline for one type of checkin (events or sessions)
        if entity_type:
            model_attr = CHECKINS_MODEL_MAP[entity_type]
            if entity_type == 'sessions':
                query = query.filter(EventCheckins.session_id.isnot(None))
            else:
                query = query.filter(EventCheckins.session_id.is_(None))

            if entity_id:
                query = query.filter(model_attr == entity_id)

            if group_by:
                query_entities.extend([EventCheckins.event_id, EventCheckins.session_id])
                query_group_by.extend([EventCheckins.event_id, EventCheckins.session_id])

        query = query.with_entities(*query_entities)
        query = query.group_by(*query_group_by)
        return query

    @staticmethod
    def _build_count_query(
        query, query_entities, query_group_by, group_by, entity_type, entity_id
    ):
        if entity_type:
            model_attr = CHECKINS_MODEL_MAP[entity_type]
            if entity_type == 'sessions':
                query = query.filter(EventCheckins.session_id.isnot(None))
            else:
                query = query.filter(EventCheckins.session_id.is_(None))

            if entity_id:
                query = query.filter(model_attr == entity_id)

            if group_by:
                query_entities.extend([EventCheckins.event_id, EventCheckins.session_id])
                query_group_by.extend([EventCheckins.event_id, EventCheckins.session_id])

        query = query.with_entities(*query_entities)
        query = query.group_by(*query_group_by)
        return query

    @staticmethod
    def _sort_and_limit(query, sort, limit, target):
        # Sort and limit must be done last (after all filtering, etc.) and prior to calling all()
        if sort == '-checkins':
            query = query.order_by(target.desc())
        elif sort == 'checkins':
            query = query.order_by(target.asc())

        if limit:
            query = query.limit(limit)
        return query

    @staticmethod
    def _convert_strings_to_datetimes(results, grouping_period):
        """
        Grouping by checkin_at with strftime means that we are now dealing with strings instead of
        datetimes. We need to convert them back so that we can calculate start/end times for our
        grouping periods.
        """
        try:
            grouping_str = GROUPING_PERIOD[grouping_period]
        except KeyError:
            return

        for i, row in enumerate(results):
            try:
                row = list(row)
                # Datetime will always be at position 4:
                # (checkins, checkouts, unique_people_checkins, unique_people_checkouts, datetime,
                # optional)
                row[4] = datetime.strptime(row[4], grouping_str)
                results[i] = tuple(row)
            except (TypeError, ValueError):
                pass

    @staticmethod
    def _adjust_datetimes_to_include_timezone(results, grouping_period, tz_offset):
        """
        The queried results datetimes are matched to expected timesteps to populate the response
        data. For these matches to work they must also have the timezone offset. This hourly
        offset is added for daily, monthly, and yearly grouping periods. This adjustment only
        applies to timeline data.
        """
        if grouping_period != GroupingPeriods.hour:
            for i, row in enumerate(results):
                row = list(row)
                # Datetime will always be at position 4:
                # (checkins, checkouts, unique checkins, unique checkouts, datetime, optional)
                row[4] = row[4] - tz_offset
                results[i] = tuple(row)

    @staticmethod
    def _increment_time(start, grouping_period):
        try:
            return start + GROUPING_PERIOD_TO_TIME_DELTA[grouping_period]
        except KeyError:
            return None

    @staticmethod
    def _get_timesteps(start_datetime, end_datetime, grouping_period, tz_offset):
        """
        Creates timesteps based on the specified grouping period. Daily, monthly, and yearly
        timesteps will be based off of midnight in the local timezone, adjusted to UTC. This
        requires adjustments for both Eastern and Western timezones.
        For example:

        Example #1 (local midnight start/end times):
        Timezone: EST (UTC-4)
        Start datetime: Jan 1, 04:00
        End datetime: Jan 2, 04:00
        Daily timesteps: Jan 1 04:00 to Jan 2 04:00

        Example #2 (non-midnight start/end times):
        Timezone EST (UTC-4)
        Start datetime: Jan 1, 02:31
        End datetime: Jan 3, 00:57
        Timesteps:
            Jan 1, 02:31 to Jan 1, 04:00
            Jan 1, 04:00 to Jan 2, 04:00
            Jan 2: 04:00 to Jan 3, 00:57

        The same start/end pattern applies to monthly and yearly timesteps. Daylight savings times
        is assumed to be present/absent for the entire time period based on the start date.
        """
        timesteps = []
        if grouping_period == GroupingPeriods.hour:
            clean_start = start_datetime.replace(microsecond=0, second=0, minute=0)
            clean_end = end_datetime.replace(microsecond=0, second=0, minute=0)
            clean_end = clean_end if clean_end == end_datetime else clean_end + timedelta(hours=1)
            timesteps = [
                clean_start + timedelta(hours=i) for i in range(
                    0, int((clean_end - clean_start).total_seconds()) // 3600
                )
            ]
        elif grouping_period == GroupingPeriods.day:
            clean_start = start_datetime.replace(
                microsecond=0, second=0, minute=0, hour=0
            ) - tz_offset
            clean_end = end_datetime.replace(microsecond=0, second=0, minute=0, hour=0) - tz_offset
            clean_end = clean_end if clean_end >= end_datetime else clean_end + timedelta(days=1)
            # Edge case for some eastern timezones
            if clean_end < end_datetime:
                clean_end = clean_end + timedelta(days=1)
            timesteps = [
                clean_start + timedelta(days=i) for i in range(
                    0, int((clean_end - clean_start).total_seconds()) // 86400
                )
            ]
        elif grouping_period == GroupingPeriods.month:
            clean_start = start_datetime.replace(
                microsecond=0, second=0, minute=0, hour=0, day=1
            ) - tz_offset
            clean_end = end_datetime.replace(
                microsecond=0, second=0, minute=0, hour=0, day=1
            ) - tz_offset
            clean_end = clean_end if clean_end >= end_datetime else clean_end + relativedelta(
                months=1
            )
            # Edge case for some eastern timezones
            if clean_end < end_datetime:
                clean_end = clean_end + relativedelta(months=1)
            months_between = ((clean_end.year - clean_start.year) * 12) + (
                clean_end.month - clean_start.month
            )
            timesteps = [clean_start + relativedelta(months=i) for i in range(0, months_between)]
        elif grouping_period == GroupingPeriods.year:
            clean_start = start_datetime.replace(
                microsecond=0, second=0, minute=0, hour=0, day=1, month=1
            ) - tz_offset
            clean_end = end_datetime.replace(
                microsecond=0, second=0, minute=0, hour=0, day=1, month=1
            ) - tz_offset
            clean_end = clean_end if clean_end >= end_datetime else clean_end + relativedelta(
                years=1
            )
            # Edge case for some eastern timezones
            if clean_end < end_datetime:
                clean_end = clean_end + relativedelta(years=1)
            timesteps = [
                clean_start + relativedelta(years=i) for i in range(
                    0, int(clean_end.year - clean_start.year)
                )
            ]
        return timesteps

    def _expand_timeseries_results(
        self,
        results,
        tz_offset,
        grouping_period,
        start_datetime,
        end_datetime,
        group_by,
        entity_type,
        specified_entity_id,
        limit,
    ):
        entity_id_map = self._get_entity_id_map(
            results, group_by, entity_type, specified_entity_id
        )
        results_map = self._get_results_map(results)
        timesteps = self._get_timesteps(start_datetime, end_datetime, grouping_period, tz_offset)

        # Pad all timesteps with zeros if they don't have counts returned by SQL in results_map
        padded_results = []
        for i, timestep in enumerate(timesteps):
            if timestep in results_map:
                row_data = self._format_existing_row_result(
                    results_map, entity_id_map, timestep, group_by, entity_type
                )
            else:
                row_data = self._create_empty_row_result(
                    entity_id_map, group_by, entity_type, specified_entity_id, limit
                )

            row_end_datetime = self._increment_time(timestep, grouping_period)
            row_end_datetime = min(row_end_datetime, end_datetime)
            padded_results.append(
                {
                    'start_datetime': start_datetime if i == 0 else timestep,
                    'end_datetime': row_end_datetime,
                    'data_points': sorted(
                        row_data,
                        key=lambda rd: rd['value']['checkins'] + rd['value']['checkouts'],
                        reverse=True,
                    )
                }
            )
        return padded_results

    def _expand_count_results(self, results, group_by, entity_type):
        ids = set()
        entity_id_map = {}
        if group_by and entity_type:
            entity_type_enum = EntityTypes[entity_type]
            if entity_type_enum == EntityTypes.events:
                ids.add(self.event_id)
            elif entity_type_enum == EntityTypes.sessions:
                for row in results:
                    if row[-1] not in ids:
                        ids.add(row[-1])

            entity_id_map = ENTITY_TYPE_TO_ENTITY_FUNC[entity_type_enum](
                self.context, self.event_id, ids
            )

        combined_results = []
        for row in results:
            row_data = {
                'value': {
                    'checkins': int(row[0]) if row[0] else 0,
                    'checkouts': int(row[1]) if row[1] else 0,
                    'unique_people_checkins': int(row[2]) if row[2] else 0,
                    'unique_people_checkouts': int(row[3]) if row[3] else 0,
                }
            }

            if group_by and entity_type:
                # Results will have two extra values: event ID and session ID. If we specified an
                # entity_type, then we are also filtering based on that entity_type such that we
                # will only have events or sessions. This allows us to ignore the None values.
                # Example:
                # [checkins, checkouts, unique_people_checkins, unique_people_checkouts, event_id,
                # session_id]
                # Session row: (5, 3, 2, 1, 12345, UUID(...))
                # Event row: (2, 0, 2, 0, 12345, None)
                entity_id = row[5] if row[5] else row[4]
                row_data['entity'] = entity_id_map[entity_id]

            combined_results.append(row_data)
        return combined_results

    def _get_entity_id_map(self, results, group_by, entity_type, specified_entity_id):
        ids = set()
        entity_id_map = {}
        if group_by and entity_type:
            entity_type_enum = EntityTypes[entity_type]
            if entity_type_enum == EntityTypes.events:
                ids.add(self.event_id)
            elif entity_type_enum == EntityTypes.sessions:
                for row in results:
                    if row[-1] not in ids:
                        ids.add(row[-1])
            if specified_entity_id and specified_entity_id not in ids:
                ids.add(specified_entity_id)

            entity_id_map = ENTITY_TYPE_TO_ENTITY_FUNC[entity_type_enum](
                self.context, self.event_id, ids
            )

        return entity_id_map

    @staticmethod
    def _get_results_map(results):
        """
        Convert the list of results returned by the database query into a dictionary for more
        efficient lookup. Multiple results with the same timestamp are saved in the dictionary as a
        list of tuples.

        The result list has the form:
        [
            checkins, checkouts, unique_people_checkins, unique_people_checkouts, timestamp,
            event_id (optional), entity_id (optional)
        ]

        The returned dictionary has the form:
        {timestamp: [
            (checkins, checkouts, unique_people_checkins, unique_people_checkouts,
            event_id (optional), entity_id (optional), (...)
            ]
        }
        """
        results_map = {}
        for result in results:
            if result[4] in results_map:
                results_map[result[4]].append(
                    (result[0], result[1], result[2], result[3], *result[5:])
                )
            else:
                results_map[result[4]] = [
                    (result[0], result[1], result[2], result[3], *result[5:])
                ]
        return results_map

    @staticmethod
    def _format_existing_row_result(results_map, entity_id_map, timestep, group_by, entity_type):
        """
        Creates a formatted row of data from an existing result in the results map.
        """
        if group_by and entity_type:
            # Results will have two extra values: event ID and session ID. If we specified
            # an entity_type, then we are also filtering based on that entity_type such
            # that we will only have events or sessions. This allows us to ignore the None
            # values at position 5.
            row_data = []
            used_entities = set()
            for result in results_map[timestep]:
                entity_id = result[5] if result[5] else result[4]
                used_entities.add(entity_id)
                row_data.append(
                    {
                        'value': {
                            'checkins': int(result[0]) if result[0] else 0,
                            'checkouts': int(result[1]) if result[1] else 0,
                            'unique_people_checkins': int(result[2]) if result[2] else 0,
                            'unique_people_checkouts': int(result[3]) if result[3] else 0,
                        },
                        'entity': entity_id_map.get(entity_id)
                    }
                )
        else:
            row_data = [
                {
                    'value': {
                        'checkins': int(results_map[timestep][0][0]),
                        'checkouts': int(results_map[timestep][0][1]),
                        'unique_people_checkins': int(results_map[timestep][0][2]),
                        'unique_people_checkouts': int(results_map[timestep][0][3]),
                    }
                }
            ]

        return row_data

    @staticmethod
    def _create_empty_row_result(entity_id_map, group_by, entity_type, specified_entity_id, limit):
        """
        Creates an empty, formatted row of data when there is no corresponding result returned from
        the SQL query. This padded format matches the expected timeline analytics format returned
        by the analytics adapter.
        """
        if group_by and entity_type:
            if specified_entity_id:
                row_data = [
                    {
                        'value': {
                            'checkins': 0,
                            'checkouts': 0,
                            'unique_people_checkins': 0,
                            'unique_people_checkouts': 0,
                        },
                        'entity': entity_id_map.get(specified_entity_id),
                    }
                ]
            else:
                row_data = [
                               {
                                   'value': {
                                       'checkins': 0,
                                       'checkouts': 0,
                                       'unique_people_checkins': 0,
                                       'unique_people_checkouts': 0,
                                   },
                                   'entity': entity_data,
                               } for entity_data in entity_id_map.values()
                           ][:limit]
        else:
            row_data = [
                {
                    'value': {
                        'checkins': 0,
                        'checkouts': 0,
                        'unique_people_checkins': 0,
                        'unique_people_checkouts': 0,
                    }
                }
            ]

        return row_data
