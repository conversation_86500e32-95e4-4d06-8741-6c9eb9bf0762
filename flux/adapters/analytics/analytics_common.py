from typing import Dict, Callable

from flux.domains.common import EntityTypes
from flux.domains.event import (
    companies,
    discussions,
    events,
    maps,
    people,
    sessions,
    virtual_meetings,
    announcements,
)


def _get_section_entities(context, event_id, ids):
    data = context.ud.sections.read({
        'ids': ids,
    })
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.sections.name,
            'section_type': d.type,
        } for d in data
    }
    return entity_map


def _get_session_entities(context, event_id, ids):
    data = sessions.get_sessions(
        context,
        event_id=event_id,
        id=ids,
    )
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.sessions.name,
        } for d in data
    }
    return entity_map


def _get_company_entities(context, event_id, ids):
    data = companies.get_by_event(
        context,
        event_id=event_id,
        id=ids,
    )
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.companies.name,
        } for d in data
    }
    return entity_map


def _get_people_entities(context, event_id, ids):
    data = people.get(
        context,
        event_id=event_id,
        id=ids,
    )
    entity_map = {
        d.id: {
            'id': d.id,
            'name': f'{d.first_name} {d.last_name}',
            'email': d.email,
            'type': EntityTypes.people.name,
        } for d in data
    }
    return entity_map


def _get_user_entities(context, event_id, ids):
    data = people.get(
        context,
        event_id=event_id,
        user_id=ids,
    )
    entity_map = {
        d.id: {
            'id': d.id,
            'name': f'{d.first_name} {d.last_name}',
            'email': d.email,
            'type': EntityTypes.people.name,
        } for d in data
    }
    return entity_map


def _get_document_entities(context, event_id, ids):
    documents = context.ud.documents.read({
        'ids': ids,
        'event_id': event_id,
    })
    if not documents:
        return {}

    files = context.ud.files.read({
        'ids': [d.file_id for d in documents],
    })
    file_id_to_name_map = {f.id: f.filename for f in files}

    entity_map = {
        d.id: {
            'id': d.id,
            'name': file_id_to_name_map.get(d.file_id),
            'type': EntityTypes.documents.name,
        } for d in documents
    }
    return entity_map


def _get_external_link_entities(context, event_id, ids):
    data = context.ud.external_links.read({
        'ids': ids,
        'event_id': event_id,
    })
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.external_links.name,
            'url': d.link,
        } for d in data
    }
    return entity_map


def _get_banner_ad_entities(context, event_id, ids):
    event_app_id = context.ud.event_apps.single({'event_id': event_id}).id
    data = context.ud.eventapp_banner_ads.read({
        'ids': ids,
        'event_app_id': event_app_id,
    })
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.banner_ads.name,
            'url': d.url,
            'company_id': str(d.company_id) if d.company_id else None,
        } for d in data
    }
    return entity_map


def _get_map_entities(context, event_id, ids):
    data = maps.get_by_event(context, event_id=event_id, id=ids)
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.maps.name,
        } for d in data
    }
    return entity_map


def _get_group_discussion_entities(context, event_id, ids):
    data = discussions.get_by_event(context, event_id=event_id, id=ids)
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.group_discussions.name,
        } for d in data
    }
    return entity_map


def _get_event_survey_entities(context, event_id, ids):
    data = context.ud.ars_question_group.read({
        'ids': ids,
        'event_id': event_id,
    })
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.event_surveys.name,
        } for d in data
    }
    return entity_map


def _get_virtual_meeting_entities(context, event_id, ids):
    data = virtual_meetings.get(context, event_id=event_id, id=ids)
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.name,
            'type': EntityTypes.virtual_meetings.name,
            'virtual_meeting_type': d.type.name,
        } for d in data
    }
    return entity_map


def _get_event_entities(context, event_id, ids):
    # We only need to query one event. The ids arg is to be consistent with the function mapping
    data = events.get_event_by_id(
        context,
        event_id=event_id,
    )
    entity_map = {
        event_id: {
            'id': data.get('id'),
            'type': EntityTypes.events.name,
            'name': data.get('name'),
        }
    }
    return entity_map


def _get_announcement_entities(context, event_id, ids):
    data = announcements.get_by_event(
        context,
        event_id=event_id,
        id=ids,
    )
    entity_map = {
        d.id: {
            'id': d.id,
            'name': d.title,
            'type': EntityTypes.announcements.name,
        } for d in data
    }
    return entity_map

ENTITY_TYPE_TO_ENTITY_FUNC: Dict[EntityTypes, Callable] = {
    EntityTypes.sections: _get_section_entities,
    EntityTypes.sessions: _get_session_entities,
    EntityTypes.companies: _get_company_entities,
    EntityTypes.people: _get_people_entities,
    EntityTypes.documents: _get_document_entities,
    EntityTypes.external_links: _get_external_link_entities,
    EntityTypes.banner_ads: _get_banner_ad_entities,
    EntityTypes.maps: _get_map_entities,
    EntityTypes.group_discussions: _get_group_discussion_entities,
    EntityTypes.event_surveys: _get_event_survey_entities,
    EntityTypes.virtual_meetings: _get_virtual_meeting_entities,
    EntityTypes.events: _get_event_entities,
    EntityTypes.user_accounts: _get_user_entities,
    EntityTypes.announcements: _get_announcement_entities,
}
