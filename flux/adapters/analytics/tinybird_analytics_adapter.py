import time
from collections import defaultdict, deque
from datetime import datetime, timed<PERSON>ta
from typing import List

from flux.adapters.clients.tinybird import tinybird_client
from flux.adapters.logging import get_metrics_logger, get_logger
from flux.apps.config import app_config
from flux.domains.common import (
    EntityTypes,
    cast_and_clean_ids_by_entity_type,
    cast_and_clean_id_by_entity_type,
    EntityIdTypeIsUndefinedError,
    ProductTypes,
    ContextBoundaryTypes
)
from flux.domains.event import (config, people, videos)
from flux.domains.event.videos.entities import Video
from flux.domains.event.analytics.adapter import AnalyticsAdapter
from flux.domains.event.analytics.entities import (
    Collections,
    GroupingPeriods,
    EventContextTypeAndIDRequried,
)
from flux.utils import timeutils

from .analytics_common import ENTITY_TYPE_TO_ENTITY_FUNC

# Legacy analytics (primarily relevant for the pdf report), uses specific names in the page
# titles to determine which type of section the data refers to


SECTION_TYPE_TO_LEGACY_PREFIX = {
    'agenda': 'Agenda_({0})',
    'people': 'people_Listing_({0})',
    'companies': 'Exhibitor_Listing_({0})',
    'maps': 'Maps',
    'infobooth': 'Infobooth',
    'alerts': 'Alerts',
    'survey': 'Event_Survey',
    'activity_feed': 'Live_Feeds',
}

ENTITY_TYPE_TO_LEGACY_PREFIX = {
    'companies': 'Exhibitor_{0}',
    'sessions': 'Session_{0}',
}

logger = get_logger(__name__)
metrics = get_metrics_logger()


class TinybirdAnalyticsAdapter(AnalyticsAdapter):

    def __init__(self, context, event_id=None, organization_id=None, client=None):
        super().__init__(context, event_id)
        self.context = context
        self.event_id = event_id
        if self.event_id:
            self.cc = config.ConfigCursor(self.context.ud, event_id)
            self.timezone = self.cc.get('timezone')
        else:
            self.timezone = 'UTC'
        self.organization_id = organization_id
        self.client = client if client else tinybird_client.TinybirdClient()

    def execute_analytics_operation(self, collection: Collections,
                                    start_datetime: datetime, end_datetime: datetime,
                                    limit: int = None, offset: int = None,
                                    grouping_period: GroupingPeriods = None,
                                    sort: str = None, group_by: List[str] = None, **params):

        # people_id filter is not supported in Tinybird
        # This line can be removed once Keen analytics implementation is deleted
        params.pop('people_id', None)
        # Analytics usecase is translating 'people' operation to a 'people_id' groupby
        # For now adapter will still take in people_id and return "person", but will
        # use user_id for communication with Tinybird
        if group_by:
            group_by = [gb.replace('people_id', 'user_id') for gb in group_by]

        tinybird_params = tinybird_client.CommonParameters(
            organization_id=self.organization_id,
            timezone=self.timezone,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            grouping_period=grouping_period,
            limit=limit + 10 if limit else None,  # Fetch extra records in case some are deleted
            sort=sort,
            group_by=group_by,
            offset=offset,
            **params
        )

        results = self.client.analysis_operation(
            collection=collection,
            params=tinybird_params,
        )

        expanded_results = self._expand_groups(results, bool(grouping_period), limit)

        return expanded_results

    def delete_analytics_data(self, event_id: int = None, user_id: int = None):
        """
        Delete analytics data.

        If you wish to delete analytics data for:
        1 - an event, only pass `event_id` param.
        2 - a single user, only pass `user_id` param.
        3 - an event person, pass both `event_id` and `user_id` params.
        """
        self.client.delete_from_analytics_input_datasource(
            organization_id=self.organization_id,
            event_id=event_id,
            user_id=user_id,
        )

        for collection in Collections:

            # Wait 5 seconds before making next delete call to avoid rate limit error
            # And we don't want to delay local env or unit tests
            if not app_config.is_dev_environment():
                time.sleep(5)

            self.client.delete_from_collection(
                collection,
                organization_id=self.organization_id,
                event_id=event_id,
                user_id=user_id,
            )

    def create_analytics_data(self, data):
        """
        append a new data to datasource.
        """
        self.client.append_new_analytics_data_to_datasource(
            data=data,
        )

    def get_unique_users(self, start_date, end_date):
        if not self.event_id:
            raise EventContextTypeAndIDRequried
        start_datetime, end_datetime = self._date_to_datetime(start_date, end_date)
        tinybird_params = tinybird_client.CommonParameters(
            organization_id=self.organization_id,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            timezone=self.timezone,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        )
        results = self.client.analysis_operation(
            collection=Collections.pageviews,
            params=tinybird_params,
        )
        return results[0]['value']['unique_users']

    def get_app_usage(self, start_date, end_date):
        if not self.event_id:
            raise EventContextTypeAndIDRequried
        start_datetime, end_datetime = self._date_to_datetime(start_date, end_date)
        # `start_datetime` and `end_datetime` here are UTC datetimes with same starting and
        # ending time (start_datetime='2023-05-07T22:00:00', end_datetime='2023-05-014T22:00:00')
        # Now if there are analytics events in the last second of `end_datetime`, they are
        # returned in a different date (out of daterange). That does not play nice with the
        # zero values padding solution on dates that didn't have analytics events in EXP-12883
        end_datetime = end_datetime - timedelta(seconds=1)

        tinybird_params = tinybird_client.CommonParameters(
            organization_id=self.organization_id,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            timezone=self.timezone,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            grouping_period=GroupingPeriods.day,
        )
        logger.info(f'app_usage_params {start_datetime}, {end_datetime}')

        records = self.client.analysis_operation(
            collection=Collections.pageviews,
            params=tinybird_params,
        )
        sum_total_views = sum(r['data_points'][0]['value']['total_views'] for r in records)

        # Tinybird does not return empty values, which the frontend normally handles, however
        # for the analytics pdf chart we need them to be padded out so that all days have a
        # record, to be consistent with the KeenAnalyticsAdapter we'll do that here.

        # Make a datestring (e.x. 20201122 to represent each day in the time range)
        num_days = (end_date - start_date).days + 1  # add 1 to be inclusive of last day
        datestrings = [
            (start_date + timedelta(days=i)).strftime('%Y%m%d') for i in range(num_days)
        ]
        # Create a mapping by datestr of empty values for each day
        results_dict = {
            datestr: {
                'date': datestr,
                'page_views': 0,
                'unique_visitors': 0,
            } for datestr in datestrings
        }

        # Iterate through the results, overriding the empty days with real data when we have it
        for r in records:
            dt = timeutils.convert_datetime_utc_to_tz(r['start_datetime'], tz=self.timezone)
            datestr = dt.strftime('%Y%m%d')
            results_dict[datestr]['page_views'] = r['data_points'][0]['value']['total_views']
            results_dict[datestr]['unique_visitors'] = r['data_points'][0]['value']['unique_users']

        result = {
            'total_page_views': sum_total_views,
            'app_usage': list(results_dict.values()),
        }

        # Analytics PDF Report Edge-case #2: If the analytics data was collected for a single day,
        # say from 13 March 2023 to 13 March 2023, there will be only a single data point
        # in app_usage. This will not render on the PDF Chart as it requires more than one
        # data point. So a workaround this is to pad 0 `page_views` and `unique_visitors` on
        # the SAME DATE before and after the actual data point. See EXP-13137 for more.
        if len(result['app_usage']) == 1:
            # Create a deque to PAD extra values at end and start in O(1)
            app_usage_deque = deque(result['app_usage'])

            # Append at the start
            app_usage_deque.appendleft({
                'date': result['app_usage'][0]['date'],
                'page_views': 0,
                'unique_visitors': 0,
            })

            # Append at the end
            app_usage_deque.append({
                'date': result['app_usage'][0]['date'],
                'page_views': 0,
                'unique_visitors': 0,
            })

            # Override the `app_usage` in `result`
            result['app_usage'] = list(app_usage_deque)

        return result

    def get_top_modules(self, start_date, end_date):
        if not self.event_id:
            raise EventContextTypeAndIDRequried
        start_datetime, end_datetime = self._date_to_datetime(start_date, end_date)
        top_sections = self.execute_analytics_operation(
            collection=Collections.pageviews,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            group_by=['entity_id'],
            entity_type=['sections'],
            sort='-total_views',
        )

        results = []
        for r in top_sections:
            title = SECTION_TYPE_TO_LEGACY_PREFIX.get(
                r['entity']['section_type'],
                r['entity']['name'],
            )
            title = title.format(r['entity']['name'])
            results.append({
                'page_titles': title,
                'page_views': r['value']['total_views'],
                'unique_visitors': r['value']['unique_users'],
            })

        top_sessions = self.execute_analytics_operation(
            collection=Collections.pageviews,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            group_by=['entity_id'],
            entity_type=['sessions'],
            sort='-total_views',
        )
        for r in top_sessions:
            title = ENTITY_TYPE_TO_LEGACY_PREFIX['sessions'].format(r['entity']['name'])
            results.append({
                'page_titles': title,
                'page_views': r['value']['total_views'],
                'unique_visitors': r['value']['unique_users'],
            })

        top_companies = self.execute_analytics_operation(
            collection=Collections.pageviews,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            group_by=['entity_id'],
            entity_type=['companies'],
            sort='-total_views',
        )
        for r in top_companies:
            title = ENTITY_TYPE_TO_LEGACY_PREFIX['companies'].format(r['entity']['name'])
            results.append({
                'page_titles': title,
                'page_views': r['value']['total_views'],
                'unique_visitors': r['value']['unique_users'],
            })

        return results

    def get_mobile_breakdown(self, start_date, end_date):
        if not self.event_id:
            raise EventContextTypeAndIDRequried
        start_datetime, end_datetime = self._date_to_datetime(start_date, end_date)
        records = self.execute_analytics_operation(
            collection=Collections.pageviews,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            group_by=['device'],
            sort='-unique_users',
        )
        results = []
        for r in records:
            results.append({
                'operating_system': r['device']['os_family'],
                'is_mobile': 'Yes' if r['device']['type'] in (
                    'mobile',
                    'mobile phone',
                    'mobile device',
                    'tablet',
                    'ebook reader',
                ) else 'No',
                'unique_visitors': r['value']['unique_users'],
            })
        return results

    def get_top_documents(self, start_date, end_date):
        if not self.event_id:
            raise EventContextTypeAndIDRequried
        start_datetime, end_datetime = self._date_to_datetime(start_date, end_date)
        records = self.execute_analytics_operation(
            collection=Collections.clicks,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            group_by=['entity_id'],
            entity_type=['documents'],
            sort='-clicks',
            limit=10
        )
        results = []
        for r in records:
            results.append({
                'file_name': r['entity']['name'],
                'clicks': r['value']['clicks'],
            })
        return results

    def get_top_external_links(self, start_date, end_date):
        if not self.event_id:
            raise EventContextTypeAndIDRequried
        start_datetime, end_datetime = self._date_to_datetime(start_date, end_date)
        records = self.execute_analytics_operation(
            collection=Collections.clicks,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            group_by=['entity_id', 'referrer_id'],
            entity_type=['external_links'],
            sort='-clicks',
            limit=10
        )
        results = []
        for r in records:
            results.append({
                'name': r['entity']['name'],
                'link': r['entity']['url'],
                'owner_name': r['referrer']['name'],
                'owner_type': r['referrer']['type'],
                'owner_id': r['referrer']['id'],
                'clicks': r['value']['clicks'],
            })
        return results

    def get_ad_impressions(self, start_date, end_date):
        if not self.event_id:
            raise EventContextTypeAndIDRequried
        start_datetime, end_datetime = self._date_to_datetime(start_date, end_date)
        records = self.execute_analytics_operation(
            collection=Collections.impressions,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            group_by=['entity_id'],
            entity_type=['banner_ads'],
            sort='-impressions',
        )
        results = {r['entity']['id']: r['value']['impressions'] for r in records}
        return results

    def get_ad_clicks(self, start_date, end_date):
        if not self.event_id:
            raise EventContextTypeAndIDRequried
        start_datetime, end_datetime = self._date_to_datetime(start_date, end_date)
        records = self.execute_analytics_operation(
            collection=Collections.clicks,
            product=ProductTypes.event_space,
            context_type=ContextBoundaryTypes.events,
            context_id=str(self.event_id),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            group_by=['entity_id'],
            entity_type=['banner_ads'],
            sort='-clicks',
        )
        results = {r['entity']['id']: r['value']['clicks'] for r in records}
        return results

    def _expand_groups(self, tinybird_results, has_grouping_period, limit):

        raw_entity_type_id_map = defaultdict(set)
        user_ids = set()
        video_ids = set()
        if has_grouping_period:
            for time_period in tinybird_results:
                for dp in time_period['data_points']:
                    if '_group' in dp and dp['_group']:
                        self._extract_entity_id_from_group(dp, raw_entity_type_id_map)
                        self._extract_user_id_from_group(dp, user_ids)
                        self._extract_referrer_id_from_group(dp, raw_entity_type_id_map)
                        self._extract_video_id_from_group(dp, video_ids)
        else:
            for dp in tinybird_results:
                if '_group' in dp and dp['_group']:
                    self._extract_entity_id_from_group(dp, raw_entity_type_id_map)
                    self._extract_user_id_from_group(dp, user_ids)
                    self._extract_referrer_id_from_group(dp, raw_entity_type_id_map)
                    self._extract_video_id_from_group(dp, video_ids)

        # Normalize & Clean Ids
        # - Cast all extracted ids into their expected internal types
        # - Filter out bad ids that do not match our expected format

        user_ids = cast_and_clean_ids_by_entity_type(EntityTypes.user_accounts, user_ids)
        video_ids = cast_and_clean_ids_by_entity_type(EntityTypes.videos, video_ids)
        clean_entity_type_id_map = {}
        for entity_type, entity_ids in raw_entity_type_id_map.items():
            try:
                entity_type_enum = EntityTypes[entity_type]
            except KeyError:
                continue  # If we encounter an unexpected entity type, ignore it and continue

            try:
                cleaned_ids = cast_and_clean_ids_by_entity_type(entity_type_enum, entity_ids)
            except EntityIdTypeIsUndefinedError:
                continue  # If entity type does not have a defined id type, ignore it and continue

            clean_entity_type_id_map[entity_type_enum] = cleaned_ids

        # Get names to expand entity data
        entity_type_to_entity_map = {}
        for entity_type, entity_ids in clean_entity_type_id_map.items():
            if entity_type not in ENTITY_TYPE_TO_ENTITY_FUNC:
                continue  # If we do not have a func to look up the entity, ignore it and continue
            entity_func = ENTITY_TYPE_TO_ENTITY_FUNC[entity_type]
            entity_map = entity_func(self.context, self.event_id, entity_ids)
            entity_type_to_entity_map[entity_type] = entity_map

        # Expand user data to expand user return
        user_map = self._get_user_map(user_ids)
        video_map = self._get_video_map(video_ids)

        if has_grouping_period:
            expanded_results = []
            for time_period in tinybird_results:
                data_points = self._build_expanded_result(
                    data_points=time_period['data_points'],
                    entity_type_to_entity_map=entity_type_to_entity_map,
                    user_map=user_map,
                    video_map=video_map,
                    limit=limit,
                )
                expanded_results.append({
                    'start_datetime': time_period['start_datetime'],
                    'end_datetime': time_period['end_datetime'],
                    'data_points': data_points,
                })
        else:
            expanded_results = self._build_expanded_result(
                data_points=tinybird_results,
                entity_type_to_entity_map=entity_type_to_entity_map,
                user_map=user_map,
                video_map=video_map,
                limit=limit,
            )

        return expanded_results

    def _build_expanded_result(
        self,
        data_points,
        entity_type_to_entity_map,
        user_map,
        video_map,
        limit,
    ):
        expanded_results = []
        for dp in data_points:
            result = {'value': dp['value']}
            if '_group' in dp and dp['_group']:
                if 'entity_type' in dp['_group']:
                    entity_type = dp['_group']['entity_type']
                    entity_id = dp['_group'].get('entity_id')
                    entity = self._lookup_entity(entity_type_to_entity_map, entity_type, entity_id)
                    if not entity:
                        # If we don't find the entity, the data is either malformed, or
                        # belongs to an entity that has been deleted, in either we will skip
                        # this row of data
                        continue
                    result['entity'] = entity

                if 'user_id' in dp['_group']:
                    user_id = dp['_group']['user_id']
                    if user_id not in user_map:
                        # If we don't find the user data, then the id is either misformed, or
                        # belongs to a person that has been deleted, in either we will skip
                        # this row of data
                        continue
                    result['person'] = user_map[user_id]

                if 'email_id' in dp['_group']:
                    result['email'] = {'id': dp['_group']['email_id']}

                if 'clicked_url' in dp['_group']:
                    result['click'] = {'url': dp['_group']['clicked_url']}

                if 'device_type' in dp['_group'] or 'os_family' in dp['_group']:
                    result['device'] = {
                        'type': dp['_group'].get('device_type'),
                        'os_family': dp['_group'].get('os_family'),
                    }

                if 'referrer_type' in dp['_group']:
                    referrer_type = dp['_group']['referrer_type']
                    referrer_id = dp['_group'].get('referrer_id')
                    ref = self._lookup_entity(
                        entity_type_to_entity_map,
                        referrer_type,
                        referrer_id,
                    )
                    if not ref:
                        continue
                    result['referrer'] = ref

                if 'video_id' in dp['_group']:
                    video_id = dp['_group']['video_id']
                    if video_id not in video_map:
                        # If we don't find the video data, the video might not be available in db
                        continue
                    result['video'] = video_map[video_id]

            expanded_results.append(result)
            if limit and len(expanded_results) >= limit:
                break
        return expanded_results

    @staticmethod
    def _extract_user_id_from_group(dp, user_ids):
        if 'user_id' in dp['_group']:
            user_ids.add(dp['_group']['user_id'])

    @staticmethod
    def _extract_video_id_from_group(dp, video_ids):
        if 'video_id' in dp['_group']:
            video_ids.add(dp['_group']['video_id'])

    @staticmethod
    def _extract_entity_id_from_group(dp, entity_type_id_map):
        entity_type = dp['_group'].get('entity_type')
        if entity_type:
            entity_type_id_map[entity_type].add(dp['_group'].get('entity_id'))

    @staticmethod
    def _extract_referrer_id_from_group(dp, entity_type_id_map):
        referrer_type = dp['_group'].get('referrer_type')
        if referrer_type:
            entity_type_id_map[referrer_type].add(dp['_group'].get('referrer_id'))

    @staticmethod
    def _lookup_entity(entity_type_to_entity_map, raw_entity_type, raw_entity_id):
        """ Given an entity_type and entity_id from an analytics row, lookup expanded entity.
        """

        # Note: Data from the analytics row will be in it's raw format, meaning the raw_entity_id
        #       data type is unknown and the data may be malformed. We have to convert these
        #       raw values to our expected internal types before doing the lookup.
        try:
            entity_type_enum = EntityTypes[raw_entity_type]
        except KeyError:
            return None
        entity_id = cast_and_clean_id_by_entity_type(entity_type_enum, raw_entity_id)

        entity = None
        entity_map = entity_type_to_entity_map.get(entity_type_enum)
        if entity_map:
            entity = entity_map.get(entity_id)
        return entity

    def _get_user_map(self, user_ids):
        if not self.event_id:
            raise NotImplementedError(
                'Grouping by user data outside of an event not currently supported.'
            )
        if not user_ids:
            return {}

        people_entities = people.get(
            self.context,
            event_id=self.event_id,
            user_id=user_ids,
            include='groups, profile_image',
        )
        include = {'id', 'external_id', 'first_name', 'last_name', 'email', 'company_name',
                   'groups', 'user_id', 'profile_image'}
        exclude = set(people.Person.Schema.get_field_names()) - include
        user_map = {str(pd.user_id): pd.serialize(exclude=exclude) for pd in people_entities}

        return user_map

    def _get_video_map(self, video_ids):
        if not video_ids:
            return {}

        video_entities = videos.get_videos(
            self.context,
            event_id=self.event_id,
            id=video_ids,
            include_deleted=True,
        )
        include = {'name', 'id', 'url'}
        exclude = set(Video.Schema.get_field_names()) - include
        video_map = {vd.id: vd for vd in video_entities.serialize(exclude=exclude)}
        return video_map

    def _date_to_datetime(self, start_date, end_date):
        """ Converts Date Range to Datetime Range

        This function assumes incoming dates are naive but representing the time zone of the event.
        The resulting datetimes will be UTC with the time component offset based on the difference
        in the event timezone to event timezone.

        end_datetime will be set as very beginning of the next day from end_date
        """
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.min.time()) + timedelta(days=1)

        result_start_datetime = timeutils.convert_datetime_to_utc(start_datetime, self.timezone)
        result_end_datetime = timeutils.convert_datetime_to_utc(end_datetime, self.timezone)

        return result_start_datetime, result_end_datetime
