from datetime import datetime, timezone

from flux.adapters.db import (
    db,
    FluxModel2,
)


REPORT_STATUS_OPTS = ['not_started', 'in_progress', 'done', 'failed']
REPORT_TYPES = ['app', 'gamification']


class AnalyticsReport(FluxModel2):
    __tablename__ = 'analytics_reports'

    _created_at_index = False
    _updated_at_index = False

    report_type = db.Column(
        db.Enum(*REPORT_TYPES),
        nullable=False
    )
    status = db.Column(
        db.Enum(*REPORT_STATUS_OPTS),
        default='not_started', nullable=False
    )
    event_id = db.Column(db.INTEGER, nullable=False)
    report_start_date = db.Column(
        db.DATETIME,
        default=datetime.now(timezone.utc), nullable=False
    )
    report_end_date = db.Column(
        db.DATETIME,
        default=datetime.now(timezone.utc), nullable=False
    )
