from uuid import uuid4
from datetime import datetime

from flux.adapters.db import db, UUID, FluxModel

REPORT_STATUS_OPTS = ['not_started', 'in_progress', 'zipping', 'done',
                      'failed']
STATUS_OPTS = ['not_started', 'in_progress', 'done', 'failed']
REPORT_TYPES = ['survey', 'poll', 'session_feedback', 'aaq']
REPORT_FORMATS = ['pdf', 'excel', 'excel-individual-responses',
                  'excel-raw-consolidated']


class ArsReport(FluxModel):
    __tablename__ = 'ars_reports'

    id = db.Column(UUID, primary_key=True, default=uuid4)
    report_type = db.Column(
        db.Enum(*REPORT_TYPES),
        nullable=False
    )
    format = db.Column(db.Enum(*REPORT_FORMATS), nullable=False)
    status = db.Column(
        db.Enum(*REPORT_STATUS_OPTS),
        default='not_started', nullable=False
    )
    event_id = db.Column(db.INTEGER, nullable=False)
    created_at = db.Column(
        db.DATETIME,
        default=datetime.utcnow, nullable=False
    )
    updated_at = db.Column(
        db.DATETIME,
        default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_anonymous = db.Column(db.BOOLEAN, default=False, nullable=False)
    total_files = db.Column(db.INTEGER, default=0, nullable=False)


class ArsReportFile(FluxModel):
    __tablename__ = 'ars_report_files'

    id = db.Column(UUID, primary_key=True, default=uuid4)
    report_id = db.Column(UUID, nullable=False)
    filename = db.Column(db.VARCHAR(512), nullable=False)
    status = db.Column(
        db.Enum(*STATUS_OPTS),
        default='not_started', nullable=False
    )
    event_id = db.Column(db.INTEGER, nullable=False)
    created_at = db.Column(
        db.DATETIME,
        default=datetime.utcnow, nullable=False
    )
    updated_at = db.Column(
        db.DATETIME,
        default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )


class ArsReportFragment(FluxModel):
    __tablename__ = 'ars_report_fragments'

    id = db.Column(UUID, primary_key=True, default=uuid4)
    file_id = db.Column(UUID, nullable=False)
    survey_id = db.Column(db.INTEGER)
    seminar_id = db.Column('session_id', db.INTEGER)
    session_id = db.Column('cms2_session_id', UUID)
    question_id = db.Column(db.INTEGER)
    order = db.Column(db.INTEGER, nullable=False)
    is_cover = db.Column(db.BOOLEAN, nullable=False)
    status = db.Column(
        db.Enum(*STATUS_OPTS),
        default='not_started', nullable=False
    )
    event_id = db.Column(db.INTEGER, nullable=False)
    created_at = db.Column(
        db.DATETIME,
        default=datetime.utcnow, nullable=False
    )
    updated_at = db.Column(
        db.DATETIME,
        default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
