from typing import Type

from flux.adapters.db import db, UUID, FluxModel
from flux.domains.ars.ask_a_question import AskAQuestion, AAQ_MAX_UPVOTE_COUNT
from flux.domains.common import PaginationInfo
from flux.utils.not_set import NotSet


class AskAQuestionTable(FluxModel):
    __tablename__ = 'ask_a_question_table'

    _searchable_fields = {
        'question_text': None,
    }

    _sortable_fields = {
        'upvotes': None,
    }

    id = db.Column('id', db.INTEGER(), primary_key=True, nullable=False)
    event_id = db.Column('event_id', db.INTEGER(), nullable=False)
    seminar_id = db.Column(
        'seminar_id',
        db.INTEGER(),
        nullable=True,
        default=None,
    )
    session_id = db.Column('session_id', UUID, nullable=True, default=None)
    question_text = db.Column('question_text', db.TEXT(), nullable=False)
    visible = db.Column('visible', db.BOOLEAN(), nullable=False, default=True)
    upvotes = db.Column('upvotes', db.INTEGER(), default=1)
    deleted = db.Column('deleted', db.BOOLEAN(), nullable=False, default=False)

    @classmethod
    def delete_all_for_event(cls, event_id):
        models = cls.all_for_event(event_id)
        for m in models:
            cls.delete(m)

    @classmethod
    def all_for_event(cls, event_id):
        for i in cls.query.filter(AskAQuestionTable.event_id == event_id):
            yield i

    @classmethod
    def get_by_event(cls, event_id, ids=NotSet, session_id=NotSet, visible=NotSet,
                     page_info: PaginationInfo = None, include=None, search=None):

        q = cls.query.filter_by(event_id=event_id)

        if search:
            q = cls._search(search, include, q)

        if ids is not NotSet:
            q = q.filter(cls.id.in_(ids))

        if session_id is not NotSet:
            q = q.filter(cls.session_id.in_(session_id))

        if visible is not NotSet:
            q = q.filter(cls.visible == visible)

        return cls._sort_and_paginate(
            query=q,
            page_info=page_info,
            default_sort='upvotes',
            paginate_in_outer_query=bool(search),
        )

    @classmethod
    def create_from_entity(cls, entity: AskAQuestion) -> 'AskAQuestionTable':
        """
        Create model object from entity
        """
        model = cls(
            id=entity.id,
            event_id=entity.event_id,
            session_id=entity.session_id,
            question_text=entity.question_text
        )
        model.update_model_from_entity(entity)
        return model

    def update_model_from_entity(self, entity: AskAQuestion):
        """
        Update model attributes using the entity
        """
        self.visible = entity.visible
        self.upvotes = entity.upvotes

    def as_entity(self, entity_cls: Type[AskAQuestion]) -> AskAQuestion:
        """
        Build AskAQuestion entity using AskAQuestionTable object
        """
        entity_data = dict(
            id=str(self.id),
            event_id=self.event_id,
            session_id=self.session_id,
            question_text=self.question_text,
            upvotes=self.upvotes,
            visible=self.visible,
        )
        return entity_cls(**entity_data)

    @classmethod
    def upvote_once(cls, event_id, question_id) -> int:
        """
        Upvote a question once if it hasn't reached the AAQ_MAX_UPVOTE_COUNT

        Query.upvote returns the number of records that were updated by the query by default
        Since we are providing a filter on the PK (id), so we can be sure
        that only one record will be updated.

        Returns 1 if the upvote is successful, 0 if unsuccessful
        """
        upvote_successful = cls.query.filter(
            cls.id == question_id,
            cls.event_id == event_id,
            cls.upvotes < AAQ_MAX_UPVOTE_COUNT,
        ).update({'upvotes': cls.upvotes + 1})

        return upvote_successful
