from typing import Type
from datetime import datetime
from sqlalchemy import func, or_, and_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import selectinload

from flux.adapters.db import (
    db,
    FluxModel,
    FluxModel2,
    ObjectNotFoundError,
    ObjectRelationshipError,
    UUID,
)
from flux.domains.ars import surveys, survey_questions
from flux.domains.common.pagination import PaginationInfo
from flux.domains.event.groups.models import Track
from flux.domains.event.sessions.models import Session

from flux.utils.not_set import NotSet


# Maps table survey types to entity survey types, table values are on the left
TYPE_MAP = {
    'poll': surveys.SurveyTypes.live_poll,
    'session_feedback': surveys.SurveyTypes.session_feedback,
    'survey': surveys.SurveyTypes.event_survey,
}
R_TYPE_MAP = {v: k for k, v in TYPE_MAP.items()}

# Maps table answer types to entity answer types, table values are on the left
ANS_MAP = {
    'text': survey_questions.AnswerTypes.text,
    'single-select': survey_questions.AnswerTypes.single_select,
    'multi-select': survey_questions.AnswerTypes.multi_select,
    'ratings': survey_questions.AnswerTypes.ratings,
}
R_ANS_MAP = {v: k for k, v in ANS_MAP.items()}

# Maps entity result display types to table result display types
RESULT_DISPLAY_TYPE_MAP = {
    survey_questions.ResultDisplayType.pie: 'pie',
    survey_questions.ResultDisplayType.column: 'column',
    survey_questions.ResultDisplayType.bar: 'bar',
}


class QuestionTable(FluxModel):
    __tablename__ = 'ars2_question'

    _searchable_fields = {
        'question_text': None,
    }

    _sortable_fields = {
        'question_text': None,
        'answer_type': None,
        'result_display_type': None,
        'order': None,
    }

    id = db.Column('id', db.INTEGER(), primary_key=True, nullable=False)
    group_id = db.Column(
        'group_id',
        db.INTEGER(),
        db.ForeignKey('ars_question_group.id', ondelete='CASCADE'),
        nullable=False,
    )
    event_id = db.Column('event_id', db.INTEGER(), nullable=False)
    answer_type = db.Column('answer_type', db.VARCHAR(25), nullable=False)
    result_display_type = db.Column('result_display_type', db.VARCHAR(25),
                                    nullable=False)
    question_text = db.Column('question_text', db.TEXT(), nullable=False)
    visible = db.Column('visible', db.BOOLEAN(), nullable=False, default=True)
    order = db.Column('order', db.INTEGER())
    multiple_answers = db.Column('multiple_answers', db.BOOLEAN(),
                                 nullable=False, default=False)
    disable_answers = db.Column('disable_answers', db.BOOLEAN(),
                                nullable=False, default=False)

    # relationships
    answers = db.relationship(
        'SurveyAnswer',
        backref='question',
        passive_deletes=True,
        cascade='save-update, merge, expunge',
    )
    options = db.relationship(
        'QuestionOptionTable',
        backref='question',
        passive_deletes=True,
        cascade='save-update, merge, expunge',
    )
    result_customizations = db.relationship(
        'ResultsCustomizationTable',
        backref='question',
        passive_deletes=True,
        cascade='expunge',
    )

    def as_entity(
        self, entity_cls: Type[survey_questions.SurveyQuestion]
    ) -> survey_questions.SurveyQuestion:
        question_data = dict(
            id=str(self.id),
            event_id=self.event_id,
            survey_id=str(self.group_id),
            question_text=self.question_text,
            answer_type=ANS_MAP[self.answer_type],
            result_display_type=survey_questions.ResultDisplayType[
                self.result_display_type
            ],
            multiple_answers=self.multiple_answers,
            disable_answers=self.disable_answers,
            order=self.order,
            visible=self.visible,
        )

        if 'options' in entity_cls.get_active_includes():
            question_data['options'] = [
                o.as_entity(entity_cls.SurveyQuestionOption)
                for o in self.options
            ]

        question_entity = entity_cls(**question_data)
        return question_entity

    @classmethod
    def _optimize_includes(cls, include, q):
        if include and 'options' in include:
            q = q.options(selectinload(cls.options))
        return q

    @classmethod
    def get_questions(
        cls,
        event_id,
        survey_id,
        _id=NotSet,
        answer_type=NotSet,
        result_display_type=NotSet,
        multiple_answers=NotSet,
        disable_answers=NotSet,
        visible=NotSet,
        page_info: PaginationInfo = None,
        include=None,
        search=None,
    ):
        q = cls.query.filter_by(event_id=event_id, group_id=survey_id)

        q = cls._optimize_includes(include, q)

        if search:
            q = cls._search(search, include, q)

        if _id is not NotSet:
            q = q.filter(cls.id.in_(_id))

        if answer_type is not NotSet:
            if not isinstance(answer_type, list):
                answer_type = [answer_type]
            answer_type = [R_ANS_MAP[ans_type] for ans_type in answer_type]
            q = q.filter(cls.answer_type.in_(answer_type))

        if result_display_type is not NotSet:
            if not isinstance(result_display_type, list):
                result_display_type = [result_display_type]
            result_display_type = [
                RESULT_DISPLAY_TYPE_MAP[display_type] for display_type in result_display_type
            ]
            q = q.filter(cls.result_display_type.in_(result_display_type))

        if multiple_answers is not NotSet:
            q = q.filter(cls.multiple_answers == multiple_answers)

        if disable_answers is not NotSet:
            q = q.filter(cls.disable_answers == disable_answers)

        if visible is not NotSet:
            q = q.filter(cls.visible == visible)

        return cls._sort_and_paginate(
            query=q,
            page_info=page_info,
            default_sort='order',
            paginate_in_outer_query=bool(search),
        )

    @classmethod
    def delete_by_id(cls, event_id, survey_id, question_id):
        cls._delete_by_id(question_id, event_id=event_id, group_id=survey_id)

    @classmethod
    def delete_all(cls, event_id, survey_id):
        return cls.bulk_delete_by_filters(cls.event_id == event_id, cls.group_id == survey_id)


class QuestionOptionTable(FluxModel):
    __tablename__ = 'ars2_option'

    # column definitions
    id = db.Column('id', db.INTEGER(), primary_key=True, nullable=False)
    option_text = db.Column('option_text', db.TEXT(), nullable=False)
    order = db.Column('order', db.INTEGER())
    question_id = db.Column(
        'question_id',
        db.INTEGER(),
        db.ForeignKey('ars2_question.id', ondelete='CASCADE'),
        nullable=False,
    )
    event_id = db.Column('event_id', db.INTEGER(), nullable=False)

    # relationships
    answers = db.relationship(
        'SurveyAnswer',
        backref='option',
        passive_deletes=True,
        cascade='save-update, merge, expunge',
    )

    def as_entity(self, entity_cls: Type[survey_questions.SurveyQuestionOption]
                  ) -> survey_questions.SurveyQuestionOption:
        option_data = dict(
            id=str(self.id),
            option_text=self.option_text,
            order=self.order,
        )
        entity = entity_cls(**option_data)
        return entity


class QuestionGroupTable(FluxModel):
    __tablename__ = 'ars_question_group'

    _searchable_fields = {
        'name': None,
    }

    _sortable_fields = {
        'name': None,
        'type': None,
        'order': None,
    }

    id = db.Column('id', db.INTEGER(), primary_key=True, nullable=False)
    name = db.Column('name', db.VARCHAR(length=275))
    type = db.Column('poll_type', db.VARCHAR(25))
    event_id = db.Column('event_id', db.INTEGER(), nullable=False)
    applied_to_event = db.Column('applied_to_event', db.BOOLEAN(),
                                 nullable=False, default=False)
    visible = db.Column('visible', db.BOOLEAN(), nullable=False, default=True)
    order = db.Column('order', db.INTEGER())

    # cms1/cms2 relationships
    questions = db.relationship(
        'QuestionTable',
        backref='group',
        passive_deletes=True,
        cascade='save-update, merge, expunge',
    )

    # cms2 relationships
    sessions = db.relationship(
        'QuestionGroupSessions',
        uselist=True,
        lazy='select',
        passive_deletes=True,
        cascade='save-update, merge, expunge',
    )
    tracks = db.relationship(
        'QuestionGroupTracks',
        uselist=True,
        lazy='select',
        passive_deletes=True,
        cascade='save-update, merge, expunge',
    )

    @classmethod
    def get_by_event(cls, event_id, id=NotSet, type=NotSet, session_id=NotSet,
                     applied_to_entity_type=NotSet, visible=NotSet,
                     page_info: PaginationInfo = None, include=None, search=None):

        q = cls.query.filter_by(event_id=event_id)

        q = cls._optimize_includes(include, q)

        if search:
            q = cls._search(search, include, q)

        if id is not NotSet:
            q = q.filter(cls.id.in_(id))

        if type is not NotSet:
            if not isinstance(type, list):
                type = [type]
            type = [R_TYPE_MAP[t] for t in type]
            q = q.filter(cls.type.in_(type))

        if session_id is not NotSet:
            try:
                session = Session.get_by_id(session_id, event_id=event_id)
            except ObjectNotFoundError:
                session = None

            if session:
                q = q.filter(or_(
                    cls.sessions.any(session_id=session_id),
                    and_(cls.type == 'session_feedback', cls.applied_to_event.is_(True)),
                    cls.tracks.any(QuestionGroupTracks.track_id.in_(session.track_ids)),
                ))
            else:
                q = q.filter(cls.sessions.any(session_id=session_id))

        if applied_to_entity_type is not NotSet:
            if applied_to_entity_type == surveys.SurveyEntityTypes.event:
                q = q.filter(cls.applied_to_event)
            elif applied_to_entity_type == surveys.SurveyEntityTypes.sessions:
                q = q.filter(cls.sessions.any())
            elif applied_to_entity_type == surveys.SurveyEntityTypes.tracks:
                q = q.filter(cls.tracks.any())
            elif applied_to_entity_type == surveys.SurveyEntityTypes.all_sessions:
                q = q.filter(and_(~cls.applied_to_event, ~cls.sessions.any(), ~cls.tracks.any()))

        if visible is not NotSet:
            q = q.filter(cls.visible == visible)

        return cls._sort_and_paginate(
            query=q,
            page_info=page_info,
            default_sort='order',
            paginate_in_outer_query=bool(search),
        )

    @classmethod
    def delete_by_id(cls, event_id, survey_id):
        try:
            model = cls.get_by_id(survey_id, event_id=event_id)
            db.session.expunge(model)
            db.session.delete(model)
            db.session.flush()
        except ObjectNotFoundError:
            pass
        except IntegrityError as err:
            db.session.rollback()
            raise ObjectRelationshipError(cls) from err

    def as_entity(self, entity_cls: Type[surveys.Survey]) -> surveys.Survey:

        applied_to = None

        if self.sessions:
            applied_to = entity_cls.AppliedTo(
                entity_type=surveys.SurveyEntityTypes.sessions,
                entity_ids=[s.session_id for s in self.sessions],
            )
        elif self.tracks:
            applied_to = entity_cls.AppliedTo(
                entity_type=surveys.SurveyEntityTypes.tracks,
                entity_ids=[t.track_id for t in self.tracks],
            )
        elif not self.applied_to_event:
            applied_to = entity_cls.AppliedTo(
                entity_type=surveys.SurveyEntityTypes.all_sessions,
                entity_ids=[],
            )

        survey_data = dict(
            id=str(self.id),
            name=self.name,
            type=TYPE_MAP[self.type],
            event_id=self.event_id,
            order=self.order,
            visible=self.visible,
            applied_to_event=self.applied_to_event,
            applied_to_session_ids=[s.session_id for s in self.sessions],
            applied_to_track_ids=[t.track_id for t in self.tracks],
            applied_to=applied_to
        )

        if 'questions' in entity_cls.get_active_includes():
            survey_data['questions'] = [
                q.as_entity(entity_cls.SurveyQuestion) for q in self.questions
            ]

        entity = entity_cls(**survey_data)
        return entity

    @classmethod
    def _optimize_includes(cls, include, q):
        if include and 'questions' in include:
            if include and 'options' in include:
                q = q.options(selectinload(cls.questions))
            else:
                q = q.options(selectinload(cls.questions).selectinload(QuestionTable.options))
        return q


class QuestionGroupSessions(FluxModel2):
    __tablename__ = 'ars_question_group_sessions'

    event_id = db.Column(db.Integer, nullable=False)
    question_group_id = db.Column(
        'question_group_id',
        db.Integer,
        db.ForeignKey('ars_question_group.id', ondelete='CASCADE'),
        nullable=False,
    )
    session_id = db.Column(
        UUID,
        db.ForeignKey('event_sessions.id', ondelete='CASCADE'),
        nullable=False,
    )


class QuestionGroupTracks(FluxModel2):
    __tablename__ = 'ars_question_group_tracks'

    event_id = db.Column(db.Integer, nullable=False)
    question_group_id = db.Column(
        'question_group_id',
        db.Integer,
        db.ForeignKey('ars_question_group.id', ondelete='CASCADE'),
        nullable=False,
    )
    track_id = db.Column(
        UUID,
        db.ForeignKey(Track.id, ondelete='CASCADE'),
        nullable=False,
    )


class SurveyAnswer(FluxModel):
    __tablename__ = 'ars_question_answer'

    _searchable_fields = {
        'answer': None,
    }

    _sortable_fields = {
        'date_answered': None,
    }

    id = db.Column('id', db.INTEGER(), primary_key=True, nullable=False)
    question_id = db.Column(
        'question_id',
        db.INTEGER(),
        db.ForeignKey('ars2_question.id', ondelete='CASCADE'),
        nullable=False,
    )
    seminar_id = db.Column('seminar_id', db.INTEGER())
    session_id = db.Column('session_id', UUID)
    option_id = db.Column(
        'option_id',
        db.INTEGER(),
        db.ForeignKey('ars2_option.id', ondelete='CASCADE'),
    )
    attendee_id = db.Column('attendee_id', db.INTEGER())
    people_id = db.Column(
        UUID, db.ForeignKey('event_people.id', ondelete='CASCADE'), nullable=True,
    )
    answer = db.Column('answer', db.TEXT())
    anonymous_session_id = db.Column('anonymous_session_id', db.TEXT())
    date_answered = db.Column(
        'date_answered',
        db.DATETIME(),
        default=datetime.utcnow,
    )
    visible = db.Column('visible', db.BOOLEAN(), nullable=False, default=True)
    applied_to_event = db.Column(
        'applied_to_event',
        db.BOOLEAN(),
        nullable=False,
        default=False,
    )
    event_id = db.Column('event_id', db.INTEGER(), nullable=False)

    @classmethod
    def delete_answers(cls, event_id, question_ids):
        models = cls.all_for_questions(event_id, question_ids)
        for model in models:
            cls.delete(model)

    @classmethod
    def all_for_questions(cls, event_id, question_ids):
        q = cls.query.filter(SurveyAnswer.event_id == event_id)
        q = q.filter(SurveyAnswer.question_id.in_(question_ids))
        for i in q:
            yield i

    @classmethod
    def get_answers(
        cls,
        event_id,
        ids=NotSet,
        question_ids=NotSet,
        session_ids=NotSet,
        option_ids=NotSet,
        people_ids=NotSet,
        applied_to_event=NotSet,
        visible=NotSet,
        page_info: PaginationInfo = None,
        include=None,
        search=None,
        skip_search_and_pagination=False,
    ):
        # We have to create the BaseQuery object like this to use the `group_concat` function
        # This function is MySQL specific and is not available in other DBMS
        # Equivalent PostgreSQL functions: `string_agg` and `array_agg`
        q = db.session.query(
            cls.id,
            cls.question_id,
            cls.session_id,
            cls.option_id,
            cls.people_id,
            cls.answer,
            cls.date_answered,
            cls.applied_to_event,
            cls.visible,
            cls.event_id,
            func.group_concat(cls.option_id).label('option_id'),
        )

        q = q.filter_by(event_id=event_id)

        if search:
            q = cls._search(search, include, q)

        if ids is not NotSet:
            q = q.filter(cls.id.in_(ids))

        if question_ids is not NotSet:
            q = q.filter(cls.question_id.in_(question_ids))

        if session_ids is not NotSet:
            q = q.filter(cls.session_id.in_(session_ids))

        if option_ids is not NotSet:
            q = q.filter(cls.option_id.in_(option_ids))

        if people_ids is not NotSet:
            q = q.filter(cls.people_id.in_(people_ids))

        if applied_to_event is not NotSet:
            q = q.filter(cls.applied_to_event == applied_to_event)

        if visible is not NotSet:
            q = q.filter(cls.visible == visible)

        q = (
            q.group_by(cls.question_id)
            .group_by(cls.people_id)
            .group_by(cls.session_id)
            .group_by(cls.applied_to_event)
        )

        if skip_search_and_pagination:
            return q

        return cls._sort_and_paginate(
            query=q,
            page_info=page_info,
            default_sort='date_answered',
            paginate_in_outer_query=bool(search),
        )


class ResultsCustomizationTable(FluxModel):
    __tablename__ = 'ars_results_customization'

    id = db.Column('id', db.INTEGER(), primary_key=True, nullable=False)
    event_id = db.Column('event_id', db.INTEGER(), nullable=False)
    question_id = db.Column(
        'question_id',
        db.INTEGER(),
        db.ForeignKey('ars2_question.id', ondelete='CASCADE'),
        nullable=True,
    )
    seminar_id = db.Column('seminar_id', db.INTEGER(), nullable=True)
    session_id = db.Column('session_id', UUID, nullable=True)
    owner_type = db.Column('owner_type', db.VARCHAR(25), nullable=False)
    name = db.Column('name', db.TEXT())
    value = db.Column('value', db.TEXT())
    deleted = db.Column('deleted', db.BOOLEAN(), nullable=False, default=False)
