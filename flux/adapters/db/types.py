import enum
import json
import uuid

import pytz

from datetime import datetime, time, timezone
from decimal import Decimal as _Decimal

from sqlalchemy.types import (
    TypeDecorator,
    BOOLEAN,
    INTEGER,
    VARBINARY,
    VARCHAR,
    TEXT,
    FLOAT,
    DATETIME,
)
from sqlalchemy.dialects.mysql import TIME, BIT, DECIMAL as MYSQL_DECIMAL


class UnboundTypeDecorator(TypeDecorator):

    def result_processor(self, dialect, coltype):
        return lambda value: self.process_result_value(value, dialect)

    def bind_processor(self, dialect):
        """ Simple override to avoid pre-processing before
        process_bind_param. This is for when the Python type can't
        be easily coerced into the `impl` type."""
        return lambda value: self.process_bind_param(value, dialect)

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class UnixTimestamp(UnboundTypeDecorator):
    """ Someone decided it was a good idea to not use
    MySQL's DATETIME type on the `packages` table.

    """

    impl = INTEGER
    cache_ok = True

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    def process_bind_param(self, value, dialect):
        # datetime.timestamp returns a float
        return value if value is None else int(value.timestamp())

    def process_result_value(self, value, dialect):
        return value if value is None else (datetime.utcfromtimestamp(value)
                                            .replace(tzinfo=pytz.UTC))

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class UnixTimestampHires(UnboundTypeDecorator):
    """ Unix timestamp with microsecond resolution. Older mysql versions have
    a TIMESTAMP/DATETIME with second resolution. Represented as a DOUBLE in
    the database. Result is a naive datetime.

    """

    impl = FLOAT(53)
    cache_ok = True

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    def process_bind_param(self, value, dialect):
        return (
            value
            if value is None
            else value.replace(tzinfo=pytz.UTC).timestamp()
        )

    def process_result_value(self, value, dialect):
        return value if value is None else datetime.utcfromtimestamp(value)

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class UUID(UnboundTypeDecorator):
    """ A memory-efficient MySQL UUID-type. """

    impl = VARBINARY(16)
    cache_ok = True

    def process_bind_param(self, value, dialect):
        """ Emit the param in hex notation. """
        if isinstance(value, str) and value:
            value = uuid.UUID(value)
        return value.bytes if value else value

    def process_result_value(self, value, dialect):
        if value:
            if len(value) != 16:
                raise AssertionError(f"Expected 16 bytes, got {len(value)}")
            return uuid.UUID(bytes=value)
        return None

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class JSON(UnboundTypeDecorator):
    """ Introduces a complex JSON-type for MySQL.

    This is valuable for storing complex, immutable data, while avoiding joins.
    Then rather than having separate serialization schemes for separate
    services, a single one can be used that works for most Python objects.

    """

    impl = VARCHAR
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is None:
            return None
        prefix = ''
        suffix = ''
        if isinstance(value, str):
            if value.startswith('%'):
                prefix = '%'
                value = value.lstrip('%')
            if value.endswith('%'):
                suffix = '%'
                value = value.rstrip('%')

        return f'{prefix}{json.dumps(value, sort_keys=True)}{suffix}'

    def process_result_value(self, value, dialect):
        return value if value is None else json.loads(value)

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class TEXTJSON(JSON):
    impl = TEXT  # type: ignore[assignment]
    cache_ok = True

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class Time(TypeDecorator):
    """Platform-independent Time type.

    """
    impl = TEXT
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'mysql':
            return dialect.type_descriptor(TIME())
        return dialect.type_descriptor(TEXT())

    def process_bind_param(self, value, dialect):
        if value is None or dialect.name == 'mysql':
            return value
        return str(value)

    def process_result_value(self, value, dialect):
        if value is None or dialect.name == 'mysql':
            return value
        return time(*list(map(int, value.split(':'))))

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class BitBoolean(TypeDecorator):
    """Coerces BIT-type columns to Boolean values for MySQL.

    """
    impl = BOOLEAN
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'mysql':
            return dialect.type_descriptor(BIT)
        return dialect.type_descriptor(BOOLEAN)

    def process_bind_param(self, value, dialect):
        if value is None or dialect.name != 'mysql':
            return value
        return b'1' if value else b'0'

    def process_result_value(self, value, dialect):
        if value is None or dialect.name != 'mysql':
            return value
        return True if int(value) else False

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class CharID(TypeDecorator):
    """
    Type for ids that supports both UUID and integer formats.
    """

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    impl = VARCHAR(36)
    cache_ok = True


class BaseProperEnum(TypeDecorator):
    """
    Base class for ProperEnum and ProperIntEnum.
    Provides shared functionality without defining `impl`,
    allowing subclasses to specify their own storage type.
    """
    cache_ok = True

    def __init__(self, *args, enum_cls=None, assert_on_invalid_value=True, **kwargs):
        super().__init__(*args, **kwargs)
        if not issubclass(enum_cls, enum.Enum):
            raise AssertionError
        self.enum_cls = enum_cls
        self.assert_on_invalid_value = assert_on_invalid_value

    def process_bind_param(self, value, dialect):
        """Convert enum instance to its stored value."""
        if not (value is None or isinstance(value, self.enum_cls)):
            raise AssertionError
        return value.value if value is not None else value

    def process_result_value(self, value, dialect):
        """
        1.  If value is None, return None
        2.  If value is not None:
            i. value in enum_cls, return enum instance
            ii. value not in enum_cls and assert_on_invalid_value is True, raise AssertionError
            iii. value not in enum_cls and assert_on_invalid_value is False, return None
        """
        if not (
            value is None or value in self.enum_cls.__members__ or self.assert_on_invalid_value
        ):
            return None
        return self.enum_cls(value) if value is not None else value

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class ProperEnum(BaseProperEnum):
    """
    This enum type works in the following way:
        1) On DB level, data is stored as VARCHAR (make sure size is big enough)
        2) On Application level, it operates on enum instances
        3) We store enum value, not enum name (as it does SQLAlchemy)
    """
    impl = VARCHAR

    @property
    def python_type(self):
        return str

    def process_literal_param(self, value, dialect):
        raise NotImplementedError()


class ProperIntEnum(BaseProperEnum):
    """Stores enums as INTEGER in the database."""
    impl = INTEGER

    @property
    def python_type(self):
        return int

    def process_literal_param(self, value, dialect):
        raise NotImplementedError()


class TupleValueEnum(UnboundTypeDecorator):
    """
    This enum type works in the following way:
    1) On DB level, data is stored as VARCHAR (make sure size is big enough)
    2) On Application level, it operates on enum instances
    3) When we have complex values (tuple), we store enum name
    """

    impl = VARCHAR
    cache_ok = True

    def __init__(self, *args, enum_cls=None, **kwargs):
        super().__init__(*args, **kwargs)
        if not issubclass(enum_cls, enum.Enum):
            raise AssertionError
        self.enum_cls = enum_cls

    def process_bind_param(self, value, dialect):
        if not (value is None or isinstance(value, self.enum_cls)):
            raise AssertionError
        return value.value if value is not None else value

    def process_result_value(self, value, dialect):
        return (self.enum_cls.__members__.get(value, None)
                if value is not None else value)

    @property
    def python_type(self):
        """Return the Python type object expected to be returned
        by instances of this type, if known.

        Basically, for those types which enforce a return type,
        or are known across the board to do such for all common
        DBAPIs (like ``int`` for example), will return that type.

        If a return type is not defined, raises
        ``NotImplementedError``.

        Note that any type also accommodates NULL in SQL which
        means you can also get back ``None`` from any type
        in practice.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()

    def process_literal_param(self, value, dialect):
        """Receive a literal parameter value to be rendered inline within
        a statement.

        .. note::

            This method is called during the **SQL compilation** phase of a
            statement, when rendering a SQL string. Unlike other SQL
            compilation methods, it is passed a specific Python value to be
            rendered as a string. However it should not be confused with the
            :meth:`_types.TypeDecorator.process_bind_param` method, which is
            the more typical method that processes the actual value passed to a
            particular parameter at statement execution time.

        Custom subclasses of :class:`_types.TypeDecorator` should override
        this method to provide custom behaviors for incoming data values
        that are in the special case of being rendered as literals.

        The returned string will be rendered into the output string.

        Note that this method was overridden in this derived class
        as it raises a bug risk in deepsource
        """
        raise NotImplementedError()


class Decimal(TypeDecorator):
    """Platform-independent Decimal type.

    """
    impl = TEXT
    cache_ok = True

    def __init__(self, *args, precision=None, scale=None, asdecimal=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.precision = precision
        self.scale = scale
        self.asdecimal = asdecimal

    def load_dialect_impl(self, dialect):
        if dialect.name == 'mysql':
            return dialect.type_descriptor(
                MYSQL_DECIMAL(precision=self.precision, scale=self.scale, asdecimal=self.asdecimal)
            )
        return dialect.type_descriptor(TEXT())

    def process_bind_param(self, value, dialect):
        if value is None or dialect.name == 'mysql':
            return value
        return str(value)

    def process_result_value(self, value, dialect):
        if value is None or dialect.name == 'mysql':
            return value
        return _Decimal(value)

    def process_literal_param(self, value, dialect):
        raise NotImplementedError()

    @property
    def python_type(self):
        raise NotImplementedError()


class AwareDateTime(TypeDecorator):
    impl = DATETIME
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if not (value is None or isinstance(value, datetime)):
            # if value is not none nor instance of datetime, raise error
            raise ValueError(f"Unexpected type {type(value)}")
        if isinstance(value, datetime) and value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        if isinstance(value, datetime):
            if value.tzinfo is None:
                value = value.replace(tzinfo=timezone.utc)
            return value
        raise ValueError(f"Unexpected type {type(value)}")

    def process_literal_param(self, value, dialect):
        raise NotImplementedError()

    @property
    def python_type(self):
        raise NotImplementedError()
