import uuid

from functools import reduce
from datetime import datetime, timezone
from typing import Dict, <PERSON>, <PERSON>, Tuple

from sqlalchemy import func, or_, desc, asc
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import contains_eager, aliased, InstrumentedAttribute
from sqlalchemy.exc import NoResultFound, MultipleResultsFound
from sqlalchemy.sql.elements import Text<PERSON>lause, Case

from flux.utils.dot_dict import DotDict
from flux.utils.not_set import NotSet
from flux.utils.timeutils import get_aware_utc_now
from flux.adapters.logging import get_logger

from flux.domains.common.consts import CreationSourceType
from flux.domains.common.error_codes import ErrorCodes
from flux.domains.common.errors import PublicError, SortingFieldError
from flux.domains.common.error_source import ErrorSourceKinds

from .db import db
from .types import UUID, AwareDateTime, ProperEnum


class SortingError(PublicError):
    http_status = 400
    code = ErrorCodes.invalid_value
    source_kind = ErrorSourceKinds.query
    message = 'Failed to parse sort parameter'

    def __init__(self):
        super().__init__(source_pointer='sort')


logger = get_logger(__name__)


class DatabaseError(Exception):
    """
    Database errors are used to encapsulate low level errors such that happens
    on database layer, such as integrity issues.
    """


class InvalidSearchableField(DatabaseError):
    def __init__(self, field):
        super().__init__(f'Searchable Field {field} was not found')


class ObjectNotFoundError(DatabaseError):
    def __init__(self, cls):
        super().__init__(f'Object not found for class {cls.__name__}')


class MultipleObjectsFoundError(DatabaseError):
    def __init__(self, cls, filter_clause='the query'):
        super().__init__(
            f'There are multiple Objects found for {cls.__name__} filtered by '
            f'{filter_clause}')


class DatabaseIntegrityError(DatabaseError):

    def __init__(self, message, integrity_err_msg=None):
        self.integrity_err_msg = integrity_err_msg
        super().__init__(message)


class ObjectConflictError(DatabaseIntegrityError):
    def __init__(self, cls, integrity_err_msg=None):
        super().__init__(f'Already exists an Object for {cls.__name__}', integrity_err_msg)


class ObjectRelationshipError(DatabaseIntegrityError):
    def __init__(self, cls, integrity_err_msg=None):
        super().__init__(f'Object relationship error for {cls.__name__}', integrity_err_msg)


class InvalidObjectError(DatabaseIntegrityError):
    def __init__(self, cls, integrity_err_msg=None):
        super().__init__(f'Object invalid error for {cls.__name__}', integrity_err_msg)


class QueryFilters(DotDict):

    def __getattr__(self, attr):
        try:
            return super().__getattr__(attr)
        except AttributeError:
            return NotSet

    def is_set(self, attr_name):
        return getattr(self, attr_name) is not NotSet


class FluxModel(db.Model):  # type: ignore
    # TODO: Find more reliable way of extending common table args
    # Possible way is at https://stackoverflow.com/questions/23415638/
    # combining-table-args-with-constraints-from-mixin-classes-in-sqlalchemy
    __table_args__: Union[Dict[str, Any], Tuple] = {
        'mysql_engine': 'InnoDB',
        'mysql_default_charset': 'utf8mb4',
    }

    __abstract__ = True
    _id_fieldname = 'id'
    _searchable_fields: dict = {}
    _sortable_fields: dict = {}

    @classmethod
    def _get_by(cls, *filter_clauses, query_ext_funcs=None):
        if not filter_clauses:
            raise ValueError('At least one filter_clause needs to be given')

        try:
            query = cls.query

            if not isinstance(query_ext_funcs, list):
                query_ext_funcs = [query_ext_funcs] if query_ext_funcs else []
            for func_ in query_ext_funcs:
                query = func_(query)

            for filter_clause in filter_clauses:
                query = query.filter(filter_clause)

            return query.one()
        except NoResultFound:
            raise ObjectNotFoundError(cls)
        except MultipleResultsFound:
            raise MultipleObjectsFoundError(cls, filter_clauses)

    @classmethod
    def _exists_by(cls, filter_clause):
        return cls.query.filter(filter_clause).exists()

    @classmethod
    def _bulk_update(cls, data, *filter_clauses):
        """
        Performs a bulk update to all objects matching the given filters.
        This operation bypass the ORM and rely on the DB for greater
        performance.

        For more details, see SQLAlchemy docs:
        (http://docs.sqlalchemy.org/en/rel_1_1/orm/query.html
        #sqlalchemy.orm.query.Query.update)

        :param filter_clauses: One or more filter clauses to be filter the
            objects which will receive the update.
        :param data: a dictionary with the key, value pairs to be updated.
        :return: The count of modified rows.
        """
        if not filter_clauses:
            raise ValueError('At least one filter_clause needs to be given')

        query = cls.query
        for filter_clause in filter_clauses:
            query = query.filter(filter_clause)

        updated_count = query.update(data, synchronize_session=False)
        db.session.flush()
        return updated_count

    @classmethod
    def bulk_archive(cls, *filter_clauses, commit=False):
        """
        Performs a bulk archive operation on all objects matching the given filters.
        Sets archived_at to the current UTC timestamp.

        :param filter_clauses: One or more filter clauses to filter the
            objects which will be archived.
        :param commit: Whether to commit the transaction (default: False, uses flush)
        :return: The count of archived rows.
        """
        if not filter_clauses:
            raise ValueError('At least one filter_clause needs to be given')

        query = cls.query
        for filter_clause in filter_clauses:
            query = query.filter(filter_clause)

        archive_timestamp = get_aware_utc_now()
        updated_count = query.update({'archived_at': archive_timestamp}, synchronize_session=False)

        if commit:
            db.session.commit()
        else:
            db.session.flush()

        return updated_count

    @classmethod
    def _search(cls, search: str, include: list, query, override_searchable_fields=None):
        """
        Generic method to generate a search query based on the
        _searchable_fields list.
        :param search: value to be searched on the searchable fields
        :return: query
        """
        filters = []
        include = include if include else []
        searchable_fields = (
            override_searchable_fields if override_searchable_fields else cls._searchable_fields
        )

        for field in searchable_fields:

            # Handle required includes
            required_include = searchable_fields.get(field)
            if required_include and required_include not in include:
                continue

            if isinstance(field, tuple):
                # Concat multiple fields with ' ' if they are in a tuple
                _fields = []
                for f in field:
                    query, _field, _ = cls._get_field_alias(query, f)
                    _fields.append(_field)
                search_field = reduce(lambda first, second: first + ' ' + second, _fields)
            else:
                # Regular case of searching one column at a time
                query, search_field, _ = cls._get_field_alias(query, field)

            filters.append(search_field.like(f'%{search}%'))
        if len(filters) > 0:
            query = query.filter(or_(*filters))
        return query

    @classmethod
    def _get_field_alias(cls, query, field):
        try:
            nodes = field.split('.')
            rel_alias = None
            clz = cls
            for node in nodes[:-1]:
                attr = getattr(clz, node)
                rel_alias = aliased(attr.mapper.class_)
                query = query.outerjoin(attr.of_type(rel_alias))
                clz = attr.property.mapper.class_
            node = nodes.pop()
            search_field = getattr(rel_alias if rel_alias else cls, node)
            return query, search_field, node
        except AttributeError:
            raise InvalidSearchableField(node)

    @classmethod
    def save(cls, model, commit=False):
        if isinstance(model, cls):
            try:
                db.session.add(model)
                if commit:
                    db.session.commit()
                else:
                    db.session.flush()
                return model
            except IntegrityError as err:
                db.session.rollback()
                logger.warn(f'Error saving {cls}', exc_info=err)
                integrity_messages = {
                    '(pymysql.err.IntegrityError) (1062',  # mysql error msg
                    'UNIQUE constraint failed',  # sqlite error msg
                }

                if any(x in str(err) for x in integrity_messages):
                    raise ObjectConflictError(
                        type(model),
                        integrity_err_msg=err.args[0].lower(),
                    ) from err
                raise InvalidObjectError(
                    type(model),
                    integrity_err_msg=err.args[0].lower(),
                ) from err
        else:
            raise TypeError(f'Given model is not instance of {cls}')

    @classmethod
    def add(cls, model):
        if isinstance(model, cls):
            db.session.add(model)
        else:
            raise TypeError(f'Given model is not instance of {cls}')

    @classmethod
    def reload_data(cls):

        """
        When we delete items explicitly and then try to update their
        parent using merge(), SQLAlchemy might encounter a conflict
        because the deleted items are still referenced in the session,
        so we need to make sure the deleted items are removed from the
        session before updating the parent

        SQLAlchemy doesn't realise it needs to query the database
        to update the relationship.

        session.expire_all() after the flush, forces SQLAlchemy to
        reload every model instance and relationship when they're
        next accessed.
        """
        try:
            db.session.flush()
            db.session.expire_all()
        except IntegrityError as err:
            cls._rollback(err)

    @classmethod
    def _rollback(cls, err):
        db.session.rollback()
        logger.warn(f'Error saving {cls}', exc_info=err)
        integrity_messages = {
            '(pymysql.err.IntegrityError) (1062',  # mysql error msg
            'UNIQUE constraint failed',  # sqlite error msg
        }

        if any(x in str(err) for x in integrity_messages):
            raise ObjectConflictError(cls) from err
        raise InvalidObjectError(cls) from err

    @classmethod
    def delete(cls, model):
        if isinstance(model, cls):
            try:
                db.session.delete(model)
                db.session.flush()
                # flushing a deleted object already moves the object to delete
                # state. Expire works only for objects with state persistent
                # See sqlalchemy session states documentation
                return model
            except IntegrityError as err:
                logger.info(f'Integrity error on delete of {model}:{err}')
                db.session.rollback()
                raise ObjectRelationshipError(type(model)) from err

        else:
            raise TypeError(f'Given model is not instance of {cls}')

    @classmethod
    def _delete_by_id(cls, m_id, **filters):
        try:
            model = cls.get_by_id(m_id, **filters)
            db.session.delete(model)
            db.session.flush()
        except ObjectNotFoundError:
            pass
        except IntegrityError as err:
            logger.info('Integrity error on delete by id')
            db.session.rollback()
            raise ObjectRelationshipError(cls) from err

    @classmethod
    def delete_by_id_and_filter(cls, m_id, *filter_clauses):
        id_field = cls.get_id_field()
        try:
            query = cls.query.filter(id_field == m_id)
            for filter_clause in filter_clauses:
                query = query.filter(filter_clause)
            db.session.delete(query.one())
            db.session.flush()
        except IntegrityError as err:
            logger.info(f'Integrity error on delete by {id_field} of {m_id}')
            db.session.rollback()
            raise ObjectRelationshipError(cls) from err
        except NoResultFound:
            pass

    @classmethod
    def delete_by_filter(cls, *filter_clauses):
        try:
            query = cls.query
            for filter_clause in filter_clauses:
                query = query.filter(filter_clause)
            instance = query.one()
            db.session.delete(instance)
            db.session.flush()
        except IntegrityError as err:
            logger.info('Integrity error on delete with given filters')
            db.session.rollback()
            raise ObjectRelationshipError(cls) from err
        except NoResultFound:
            # Handle the case where no record is found.
            pass

    @classmethod
    def bulk_delete_by_filters(cls, *filter_clauses):
        """This method will delete all the model objects matching the required filters"""
        try:
            query = cls.query
            for filter_clause in filter_clauses:
                query = query.filter(filter_clause)
            deleted_rows = query.delete()
            db.session.flush()
            return deleted_rows
        except IntegrityError as err:
            logger.info('Integrity error on delete all by filters')
            db.session.rollback()
            raise ObjectRelationshipError(cls) from err
        except NoResultFound:
            pass
        return None

    @classmethod
    def bulk_upsert(cls, models):

        """This method will create/update all the model objects once"""

        if not isinstance(models, list):
            models = [models] if models else []
        try:
            db.session.add_all(models)
            db.session.flush()
        except IntegrityError as err:
            cls._rollback(err)

    @classmethod
    def get_by_id(cls, pk_id, query_ext_funcs=None, **filters):
        """ Efficient lookup for a single model by its primary key.

        Key value filters can be passed in to limit the results, such as to
        enforce authz.
        """
        q = cls.query
        if not isinstance(query_ext_funcs, list):
            query_ext_funcs = [query_ext_funcs] if query_ext_funcs else []
        for func_ in query_ext_funcs:
            q = func_(q)
        model = q.get(pk_id) if pk_id else None
        if not model:
            raise ObjectNotFoundError(cls)
        for field_name, expected_value in filters.items():
            if getattr(model, field_name) != expected_value:
                raise ObjectNotFoundError(cls)
        return model

    @classmethod
    def get_by_context(cls, context_type, context_id):
        """
        Efficient lookup for a single model by its context.
        Simple Search by context_type and context_id when there is a singleton
        model for a given context.
        """
        q = cls.query
        q = q.filter_by(
            context_type=context_type,
            context_id=context_id
        )
        model = q.one_or_none()
        if not model:
            raise ObjectNotFoundError(cls)
        return model

    @classmethod
    def get_by_event_id(cls, event_id):
        """
        Efficient lookup for a single model by its event_id. Used for singleton models.
        """
        q = cls.query
        q = q.filter_by(
            event_id=event_id
        )
        model = q.one_or_none()
        if not model:
            raise ObjectNotFoundError(cls)
        return model

    @classmethod
    def get_id_field(cls):
        return getattr(cls, cls._id_fieldname)

    @classmethod
    def exists_by_id(cls, m_id):
        return cls._exists_by(cls.get_id_field() == m_id)

    @classmethod
    def all(cls):
        for i in cls.query:
            yield i

    @classmethod
    def count(cls, *filter_clauses):
        if not filter_clauses:
            return cls.query.count()
        query = cls.query
        for filter_clause in filter_clauses:
            query = query.filter(filter_clause)
        return query.count()

    @classmethod
    def filter_by_created_updated_at(cls, query, filters: QueryFilters):
        if filters.is_set('created_before'):
            query = query.filter(cls.created_at < filters.created_before)
        if filters.is_set('created_after'):
            query = query.filter(cls.created_at > filters.created_after)
        if filters.is_set('updated_before'):
            query = query.filter(cls.updated_at < filters.updated_before)
        if filters.is_set('updated_after'):
            query = query.filter(cls.updated_at > filters.updated_after)
        return query

    @classmethod
    def _query_include(cls, query, rel, load_eager=False):
        """ Includes a related model by explicitly loading it through a join.

        :param query: The query to join the related model into
        :param rel: A model relationship, such as a child model
        :param load_eager: A flag indicating if the relationship should be
        loaded eagerly. Relationships that don't belong to the root entity
        must set load_eager to False, only relationships belonging to the
        query's root entity can be loaded eagerly.
        :return: Tuple of the updated query, and an alias to the joined table

        For further details, see reference docs here:
        https://docs.sqlalchemy.org/en/13/orm/loading_relationships.html#
        routing-explicit-joins-statements-into-eagerly-loaded-collections
        """

        # Generate an explict alias handle for the joined relationship
        rel_alias = aliased(rel)

        # Join the related table through an outer join and loads the related
        # table's data through the join with the alias
        query = query.outerjoin((rel_alias, rel))
        if load_eager:
            query = query.options(
                contains_eager(rel, alias=rel_alias)
            )

        return query, rel_alias

    @classmethod
    def from_resource(cls, resource, mapping=None, override=None):
        """
        A cleaner interface for creating a SQLAlchemy Model from a Flux
        Contract. Initiates an instance of the Model with data copied over
        from the Contract based on matching field names.
        :param resource: The contract instance with data to transfer over.
        :param mapping: A dictionary mapping of contract_name:model_name,
            allows for discrepancies in the contract and model naming. A
            mapping of a field to a None value will skip modifying the field.
        :param override: A dictionary that sets the Model field based on the
            override key to the value provided in the dictionary.
        :return: An instance of the SQLALchemy Model.
        """
        model = resource.to_obj(cls(), mapping, override)
        return model

    def update_from_dict(self, data, mapping=None):
        """
        A cleaner interface for updating a SQLAlchemy Model instance from a
        dict. Copies data over from the passed in dict to the current
        SQLALchemy Model instance. This method mutates the data in the
        SQLAlchemy instance, and does not have a return value.

        :param data: The dict with data to transfer over.
        :param mapping: A dictionary mapping of contract_name:model_name,
            allows for discrepancies in the contract and model naming. A
            mapping of a field to a None value will skip modifying the field.
        """
        for field, value in data.items():
            mapped_name = field

            # Convert name to mapped name if available,
            # else use Contract's existing name
            if mapping and field in mapping:
                mapped_name = mapping[field]
                if not mapped_name:
                    # None value indicates that we should skip this field
                    continue

            if hasattr(self, mapped_name) and not isinstance(
                    getattr(type(self), mapped_name, None), property):
                setattr(self, mapped_name, value)

        return self

    @staticmethod
    def incremented_attribute_value(model_attribute, filters):
        """
        Returns the correct incremented value of the `model_attribute` for a new row.
        Example uses:
            - Get the order/position for the new row in a table.
        """
        q = db.session.query(func.max(model_attribute))

        for filter_clause in filters:
            q = q.filter(filter_clause)

        max_val = q.scalar()
        return max_val + 1 if max_val is not None else 0

    @classmethod
    def _sort_and_paginate(cls, query, page_info, default_sort=None, paginate_in_outer_query=False,
                           override_sort_mapping=None, pagination_id_override=None):
        """
        :param query: Query object
        :param page_info: PaginationInfo object
        :param default_sort default field to sort by
        :return: Query object
        """

        if not page_info:
            return cls._set_default_sorting(query, default_sort)

        _id_field = pagination_id_override if pagination_id_override else cls.get_id_field()

        # Get total count of items in the collection for pagination metadata
        page_info.total_items = query.with_entities(_id_field).count()

        # In some situations it is more efficient to apply offset / limit as a wrapper
        if paginate_in_outer_query:
            _query = query.with_entities(_id_field)
            query = cls.query.filter(_id_field.in_(_query))

        if len(page_info.sorting_fields):
            if override_sort_mapping:
                _sortable_fields = override_sort_mapping
            else:
                _sortable_fields = cls._sortable_fields

            for sort in page_info.sorting_fields:

                if sort.field_name not in _sortable_fields.keys():
                    raise SortingFieldError(field=sort.field_name)

                # Determine if the sorting should be applied on the aggregate value of field
                sort_by_aggregate = False
                if isinstance(_sortable_fields.get(sort.field_name, None), dict):
                    rel_field = _sortable_fields.get(sort.field_name, None).get('field', None)
                    sort_by_aggregate = _sortable_fields.get(sort.field_name, None).get(
                        'sort_by_aggregate', False
                    )

                else:
                    rel_field = _sortable_fields.get(sort.field_name, None)

                if sort_by_aggregate:
                    # Aggregate functions require group by
                    query = query.group_by(cls.id)

                if isinstance(rel_field, (Case, TextClause, InstrumentedAttribute)):
                    # Sortable field maps to a sqlalchemy TextClause or Case, use it directly
                    if sort.desc:
                        query = query.order_by(
                            desc(func.max(rel_field) if sort_by_aggregate else rel_field)
                        )
                    else:
                        query = query.order_by(
                            asc(func.min(rel_field) if sort_by_aggregate else rel_field)
                        )
                else:
                    if isinstance(rel_field, str):
                        # Sortable field maps to a string, assumed to be the relative path to a
                        # related field
                        query, attr, _ = cls._get_field_alias(
                            query,
                            rel_field,
                        )
                    else:
                        try:
                            # Find column attribute and order the query
                            attr = getattr(
                                query.column_descriptions[0]['entity'],
                                sort.field_name
                            )
                        except AttributeError as e:
                            raise SortingError from e
                    if sort.desc:
                        query = query.order_by(
                            attr.isnot(None),
                            desc(func.max(attr) if sort_by_aggregate else attr),
                        )
                    else:
                        query = query.order_by(
                            attr.is_(None),
                            asc(func.min(attr) if sort_by_aggregate else attr),
                        )
        else:
            query = cls._set_default_sorting(query, default_sort)

        query_offset = page_info.page_offset * page_info.items_per_page
        query = query.offset(query_offset)
        query = query.limit(page_info.items_per_page)
        return query

    @classmethod
    def _set_default_sorting(cls, query, default_sort):
        if default_sort is not None:
            if isinstance(default_sort, (tuple, list)):
                query = query.order_by(*default_sort)
            else:
                query = query.order_by(default_sort)
        return query


class Id(db.Model):  # type: ignore
    __abstract__ = True

    @declared_attr
    def id(cls):
        return db.Column(UUID, primary_key=True, default=uuid.uuid4)


class AwareCreatedAt(db.Model):  # type: ignore
    __abstract__ = True
    _created_at_index = False

    @declared_attr
    def created_at(cls):
        return db.Column(
            AwareDateTime,
            nullable=True,
            default=get_aware_utc_now,
            index=cls._created_at_index,
        )


class AwareUpdatedAt(db.Model):  # type: ignore
    __abstract__ = True
    _updated_at_index = False

    @declared_attr
    def updated_at(cls):
        return db.Column(
            AwareDateTime,
            nullable=True,
            default=get_aware_utc_now,
            onupdate=get_aware_utc_now,
            index=cls._updated_at_index,
        )


class AwareCreatedAtNoDefault(db.Model):  # type: ignore
    __abstract__ = True
    _created_at_index = False

    @declared_attr
    def created_at(cls):
        return db.Column(
            AwareDateTime,
            nullable=False,
            index=cls._created_at_index,
        )


class AwareUpdatedAtNoDefault(db.Model):  # type: ignore
    __abstract__ = True
    _updated_at_index = False

    @declared_attr
    def updated_at(cls):
        return db.Column(
            AwareDateTime,
            nullable=False,
            default=get_aware_utc_now,
            index=cls._updated_at_index,
        )


class SoftDelete(db.Model):  # type: ignore
    __abstract__ = True
    _deleted_at_index = False

    @declared_attr
    def deleted_at(cls):
        return db.Column(
            db.DATETIME,
            nullable=True,
            default=None,
            index=cls._deleted_at_index,
        )

    @classmethod
    def delete(cls, model, hard_delete=False):
        """ Overrides Model Manager delete """
        if not hard_delete:
            model.deleted_at = datetime.now(timezone.utc)
            return cls.save(model)
        db.session.delete(model)
        db.session.flush()
        return None


class FluxModel2(Id, AwareCreatedAt, AwareUpdatedAt, FluxModel):
    __abstract__ = True
    _created_at_index = True
    _updated_at_index = True


class EntityModel(Id, AwareCreatedAtNoDefault, AwareUpdatedAtNoDefault, FluxModel):
    __abstract__ = True
    _created_at_index = True
    _updated_at_index = True


class EntityModelNoUpdates(Id, AwareCreatedAtNoDefault, FluxModel):
    """ Same as EntityModel but without updated_at attribute """
    __abstract__ = True
    _created_at_index = True


class CreationSourceModelMixin(db.Model):  # type: ignore
    __abstract__ = True

    @declared_attr
    def creation_source(self):
        return db.Column(
            ProperEnum(20, enum_cls=CreationSourceType),
            nullable=False,
            default=CreationSourceType.uapi_private,
        )
