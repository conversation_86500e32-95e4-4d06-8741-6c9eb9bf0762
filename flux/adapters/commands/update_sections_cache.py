import click

from flask import current_app as flask_app

from flux.flux_services.common import get_internal_flux_client

from .helpers import terminate, load_event


def get_section_types_ids(ud, event):
    from flux.adapters.sections.common import (
        UNGROUPED_SECTION_TYPE_MAP
    )

    ea = ud.event_apps.single({'event_id': event.id})
    sections = ud.sections.read({'event_app_id': ea.id})
    types_ids = {(section.type, str(section.id)) for section in sections}
    types_ids.update(UNGROUPED_SECTION_TYPE_MAP.items())
    return types_ids


@flask_app.cli.command()  # type: ignore
@click.option('--event_id', default=None)
@click.option('--shortcode', default=None)
@click.option(
    '--operation',
    type=click.Choice(['invalidate', 'force_refill']),
    required=True,
)
def update_sections_cache(event_id, shortcode, operation):
    """
    Manage sections cache for specified event
    """
    from flux.adapters.sections.section_builder import (
        get_section_builder,
        is_section_cache_enabled,
    )
    ud = get_internal_flux_client(origin='cmd.update_sections_cache')
    event = load_event(ud, event_id, shortcode)

    if not is_section_cache_enabled():
        terminate('Section cache is not enabled for the event')

    builder = get_section_builder(ud, user_id=None, event_id=event.id)
    section_types_ids = get_section_types_ids(ud, event)
    for (_, section_id) in section_types_ids:
        section = builder._get_section(section_id)
        click.secho(
            f'Handling section {section["id"]} ({section["type"]}, '
            f'"{section["name"]}")',
            fg='green',
        )
        if operation == 'invalidate':
            builder._complete_section_data(section).invalidate()
        elif operation == 'force_refill':
            builder._complete_section_data(section).refill_cache(
                updated_at=None,
                force_refill=True,
            )
