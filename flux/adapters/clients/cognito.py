import enum
import base64
import jwt
import requests
from dataclasses import dataclass
from typing import Optional

import boto3

from flux.adapters.logging import get_logger
from flux.utils.enum import StringEnum
from flux.apps.config import app_config
from flux.domains.common.errors import (
    NotFoundError,
    AuthenticationError,
    InternalError,
    InvalidOperationError,
    aws_exception,
)

logger = get_logger(__name__)


@dataclass
class UserPool:
    name: str
    id: str
    arn: str
    domain: str
    resource_server: str
    client_id: str
    client_secret: str


@dataclass
class IdentityProviderMetadata:
    email: str
    given_name: str
    family_name: str
    oidc_client_id: Optional[str] = None
    oidc_client_secret: Optional[str] = None
    oidc_issuer: Optional[str] = None
    oidc_attributes_request_method: Optional[str] = None
    saml_metadata_url: Optional[str] = None
    saml_metadata_file: Optional[str] = None


@dataclass
class User:
    email: str
    given_name: str
    family_name: str
    username: str


class ProviderType(StringEnum):
    SAML = enum.auto()
    OIDC = enum.auto()


class IdpTypeNotFoundError(NotFoundError):
    pass


class OauthTokenError(AuthenticationError):
    pass


class CognitoAPIError(InternalError):
    pass


class InvalidEnvironmentError(InvalidOperationError):
    pass


class Cognito:

    def __init__(self):
        if app_config.is_dev_environment_and_dev_aws():
            self._client = boto3.client(
                'cognito-idp',
                region_name=app_config.AWS_REGION_NAME,
                endpoint_url=app_config.AWS_DEV_ENDPOINT,
            )
        else:
            self._client = boto3.client(
                'cognito-idp',
                region_name=app_config.AWS_REGION_NAME,
            )

    @staticmethod
    def _create_domain_prefix(pool_name):
        return 'em-' + pool_name

    def _create_user_pool_domain(self, user_pool_id, pool_name):
        """
        Domain names can only contain lower-case letters, numbers, and hyphens.
        ^[a-z0-9](?:[a-z0-9\\-]{0,61}[a-z0-9])?$
        If domain is already taken we will try with another uuid
        """
        domain = self._create_domain_prefix(pool_name)
        self._client.create_user_pool_domain(
            Domain=domain,
            UserPoolId=user_pool_id,
        )
        return 'https://' + domain + '.auth.' + app_config.AWS_REGION_NAME + '.amazoncognito.com'

    def _create_resource_server(self, user_pool_id, server_name):
        response = self._client.create_resource_server(
            UserPoolId=user_pool_id,
            Identifier='https://' + app_config.MOBILEAPP_DOMAIN,
            Name=server_name,
            Scopes=[],
        )
        return response['ResourceServer']['Identifier']

    def _create_user_pool_client(self, user_pool_id, client_name='EventApp'):
        response = self._client.create_user_pool_client(
            UserPoolId=user_pool_id,
            ClientName=client_name,
            GenerateSecret=True,
            RefreshTokenValidity=30,
            AccessTokenValidity=5,
            IdTokenValidity=5,
            TokenValidityUnits={
                'AccessToken': 'minutes',
                'IdToken': 'minutes',
                'RefreshToken': 'days',
            },
            ReadAttributes=[
                'email',
                'email_verified',
                'given_name',
                'family_name',
            ],
            WriteAttributes=[
                'email',
                'given_name',
                'family_name',
            ],
            ExplicitAuthFlows=[
                'ALLOW_ADMIN_USER_PASSWORD_AUTH',
                'ALLOW_CUSTOM_AUTH',
                'ALLOW_USER_SRP_AUTH',
                'ALLOW_REFRESH_TOKEN_AUTH',
            ],
            SupportedIdentityProviders=[],
            CallbackURLs=[
                'https://' + app_config.MOBILEAPP_DOMAIN + app_config.SSO_CALLBACK,
            ],
            LogoutURLs=[],
            DefaultRedirectURI='https://' + app_config.MOBILEAPP_DOMAIN + app_config.SSO_CALLBACK,
            AllowedOAuthFlows=[
                'code',
            ],
            AllowedOAuthScopes=[
                'openid',
                'email',
                'profile',
                'aws.cognito.signin.user.admin',
            ],
            AllowedOAuthFlowsUserPoolClient=True,
            PreventUserExistenceErrors='ENABLED',
        )
        return response['UserPoolClient']['ClientId'], response['UserPoolClient']['ClientSecret']

    @aws_exception(CognitoAPIError)
    def create_user_pool(self, pool_name):
        """
        Schema attributes max length matches respective db attribute
        """
        response = self._client.create_user_pool(
            PoolName=pool_name,
            UsernameAttributes=[
                'email',
            ],
            MfaConfiguration='OFF',
            UserPoolTags={
                'environment': app_config.ENV,
            },
            AdminCreateUserConfig={
                'AllowAdminCreateUserOnly': True,
            },
            Schema=[
                {
                    'Name': 'email',
                    'AttributeDataType': 'String',
                    'Mutable': True,
                    'Required': True,
                    'StringAttributeConstraints': {
                        'MinLength': '1',
                        'MaxLength': '100',
                    }
                },
                {
                    'Name': 'given_name',
                    'AttributeDataType': 'String',
                    'Mutable': True,
                    'Required': True,
                    'StringAttributeConstraints': {
                        'MinLength': '1',
                        'MaxLength': '256',
                    }
                },
                {
                    'Name': 'family_name',
                    'AttributeDataType': 'String',
                    'Mutable': True,
                    'Required': True,
                    'StringAttributeConstraints': {
                        'MinLength': '1',
                        'MaxLength': '256',
                    }
                },
            ],
            UserPoolAddOns={
                'AdvancedSecurityMode': 'OFF'
            },
            UsernameConfiguration={
                'CaseSensitive': False
            },
            AccountRecoverySetting={
                'RecoveryMechanisms': [
                    {
                        'Priority': 1,
                        'Name': 'admin_only'
                    },
                ]
            }
        )
        domain = self._create_user_pool_domain(response['UserPool']['Id'], pool_name)
        resource_server = self._create_resource_server(response['UserPool']['Id'], pool_name)
        client_id, client_secret = self._create_user_pool_client(response['UserPool']['Id'])
        return UserPool(
            name=response['UserPool']['Name'],
            id=response['UserPool']['Id'],
            arn=response['UserPool']['Arn'],
            domain=domain,
            resource_server=resource_server,
            client_id=client_id,
            client_secret=client_secret,
        )

    def _delete_user_pool_domain(self, user_pool_id, pool_name):
        domain = self._create_domain_prefix(pool_name)
        self._client.delete_user_pool_domain(
            Domain=domain,
            UserPoolId=user_pool_id,
        )

    @aws_exception(CognitoAPIError)
    def delete_user_pool(self, user_pool_id, pool_name, force_delete_non_prod=False):
        user_pool = self._client.describe_user_pool(
            UserPoolId=user_pool_id,
        )
        user_pool_env = user_pool['UserPool']['UserPoolTags']['environment']
        if force_delete_non_prod:
            if user_pool_env not in ['dev', 'stage']:
                return
        elif user_pool_env != app_config.ENV:
            raise InvalidEnvironmentError(message='Environment mismatch')

        self._delete_user_pool_domain(user_pool_id, pool_name)
        self._client.delete_user_pool(
            UserPoolId=user_pool_id,
        )
        logger.info(
            f'Deleted SSO user pool (force_delete {force_delete_non_prod}): {user_pool_id}'
            f' - {pool_name} - {user_pool_env}'
        )

    def _add_identity_provider_to_user_pool_client(
        self,
        user_pool_id,
        client_id,
        identity_provider,
    ):
        identity_providers = []
        user_pool_client = self._client.describe_user_pool_client(
            UserPoolId=user_pool_id,
            ClientId=client_id,
        )['UserPoolClient']
        if 'SupportedIdentityProviders' in user_pool_client:
            identity_providers = user_pool_client['SupportedIdentityProviders']
        identity_providers.append(identity_provider)
        self._client.update_user_pool_client(
            UserPoolId=user_pool_id,
            ClientId=client_id,
            RefreshTokenValidity=user_pool_client['RefreshTokenValidity'],
            AccessTokenValidity=user_pool_client['AccessTokenValidity'],
            IdTokenValidity=user_pool_client['IdTokenValidity'],
            TokenValidityUnits=user_pool_client['TokenValidityUnits'],
            ReadAttributes=user_pool_client['ReadAttributes'],
            WriteAttributes=user_pool_client['WriteAttributes'],
            ExplicitAuthFlows=user_pool_client['ExplicitAuthFlows'],
            SupportedIdentityProviders=identity_providers,
            CallbackURLs=user_pool_client['CallbackURLs'],
            LogoutURLs=user_pool_client['LogoutURLs'] if 'LogoutURLs' in user_pool_client else [],
            DefaultRedirectURI=user_pool_client['DefaultRedirectURI'],
            AllowedOAuthFlows=user_pool_client['AllowedOAuthFlows'],
            AllowedOAuthScopes=user_pool_client['AllowedOAuthScopes'],
            AllowedOAuthFlowsUserPoolClient=user_pool_client['AllowedOAuthFlowsUserPoolClient'],
            PreventUserExistenceErrors=user_pool_client['PreventUserExistenceErrors'],
        )

    @aws_exception(CognitoAPIError)
    def create_identity_provider(
        self,
        user_pool_id,
        client_id,
        provider_name,
        provider_type,
        provider_metadata: IdentityProviderMetadata,
    ):
        if provider_type == ProviderType.OIDC.name:
            provider_details = {
                'client_id': provider_metadata.oidc_client_id,
                'client_secret': provider_metadata.oidc_client_secret,
                'attributes_request_method': provider_metadata.oidc_attributes_request_method,
                'authorize_scopes': 'openid email profile',
                'oidc_issuer': provider_metadata.oidc_issuer,
            }
            attribute_mapping = {
                'email': provider_metadata.email,
                'given_name': provider_metadata.given_name,
                'family_name': provider_metadata.family_name,
            }
        elif provider_type == ProviderType.SAML.name:
            if provider_metadata.saml_metadata_url:
                provider_details = {
                    'MetadataURL': provider_metadata.saml_metadata_url,
                }
            else:
                provider_details = {
                    'MetadataFile': provider_metadata.saml_metadata_file,
                }
            attribute_mapping = {
                'email': provider_metadata.email,
                'given_name': provider_metadata.given_name,
                'family_name': provider_metadata.family_name,
            }
        else:
            raise IdpTypeNotFoundError(message=f'Provider {provider_type} not supported')

        identity_provider = self._client.create_identity_provider(
            UserPoolId=user_pool_id,
            ProviderName=provider_name,
            ProviderType=provider_type,
            ProviderDetails=provider_details,
            AttributeMapping=attribute_mapping,
        )['IdentityProvider']['ProviderName']
        self._add_identity_provider_to_user_pool_client(
            user_pool_id,
            client_id,
            identity_provider,
        )

    @aws_exception(CognitoAPIError)
    def delete_identity_provider(self, user_pool_id, provider_name):
        user_pool = self._client.describe_user_pool(
            UserPoolId=user_pool_id,
        )
        if user_pool['UserPool']['UserPoolTags']['environment'] != app_config.ENV:
            raise InvalidEnvironmentError(message='Environment mismatch')
        self._client.delete_identity_provider(
            UserPoolId=user_pool_id,
            ProviderName=provider_name,
        )

    @aws_exception(CognitoAPIError)
    def list_user_pools(self, max_results=60, next_token=None):
        if next_token:
            return self._client.list_user_pools(
                NextToken=next_token,
                MaxResults=max_results,
            )
        else:
            return self._client.list_user_pools(
                MaxResults=max_results,
            )

    @staticmethod
    def get_user(client_id, client_secret, auth_code, domain):
        encoded_authorization = base64.b64encode(f'{client_id}:{client_secret}'.encode()).decode()
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': f'Basic {encoded_authorization}',
        }
        request_body = {
            'grant_type': 'authorization_code',
            'client_id': client_id,
            'code': auth_code,
            'redirect_uri': 'https://' + app_config.MOBILEAPP_DOMAIN + app_config.SSO_CALLBACK,
        }
        response = requests.post(domain + '/oauth2/token', data=request_body, headers=headers)
        if response.status_code != 200:
            logger.error(f'SSO - Oauth2 Token error - status: {response.status_code} - reason: '
                         f'{response.text}')
            raise OauthTokenError(message=f'Error in getting user info')

        oauth_token = response.json()
        user = jwt.decode(oauth_token['id_token'], options={"verify_signature": False})
        return User(
            email=user['email'],
            given_name=user['given_name'],
            family_name=user['family_name'],
            username=user['cognito:username'],
        )
