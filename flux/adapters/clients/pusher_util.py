from pusher import Pusher

from flux.apps.config import app_config

from flux.utils.serializer import ExtendedEncoder
from flux.adapters.celery import celery as celery_app
from flux.adapters.logging import get_logger

logger = get_logger(__name__)


class PusherClient(Pusher):

    @staticmethod
    def trigger_async(channels, event_name, data, channel_type, socket_id=None):
        trigger_pusher_async.delay(channels, event_name, data, channel_type, socket_id)

    @staticmethod
    def trigger_batch_async(batch):
        trigger_pusher_batch_async.delay(batch)


@celery_app.task()  # type: ignore
def trigger_pusher_async(channel, event_name, data, channel_type, socket_id=None):
    pusher_client = get_pusher_client()
    if channel_type == 'event':
        logger.info('Sending Async Pusher Msg to Event:', channel=channel, event_name=event_name,
                    data=data, socket_id=socket_id)
    else:
        logger.info('Sending Async Pusher Msg to Event Person:',
                    channel=channel, event_name=event_name,
                    data=data, socket_id=socket_id)

    response = pusher_client.trigger(channel, event_name, data, socket_id)

    if channel_type == 'event':
        logger.info('Async Pusher Msg Sent to Event', response=response)
    else:
        logger.info('Async Pusher Msg Sent to Event Person', response=response)


@celery_app.task()  # type: ignore
def trigger_pusher_batch_async(batch):
    pusher_client = get_pusher_client()
    event_name = batch[0]['name']
    logger.info(
        'Sending Async Pusher Msg to Event People Batch',
        event_name=event_name,
        data=batch
    )
    response = pusher_client.trigger_batch(batch)
    logger.info('Async Pusher Msg Sent to Event People Batch', response=response)


def get_pusher_client():
    pusher = PusherClient(
        app_id=str(app_config.PUSHER_APP_ID),
        key=app_config.PUSHER_KEY,
        secret=app_config.PUSHER_SECRET,
        cluster=app_config.PUSHER_CLUSTER,
        ssl=True,
        json_encoder=ExtendedEncoder,
    )
    return pusher
