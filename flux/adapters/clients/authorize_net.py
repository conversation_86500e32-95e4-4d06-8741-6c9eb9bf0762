"""
Parks implementation of Authorize.net client
"""
import json

import requests

from flux.adapters.logging import get_logger
from flux.domains.common.errors import PublicError, InternalError

logger = get_logger(__name__)


class AuthorizeNetAPIError(PublicError):
    code = "authorize_net_api_error"


class MalformedAPIResponseException(InternalError):
    code = "authorize_net_malformed_response"
    message = "Failed to parse error response from Authorize.Net API."


class AuthorizeNetClient:
    API_URL = "https://{}.authorize.net/xml/v1/request.api"
    SANDBOX_HOST = "apitest"
    PRODUCTION_HOST = "api"

    def __init__(self, name, transaction_key, is_sandbox):
        self._name = name
        self._transaction_key = transaction_key
        self.is_sandbox = is_sandbox
        self._host = self.SANDBOX_HOST if is_sandbox else self.PRODUCTION_HOST
        self.api_url = self.API_URL.format(self._host)
        self.session = requests.Session()
        headers = {"Accept": "application/json", "Content-Type": "application/json"}
        self.session.headers = headers
        self.session.hooks["response"].append(self._response_processor)

    @staticmethod
    def _parse_response(response):
        try:
            # Check if response starts with BOM
            content = response.content
            if content.startswith(b'\xef\xbb\xbf'):
                return json.loads(content.decode('utf-8-sig'))
            return response.json()
        except json.JSONDecodeError as e:
            raise MalformedAPIResponseException from e

    def _response_processor(self, response, *args, **kargs):
        if response.status_code == 200:
            try:
                response_data = self._parse_response(response)
                messages = response_data.get("messages", {})
                if messages.get("resultCode") == "Error":
                    error_message = messages.get("message", [])[0]
                    error_code = error_message.get("code")
                    error_text = error_message.get("text")
                    logger.error(
                        error_text, error_code=error_code, content=response.content
                    )
                    raise AuthorizeNetAPIError(message=error_text, params=response_data)
            except (ValueError, IndexError, KeyError) as e:
                raise MalformedAPIResponseException from e

    def get_merchant_details(self):
        """
        Retrieves merchant details from Authorize.Net.
        """
        payload = {
            "getMerchantDetailsRequest": {
                "merchantAuthentication": {
                    "name": self._name,
                    "transactionKey": self._transaction_key,
                }
            }
        }

        response = self.session.post(url=self.api_url, json=payload)
        return self._parse_response(response)


def get_authorize_net_client(name, transaction_key, is_sandbox):
    """
    Factory function to create an AuthorizeNetClient instance.
    """
    return AuthorizeNetClient(name, transaction_key, is_sandbox)
