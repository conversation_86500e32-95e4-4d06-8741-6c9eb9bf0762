import enum

import boto3

from flux.adapters.logging import get_logger
from flux.apps.config import app_config
from flux.utils.enum import StringEnum
from flux.domains.common.errors import (
    InternalError,
    InvalidOperationError,
    aws_exception,
)

logger = get_logger(__name__)


class Route53Action(StringEnum):
    CREATE = enum.auto()
    DELETE = enum.auto()


class Route53APIError(InternalError):
    pass


class InvalidRoute53ActionError(InvalidOperationError):
    pass


class Route53:

    def __init__(self):
        if app_config.is_dev_environment_and_dev_aws():
            self._client = boto3.client('route53', endpoint_url=app_config.AWS_DEV_ENDPOINT)
        else:
            self._client = boto3.client('route53')

    @aws_exception(Route53APIError)
    def change_resource_record_sets(self, action, record_name, record_value):
        if action not in Route53Action.all_values_in_string_list():
            raise InvalidRoute53ActionError(message=f'{action} is not valid')
        self._client.change_resource_record_sets(
            HostedZoneId=app_config.AWS_ROUTE53_HOSTED_ZONE_ID,
            ChangeBatch={
                'Changes': [
                    {
                        'Action': action,
                        'ResourceRecordSet': {
                            'Name': record_name,
                            'Type': 'CNAME',
                            'TTL': 300,
                            'ResourceRecords': [
                                {
                                    'Value': record_value,
                                },
                            ],
                        }
                    },
                ]
            }
        )

    @aws_exception(Route53APIError)
    def list_resource_record_sets(self, max_items='500', starter_record_name=None):
        if starter_record_name:
            return self._client.list_resource_record_sets(
                HostedZoneId=app_config.AWS_ROUTE53_HOSTED_ZONE_ID,
                StartRecordName=starter_record_name,
                StartRecordType='CNAME',
                MaxItems=max_items,
            )
        return self._client.list_resource_record_sets(
            HostedZoneId=app_config.AWS_ROUTE53_HOSTED_ZONE_ID,
            MaxItems=max_items,
        )

    @staticmethod
    def record_name(event_prefix):
        return f'{event_prefix}.{app_config.AWS_ROUTE53_HOSTED_ZONE}.'

    @aws_exception(Route53APIError)
    def remove_non_prod_route53_records(self, starter_record_name=None):
        records = self.list_resource_record_sets(
            starter_record_name=starter_record_name,
        )
        for record in records['ResourceRecordSets']:
            try:
                if record['Type'] == 'CNAME' and ('-dev' in record['Name'] or
                                                  '-stage' in record['Name']):
                    self.change_resource_record_sets(
                        Route53Action.DELETE.name,
                        record['Name'],
                        record['ResourceRecords'][0]['Value'],
                    )
                    logger.info(f'Deleted Route53 record: {record["Name"]} - '
                                f'{record["ResourceRecords"][0]["Value"]}')
            except Route53APIError as err:
                logger.exception(f'Unable to check or delete route53 record: {record["Name"]}: '
                                 f'{err}')
                pass
        if 'NextRecordName' in records:
            self.remove_non_prod_route53_records(records['NextRecordName'])
