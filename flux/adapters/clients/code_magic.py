import time
from base64 import b64encode
from datetime import datetime, timedelta
from json import dumps
from requests import Session, HTTPError

from flux.apps.config import app_config
from flux.adapters.logging import get_logger
from flux.domains.common.errors import InternalError
from flux.domains.files.entities import File
from flux.domains.files.images.entities import Image, AssociatedImage
from flux.domains.platform.native_application_build.adapters import ExternalBuildInfo
from flux.domains.platform.native_application.entities import NativeApplication
from flux.domains.platform.native_application_build.entities import (
    BuildStatus,
    MobilePlatform,
    NativeApplicationBuild
)
from flux.utils.timeutils import datetime_from_isoformat

logger = get_logger(__name__)


CODE_MAGIC_PUBLIC_URL_EXPIRATION_HOURS = 2


class CodeMagicAPIError(InternalError):
    pass


class CodeMagicClient:
    def __init__(self):
        self.api_key = app_config.CODE_MAGIC_API_KEY
        self.url_base = app_config.CODE_MAGIC_API_ENDPOINT
        self.code_magic_app_id = app_config.CODE_MAGIC_APP_ID
        self.request_session = Session()
        self.request_session.headers.update(
            {'x-auth-token': self.api_key}
        )

    def get_build_by_id(self, external_build_id: str) -> ExternalBuildInfo:
        res = self.request_session.get(
            self.url_base + f'/builds/{external_build_id}'
        )
        res_json = res.json()

        try:
            res.raise_for_status()
        except HTTPError as e:
            self._error_handling(e, res_json.get('error'))

        build_info = ExternalBuildInfo(
            build_id=res_json['build']['_id'],
            status=res_json['build']['status'],
            started_at=datetime_from_isoformat(res_json['build']['createdAt']),
            finished_at=datetime_from_isoformat(res_json['build'].get('finishedAt')) if res_json[
                'build'].get('finishedAt') else None,
        )
        if res_json['build']['status'] == BuildStatus.failed.value:
            build_info.error_message = res_json['build']['message']
            build_info.error_details = self._extract_error_details(res_json['build'])

        if res_json['build'].get('artefacts'):
            _artifacts = res_json['build']['artefacts']
            build_info.artifact_publish_url = _artifacts[0]['url']
            build_info.artifact_package_url = _artifacts[0]['url']

            for _entry in _artifacts:
                if _entry['type'] == 'aab':
                    build_info.artifact_publish_url = _entry['url']
                elif _entry['type'] == 'apk':
                    build_info.artifact_package_url = _entry['url']
                elif _entry['type'] == 'ipa':
                    build_info.artifact_publish_url = _entry['url']
                    build_info.artifact_package_url = _entry['url']

        return build_info

    def get_artifact_public_url(self, artifact_url: str) -> str:
        # expiresAt needs to be in unix time, like 1675419345
        expires_at = datetime.utcnow() + timedelta(hours=CODE_MAGIC_PUBLIC_URL_EXPIRATION_HOURS)
        expires_at_unix_time = int(time.mktime(expires_at.timetuple()))

        payload = {
            'expiresAt': expires_at_unix_time,
        }
        res = self.request_session.get(
            f'{artifact_url}/public-url',
            json=payload,
        )
        res_json = res.json()
        return res_json['url']

    def create_build(self,
                     build_entity: NativeApplicationBuild,
                     native_application: NativeApplication,
                     mea_list_url: str) -> ExternalBuildInfo:
        payload = {
            'appId': self.code_magic_app_id,
            'branch': build_entity.branch if build_entity.branch else 'master',
            'environment': {},
        }
        self._generate_code_magic_base_payload(
            build_entity, native_application, mea_list_url, payload
        )

        self._generate_code_magic_publish_payload(
            build_entity,
            native_application,
            payload
        )

        res = self.request_session.post(
            self.url_base + '/builds',
            json=payload,
        )
        res_json = res.json()

        try:
            res.raise_for_status()
        except HTTPError as e:
            self._error_handling(e, res_json.get('error'))

        build_id = res_json['buildId']
        return ExternalBuildInfo(
            build_id=build_id,
            status=BuildStatus.queued.value,
            started_at=datetime.utcnow(),
        )

    def cancel_build_by_id(self, external_build_id: str) -> bool:
        res = self.request_session.post(
            self.url_base + f'/builds/{external_build_id}/cancel'
        )
        res_json = res.json()

        try:
            res.raise_for_status()
        except HTTPError as e:
            self._error_handling(e, res_json.get('error'))

        return True

    @staticmethod
    def _error_handling(api_error, error_message=None):
        logger.error(
            'Code Magic API HTTP Error',
            error=f'{api_error.__class__.__name__}: {error_message}',
            exc_info=api_error
        )
        raise CodeMagicAPIError(message=error_message)

    @classmethod
    def _generate_code_magic_base_payload(cls, build_entity: NativeApplicationBuild,
                                          native_application: NativeApplication,
                                          mea_list_url: str, payload: dict):
        """
        Map the code magic payload based on which platform it is building
        """
        payload['workflowId'] = cls._get_code_magic_workflow(build_entity)
        envs = {
            'URL_ICON': cls._get_entity_url(native_application.icon_image),
            'URL_SPLASH': cls._get_entity_url(native_application.splash_image),
            # Do not send this field at all and let code magic use default
            # until we put on actual implementation
            # 'URL_DEEP_LINK': None,
            'APP_ID': native_application.app_id_in_stores,
            'APP_NAME': native_application.app_name_in_stores,
            'LIST_URL': mea_list_url,
        }

        if build_entity.build_platform == MobilePlatform.android:
            android_creds = native_application.android_build_credentials
            envs['ANDROID_VERSION'] = build_entity.build_version
            envs[
                'URL_FCI_KEYSTORE'] = cls._get_entity_url(android_creds.android_keystore_file)
            envs['FCI_KEYSTORE_PASSWORD'] = android_creds.android_keystore_password
            envs['FCI_KEY_PASSWORD'] = android_creds.android_key_password
            envs['FCI_KEY_ALIAS'] = android_creds.android_key_alias

        elif build_entity.build_platform == MobilePlatform.ios:
            ios_creds = native_application.ios_build_credentials
            envs['IOS_VERSION'] = build_entity.build_version
            envs['APP_STORE_CONNECT_ISSUER_ID'] = ios_creds.ios_app_store_connect_issuer_id
            envs[
                'APP_STORE_CONNECT_KEY_IDENTIFIER'] = ios_creds.ios_app_store_connect_key_identifier
            envs['APP_STORE_CONNECT_PRIVATE_KEY'] = cls._get_content_as_string(
                ios_creds.ios_app_store_connect_private_key_file)
            envs['CERTIFICATE_PRIVATE_KEY'] = cls._get_content_as_string(
                ios_creds.ios_certificate_private_key_file)

            # Optional fields:
            if ios_creds.ios_certificate_file:
                envs['URL_FCI_CERTIFICATE'] = cls._get_entity_url(ios_creds.ios_certificate_file)
            if ios_creds.ios_certificate_password:
                envs['FCI_CERTIFICATE_PASSWORD'] = ios_creds.ios_certificate_password
            if ios_creds.ios_provisioning_profile_file:
                envs['FCI_PROVISIONING_PROFILE'] = cls._get_content_as_base64(
                    ios_creds.ios_provisioning_profile_file)

        payload['environment']['variables'] = envs

    @classmethod
    def _generate_code_magic_publish_payload(cls, build_entity: NativeApplicationBuild,
                                             native_application: NativeApplication,
                                             payload: dict):

        if build_entity.build_platform == MobilePlatform.android:
            if build_entity.publish_to_store:
                payload['environment']['variables'][
                    'GOOGLE_CREDENTIALS'] = cls._get_content_as_string(
                    native_application.android_build_credentials.android_google_credentials_file)
            else:
                payload['environment']['variables']['GOOGLE_CREDENTIALS'] = ''

        elif build_entity.build_platform == MobilePlatform.ios:
            if build_entity.publish_to_store:
                payload['environment']['variables'][
                    'APPLE_ID'] = native_application.ios_build_credentials.ios_apple_id
                payload['environment']['variables'][
                    'APPLE_PASSWORD'] = native_application.ios_build_credentials.ios_apple_password
            else:
                payload['environment']['variables']['APPLE_ID'] = ''
                payload['environment']['variables']['APPLE_PASSWORD'] = ''

    @classmethod
    def _get_entity_url(cls, entity):
        """
        Returns public accessible URL based on entity type
        """
        url = None
        if isinstance(entity, AssociatedImage):
            url = entity.full_size_url
        elif isinstance(entity, File):
            url = entity.generate_presign_download_url()
        return url

    @classmethod
    def _get_content_as_string(cls, file_entity) -> str:
        """
        Download the S3 object as bytes and decode back to utf-8 string
        :param file_entity: file entity to be downloaded
        :return: file content in utf-8 string format
        """
        file_content = file_entity.deps.s3_lib.download_file_as_bytes(  # type: ignore
            file_entity.s3_key,
        )
        return file_content.decode()

    @classmethod
    def _get_content_as_base64(cls, file_entity: File) -> str:
        """
        Download the S3 object as bytes and encode them into base64 string format
        :param file_entity: file entity to be downloaded
        :return: base64 string in utf-8 encode
        """
        file_content = file_entity.deps.s3_lib.download_file_as_bytes(  # type: ignore
            file_entity.s3_key,
        )
        return b64encode(file_content).decode()

    @staticmethod
    def _get_code_magic_workflow(build_entity: NativeApplicationBuild):
        """
        Set code magic workflow ID based on build entity
        :return: code magic workflow ID
        """
        workflow_id = build_entity.build_platform.value
        if build_entity.publish_to_store:
            workflow_id += '-with-publish'
        return workflow_id

    @staticmethod
    def _extract_error_details(build_json):
        error_details = {
            'failed_steps': []
        }
        failed_actions = [act for act in build_json['buildActions'] if
                          act.get('status') == BuildStatus.failed.value]

        for act in failed_actions:
            error_details['failed_steps'].append({
                'name': act.get('name'),
                'status': act.get('status'),
                'command': act['subactions'][0].get('command') if act['subactions'] else '',
                'log_url': (act['subactions'][0].get('logUrl')
                            if act['subactions'] else act.get('logUrl')
                            ),
            })

        return dumps(error_details)


code_magic_client = None


# Get singleton
def get_code_magic_client():
    global code_magic_client
    if not code_magic_client:
        code_magic_client = CodeMagicClient()
    return code_magic_client


__ALL__ = ['get_code_magic_client']
