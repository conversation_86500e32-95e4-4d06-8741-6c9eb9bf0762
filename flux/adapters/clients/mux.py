import json
from typing import List, Optional
from dataclasses import dataclass
from math import ceil
from datetime import datetime

import mux_python
from mux_python.rest import ApiException, NotFoundException

from flux.utils import mux

from flux.apps.config import app_config
from flux.domains.common.errors import (
    NotFoundError,
    InternalError,
)


class MuxLiveStreamNotFound(NotFoundError):
    pass


class MuxApiException(InternalError):
    pass


class MuxAssetNotFound(NotFoundError):
    pass


@dataclass
class MuxLiveStream:
    id: str
    basic_playback_id: str
    stream_key: str
    # TODO: remove when we no longer have public policy in the system
    public_policy: bool
    do_record: bool = True


@dataclass
class MuxAssetDelivery:
    asset_duration: int
    asset_id: str
    asset_state: str
    created_at: datetime
    delivered_seconds: int
    live_stream_id: Optional[str]


@dataclass
class MuxUpload:
    id: str
    url: str


class MuxClient:

    def __init__(self):
        configuration = mux_python.Configuration()
        configuration.username = app_config.MUX_TOKEN_ID
        configuration.password = app_config.MUX_TOKEN_SECRET
        self.video_policy = mux_python.PlaybackPolicy.SIGNED

        # API Client Initialization
        self.api_client = mux_python.LiveStreamsApi(
            mux_python.ApiClient(configuration)
        )
        self.delivery_client = mux_python.DeliveryUsageApi(
            mux_python.ApiClient(configuration)
        )
        self.uploads_api = mux_python.DirectUploadsApi(
            mux_python.ApiClient(configuration)
        )
        self.asset_api = mux_python.AssetsApi(
            mux_python.ApiClient(configuration)
        )

    @staticmethod
    def _process_mux_exception(e: ApiException, raise_exc):
        """
        Client throws error when resource is available in prod but not in stage or the other way
        this should be interpreted as not found error
        """
        if e.status == 400 and e.body:
            body = json.loads(e.body) if isinstance(e.body, str) else e.body
            error = body.get('error')
            if error and 'type' in error and 'messages' in error:
                error_type = error['type']
                message = error['messages'][-1] if error['messages'] else ''
                if error_type == "invalid_parameters" and "mismatching environment" in message:
                    raise raise_exc from e

    def create_live_stream(self, record_live_stream=True) -> MuxLiveStream:
        try:
            new_asset_settings = mux_python.CreateAssetRequest(
                playback_policy=[self.video_policy],
                mp4_support='standard' if record_live_stream else None,
            )

            create_live_stream_request = mux_python.CreateLiveStreamRequest(
                low_latency=True,
                playback_policy=[self.video_policy],
                new_asset_settings=new_asset_settings,
                reconnect_window=120,
            )

            response = self.api_client.create_live_stream(
                create_live_stream_request
            )

            live_stream = MuxLiveStream(
                id=response.data.id,
                basic_playback_id=response.data.playback_ids[0].id,
                stream_key=response.data.stream_key,
                # TODO: remove when we no longer have public policy in the system
                public_policy=self.video_policy == mux_python.PlaybackPolicy.PUBLIC,
                do_record=record_live_stream
            )
            return live_stream
        except ApiException as e:
            raise MuxApiException from e

    def get_live_stream(self, live_stream_id: str) -> MuxLiveStream:
        try:
            response = self.api_client.get_live_stream(live_stream_id)

            live_stream = MuxLiveStream(
                id=response.data.id,
                basic_playback_id=response.data.playback_ids[0].id,
                stream_key=response.data.stream_key,
                # TODO: remove when we no longer have public policy in the system
                public_policy=self.video_policy == mux_python.PlaybackPolicy.PUBLIC,
            )
            return live_stream
        except NotFoundException as e:
            raise MuxLiveStreamNotFound from e
        except ApiException as e:
            self._process_mux_exception(e, MuxLiveStreamNotFound)
            raise MuxApiException from e

    def delete_live_stream(self, live_stream_id: str) -> None:
        try:
            self.api_client.delete_live_stream(live_stream_id)
        except NotFoundException as e:
            raise MuxLiveStreamNotFound from e
        except ApiException as e:
            self._process_mux_exception(e, MuxLiveStreamNotFound)
            raise MuxApiException from e

    def complete_live_stream(self, live_stream_id: str) -> None:
        try:
            self.api_client.signal_live_stream_complete(live_stream_id)
        except NotFoundException as e:
            raise MuxLiveStreamNotFound from e
        except ApiException as e:
            self._process_mux_exception(e, MuxLiveStreamNotFound)
            raise MuxApiException from e

    def get_delivery_usage(self,
                           start_datetime: datetime,
                           end_datetime: datetime,
                           ) -> List[MuxAssetDelivery]:

        timeframe = [
            str(int(start_datetime.timestamp())),
            str(int(end_datetime.timestamp())),
        ]

        records = self._get_delivery_records(timeframe)

        asset_delivery = [
            MuxAssetDelivery(
                asset_duration=int(r.asset_duration) if r.asset_duration else 0,
                asset_id=r.asset_id,
                asset_state=r.asset_state,
                created_at=datetime.utcfromtimestamp(int(r.created_at)),
                delivered_seconds=int(r.delivered_seconds) if r.delivered_seconds else 0,
                live_stream_id=r.live_stream_id,
            ) for r in records
        ]

        return asset_delivery

    def create_direct_upload(self, expires_in=3600) -> MuxUpload:
        try:
            create_asset_request = mux_python.CreateAssetRequest(
                playback_policy=[self.video_policy],
                mp4_support='standard',
            )
            create_upload_request = mux_python.CreateUploadRequest(
                timeout=expires_in,
                new_asset_settings=create_asset_request,
                cors_origin="*",
            )
            create_upload_response = self.uploads_api.create_direct_upload(create_upload_request)

            upload = MuxUpload(
                id=create_upload_response.data.id,
                url=create_upload_response.data.url,
            )
            return upload
        except ApiException as e:
            raise MuxApiException from e

    def delete_asset(self, asset_id):
        try:
            self.asset_api.delete_asset(asset_id=asset_id)
        except NotFoundException as e:
            raise MuxAssetNotFound from e
        except ApiException as e:
            self._process_mux_exception(e, MuxAssetNotFound)
            raise MuxApiException from e

    def list_assets(self, page):
        try:
            return self.asset_api.list_assets(page=page)
        except ApiException as e:
            raise MuxApiException from e

    @staticmethod
    def get_playback_image(playback_id, animated=False):
        url = (
            mux.get_animated_gif_url(playback_id)
            if animated else mux.get_thumbnail_url(playback_id)
        )
        return url

    def _get_delivery_records(self, timeframe):
        page = 1
        limit = 100
        try:
            response = self.delivery_client.list_delivery_usage(
                page=page,
                limit=limit,
                timeframe=timeframe,
            )
            if response.total_row_count:
                num_pages = ceil(response.total_row_count / limit)
            else:
                num_pages = 0
            records = response.data
            for page in range(2, num_pages + 1):
                response = self.delivery_client.list_delivery_usage(
                    page=page,
                    limit=limit,
                    timeframe=timeframe,
                )
                records.extend(response.data)
        except ApiException as e:
            raise MuxApiException from e
        return records
