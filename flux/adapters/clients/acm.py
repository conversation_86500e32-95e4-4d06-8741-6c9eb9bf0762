from uuid import UUID
from dataclasses import dataclass
from typing import Optional

import boto3
from botocore.exceptions import ClientError

from flux.adapters.logging import get_logger
from flux.apps.config import app_config
from flux.domains.common.errors import InternalError, aws_exception, NotFoundError

logger = get_logger(__name__)


@dataclass
class Certificate:
    arn: str
    domain_name: str
    domain_validation_status: str
    cname_key: str
    cname_value: str
    status: str
    failure_reason: Optional[str] = None


@dataclass
class CertificateTags:
    tags: str
    environment: str
    terraform: bool


class ACMAPIError(InternalError):
    pass


class ACMNoSuchCertificateError(NotFoundError):
    message = 'The provided certificate does not exist.'


class ACM:

    def __init__(self):
        # Certificates must only be created in this region for CloudFront
        if app_config.is_dev_environment_and_dev_aws():
            self._client = boto3.client(
                'acm',
                region_name='us-east-1',
                endpoint_url=app_config.AWS_DEV_ENDPOINT,
            )
        else:
            self._client = boto3.client(
                'acm',
                region_name='us-east-1',
            )

    @aws_exception(ACMAPIError)
    def request_certificate(self, domain_name, event_id):
        return self._client.request_certificate(
            DomainName=domain_name,
            ValidationMethod='DNS',
            Tags=[
                {
                    'Key': 'environment',
                    'Value': app_config.ENV
                },
                {
                    'Key': 'event',
                    'Value': f'{event_id}'
                },
                {
                    'Key': 'terraform',
                    'Value': 'false'
                }
            ]
        )['CertificateArn']

    @aws_exception(ACMAPIError)
    def describe_certificate(self, certificate_arn):
        cert = self._client.describe_certificate(
            CertificateArn=certificate_arn
        )['Certificate']
        domain_validation = cert['DomainValidationOptions'][0]
        return Certificate(
            arn=cert['CertificateArn'],
            domain_name=cert['DomainName'],
            domain_validation_status=domain_validation['ValidationStatus'],
            cname_key=(domain_validation['ResourceRecord']['Name']
                       if 'ResourceRecord' in domain_validation else None),
            cname_value=(domain_validation['ResourceRecord']['Value']
                         if 'ResourceRecord' in domain_validation else None),
            status=cert['Status'],
            failure_reason=cert['FailureReason'] if 'FailureReason' in cert else None,
        )

    @aws_exception(ACMAPIError)
    def delete_certificate(self, certificate_arn):
        self._client.delete_certificate(
            CertificateArn=certificate_arn,
        )

    @aws_exception(ACMAPIError)
    def list_certificate_tags(self, certificate_arn):
        tags = self._client.list_tags_for_certificate(
            CertificateArn=certificate_arn,
        )['Tags']
        env = [tag['Value'] for tag in tags if tag['Key'].lower() == 'environment'][0]
        terraform = [tag['Value'] for tag in tags if tag['Key'].lower() == 'terraform'][0]
        return CertificateTags(
            tags=tags,
            environment=env,
            terraform=bool(terraform == 'true')
        )

    @aws_exception(ACMAPIError)
    def list_certificates(self, max_items=500, next_token=None):
        if next_token:
            return self._client.list_certificates(
                NextToken=next_token,
                MaxItems=max_items,
            )
        return self._client.list_certificates(
            MaxItems=max_items,
        )

    @aws_exception(ACMAPIError)
    def remove_non_prod_certificates(self, next_token=None):
        certs = self.list_certificates(next_token=next_token)
        for cert in certs['CertificateSummaryList']:
            try:
                tags = self.list_certificate_tags(cert['CertificateArn'])
                if not tags.terraform and tags.environment in ['dev', 'stage', 'deleted']:
                    self.delete_certificate(cert['CertificateArn'])
                    logger.info(f'Deleted ACM certificate: {cert["CertificateArn"]} - '
                                f'{cert["DomainName"]}')
            except ACMAPIError:
                logger.warning(f'Unable to check or delete certificate: {cert["CertificateArn"]}'
                               f' - {cert["DomainName"]}')
        if 'NextToken' in certs:
            self.remove_non_prod_certificates(next_token=certs['NextToken'])

    @aws_exception(ACMAPIError)
    def add_tags_to_certificate(self, certificate_arn, certificate_tags):
        self._client.add_tags_to_certificate(
            CertificateArn=certificate_arn,
            Tags=[
                {
                    'Key': key,
                    'Value': value
                } for key, value in certificate_tags.items()
            ]
        )

    @aws_exception(ACMAPIError)
    def request_ssl_certificate(self, domain_name, organization_id: UUID):
        return self._client.request_certificate(
            DomainName=domain_name,
            ValidationMethod='DNS',
            Tags=[
                {
                    'Key': 'environment',
                    'Value': app_config.ENV
                },
                {
                    'Key': 'organization_id',
                    'Value': str(organization_id)
                },
                {
                    'Key': 'terraform',
                    'Value': 'false'
                }
            ]
        )['CertificateArn']

    @aws_exception(ACMAPIError)
    def delete_ssl_certificate(self, certificate_arn):
        try:
            self._client.delete_certificate(
                CertificateArn=certificate_arn,
            )
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ResourceNotFoundException':
                raise ACMNoSuchCertificateError
            raise ClientError(error_response=e.response, operation_name=e.operation_name)
