import requests

from flux.apps.config import app_config
from flux.adapters.logging import get_logger

from flux.domains.common.errors import InternalError

logger = get_logger(__name__)


# BeeFree API endpoint
AUTH_ENDPOINT = 'https://auth.getbee.io/apiauth'


class BeeFreeAPIError(InternalError):
    pass


class BeeFreeClient:

    def __init__(self):

        # BeeFree API credentials
        self.CLIENT_CREDENTIALS = {
            'email_builder': {
                'client_id': app_config.EMAIL_BUILDER_CLIENT_ID,
                'client_secret': app_config.EMAIL_BUILDER_CLIENT_SECRET,
            },
            'page_builder': {
                'client_id': app_config.PAGE_BUILDER_CLIENT_ID,
                'client_secret': app_config.PAGE_BUILDER_CLIENT_SECRET,
            },
        }
        self.session = requests.Session()
        self.session.headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
        }

    # Make a POST request to obtain the access token for email builder or page builder
    def get_access_token(self, client_type):

        client_credentials = self.CLIENT_CREDENTIALS.get(client_type)

        if not client_credentials:
            raise BeeFreeAPIError(code=404, message='Client type not found')

        data = {
            'client_id': client_credentials['client_id'],
            'client_secret': client_credentials['client_secret'],
        }

        response = self.session.post(AUTH_ENDPOINT, data=data)

        if response.status_code == 200:
            access_token = response.json().get('access_token')
            logger.info(
                'POST request to obtain the BeeFree access token completed',
                response=response,
            )
            return access_token
        raise BeeFreeAPIError(code=response.status_code, message=response.text)
