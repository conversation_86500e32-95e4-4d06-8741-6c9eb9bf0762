SCHEMA >
    `timestamp` DateTime64(3),
    `product` LowCardinality(String),
    `organization_id` LowCardinality(String),
    `unique_device_id` String,
    `context_type` LowCardinality(String),
    `context_id` LowCardinality(String),
    `entity_type` LowCardinality(String),
    `entity_id` LowCardinality(String),
    `user_id` String,
    `clicks` SimpleAggregateFunction(sum, UInt64),
    `referrer_type` LowCardinality(String),
    `referrer_id` LowCardinality(String),
    `city` LowCardinality(String),
    `country` LowCardinality(String),
    `device_type` LowCardinality(String),
    `os_family` LowCardinality(String)

ENGINE "AggregatingMergeTree"
ENGINE_PARTITION_KEY "toYYYYMM(timestamp)"
ENGINE_SORTING_KEY "product, context_type, organization_id, entity_type, context_id, entity_id, referrer_type, referrer_id, user_id, unique_device_id, timestamp, city, country, device_type, os_family"
ENGINE_PRIMARY_KEY "product, context_type, organization_id, entity_type, context_id, entity_id, referrer_type, referrer_id, user_id, unique_device_id, timestamp"
