VERSION 1
TOKEN eventspace_write_token APPEND

DESCRIPTION >
    Analytics events landing data source

SCHEMA >
    `timestamp` DateTime `json:$.timestamp`,
    `session_id` String `json:$.session_id`,
    `unique_device_id` String `json:$.unique_device_id`,
    `action` LowCardinality(String) `json:$.action`,
    `product` LowCardinality(String) `json:$.product`,
    `organization_id` LowCardinality(String) `json:$.organization_id`,
    `context_type` LowCardinality(String) `json:$.context_type`,
    `context_id` LowCardinality(String) `json:$.context_id`,
    `user_id` String `json:$.user_id`,
    `payload` String `json:$.payload`

ENGINE "MergeTree"
ENGINE_PARTITION_KEY "toYYYYMM(timestamp)"
ENGINE_SORTING_KEY "action, product, context_type, organization_id, context_id, user_id, unique_device_id, timestamp"
