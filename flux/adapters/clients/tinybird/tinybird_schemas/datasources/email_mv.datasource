SCHEMA >
    `timestamp` DateTime,
    `product` LowCardinality(String),
    `organization_id` LowCardinality(String),
    `context_type` LowCardinality(String),
    `context_id` LowCardinality(String),
    `entity_type` LowCardinality(String),
    `entity_id` LowCardinality(String),
    `email_id` LowCardinality(String),
    `email_type` LowCardinality(String),
    `user_id` String,
    `action` LowCardinality(String),
    `clicked_url` String

ENGINE "MergeTree"
ENGINE_PARTITION_KEY "toYYYYMM(timestamp)"
ENGINE_SORTING_KEY "product,context_type,organization_id,context_id,email_id,user_id,action,timestamp"
ENGINE_PRIMARY_KEY "product,context_type,organization_id,context_id,email_id,user_id,action,timestamp"

