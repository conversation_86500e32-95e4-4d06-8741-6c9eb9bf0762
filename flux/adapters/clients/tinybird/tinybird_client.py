import uuid
import json
import requests

from collections import defaultdict
from itertools import groupby
from urllib.parse import urlencode

from datetime import datetime
from typing import List, Optional, Any, Union
from dataclasses import dataclass

from flux.apps.config import app_config

from flux.adapters.logging import get_logger
from flux.domains.common.entity import ProductTypes, ContextBoundaryTypes
from flux.domains.event.analytics.entities import (
    GroupingPeriods,
    Collections,
)
from flux.domains.event.analytics.exceptions import AnalyticsAPIError, AnalyticsAPIRateLimitError
from flux.utils import timeutils
from flux.utils.not_set import NotSet


logger = get_logger(__name__)

BASE_URL = 'https://api.us-east.aws.tinybird.co/v0'
DELETE_URL = BASE_URL + '/datasources/{datasource}/delete'
SQL_URL = BASE_URL + '/sql'
CREATE_URL = BASE_URL + '/events?name={data_source}'


SQL_TEMPLATE = """
SELECT {d[select]}
FROM _
{d[where]}
{d[group_by]}
{d[order_by]}
{d[limit]} {d[offset]}
FORMAT JSON
"""

SUBQUERY_TEMPLATE = """
SELECT {d[select]}
FROM _
{d[where]}
{d[group_by]}
{d[order_by]}
{d[limit]}
"""

ANALYTICS_INPUT_DATASOURCE = 'analytics_events__v1'

COLLECTION_TO_DATASOURCE_NAME = {
    Collections.pageviews: 'pageview_mv',
    Collections.impressions: 'impression_mv',
    Collections.clicks: 'click_mv',
    Collections.video: 'video_mv',
    # Not implemented yet. Activate it when we update production tinybird
    # Collections.email: 'email_mv',
}

COLLECTION_TO_SQL_PIPE = {
    Collections.pageviews: 'pageviews_sql_query',
    Collections.impressions: 'impressions_sql_query',
    Collections.clicks: 'clicks_sql_query',
    Collections.video: 'video_sql_query',
    Collections.email: 'email_sql_query',
}

# Select statements for timeline view (grouping by interval period)
TIMELINE_ANALYSIS = {
    'local_datetime': (
        "toDateTime("
        "toStartOfInterval(timestamp, INTERVAL 1 {interval_period}, '{timezone}'),"
        "'{timezone}')"
    ),
    'start_datetime': "toTimezone(local_datetime, 'UTC')",
    'end_datetime': "date_add(start_datetime, INTERVAL 1 {interval_period})",
}

COLLECTION_ANALYSIS = {
    Collections.pageviews: {
        'unique_users': 'count(DISTINCT unique_device_id)',
        'logged_in_users': 'count(DISTINCT user_id)',
        'total_views': 'sum(views)',
    },
    Collections.impressions: {
        'impressions': 'sum(impressions)',
    },
    Collections.clicks: {
        'clicks': 'sum(clicks)'
    },
    Collections.video: {
        'unique_users': 'count(DISTINCT unique_device_id)',
        'logged_in_users': 'count(DISTINCT user_id)',
        'total_views': 'count(DISTINCT view_id)',
        'watched_seconds': 'toInt64(sum(watched_seconds))',
    },
    Collections.email: {
        'logged_in_users': 'count(DISTINCT user_id)',
        'unique_opens': "uniqIf(user_id, action = 'email_engagement_open')",
        'unique_clicks': "uniqIf(user_id, action = 'email_engagement_click')",
        'unique_bounces': "uniqIf(user_id, action = 'email_delivery_bounce')",
        'unique_processed': "uniqIf(user_id, action = 'email_delivery_processed')",
        'unique_deliveries': "uniqIf(user_id, action = 'email_delivery_delivered')",
        'unique_unsubscribes': "uniqIf(user_id, action = 'email_engagement_unsubscribe')",
        'unique_spam_reports': "uniqIf(user_id, action = 'email_engagement_spam_report')",
    },
}

GROUP_BY_INPUT_MAP = {
    'entity_id': ['entity_type', 'entity_id'],
    'entity_type': ['entity_type'],
    'device': ['device_type', 'os_family'],
    'referrer_id': ['referrer_type', 'referrer_id'],
    'video_id': ['video_id'],
    'user_id': ['user_id'],
    'email_id': ['email_id'],
    'clicked_url': ['clicked_url'],
}

GROUPING_PERIOD_TO_INTERVAL = {
    # GroupingPeriods.second: 'SECOND'    # Not currently supported
    # GroupingPeriods.minute: 'MINUTE',   # Not currently supported
    GroupingPeriods.hour: 'HOUR',
    GroupingPeriods.day: 'DAY',
    # GroupingPeriods.week: 'WEEK',       # Not currently supported
    GroupingPeriods.month: 'MONTH',
    # GroupingPeriods.quarter: 'QUARTER', # Not currently supported
    GroupingPeriods.year: 'YEAR',
}


@dataclass
class Condition:
    """ Supports constructing a SQL like condition.

    NOTE: Currently only supports string value types.

    Example Usage:
    str(Condition(name='user_id', value='14717', operator='='))
    > user_id='14717'
    str(Condition(name='user_id', value=['39850', '40809'], operator='in'))
    > user_id in ('39850', '40809')
    """
    name: str
    value: Optional[Union[str, List[str]]]
    operator: str = '='
    add_quotes: bool = True

    def __str__(self):

        # Format value, set NULL if value is None, wrap in single quotes otherwise
        if self.value is None:
            value = 'NULL'
        elif self.add_quotes:
            if isinstance(self.value, list):
                value = [f"'{v}'" for v in self.value]
            else:
                value = f"'{self.value}'"
        else:
            value = self.value

        if self.operator == 'in':
            result = f"{self.name} in ({', '.join(value)})"
        elif self.operator in {'=', '!=', '>', '>=', '<', '<=', 'is', 'is not'}:
            result = f"{self.name} {self.operator} {value}"
        else:
            raise Exception(f'Unsupported operator {self.operator}.')
        return result


def and_condition_formatter(conditions: List[Condition]):
    """ Joins a list of conditions with AND statements

    Example:
        c1 = Condition(name='user_id', value=[39850, '40809'], operator='in')
        c2 = Condition(name='user_id', value='14717', operator='=')
        print(_and_condition_formatter([c1,c2]))
        > user_id in ('39850', '40809') AND user_id='14717'
    """
    if not conditions:
        raise Exception('Minimum of one condition expected')

    return ' AND '.join([str(c) for c in conditions])


@dataclass
class CommonParameters:
    organization_id: uuid.UUID
    start_datetime: datetime
    end_datetime: datetime
    timezone: str
    product: Optional[ProductTypes] = None
    context_type: Optional[ContextBoundaryTypes] = None
    context_id: Optional[str] = None
    entity_id: Optional[List[str]] = None
    entity_type: Optional[List[str]] = None
    video_id: Optional[List[str]] = None
    url: Optional[str] = None
    limit: Optional[int] = None
    group_by: Optional[List[str]] = None
    sort: Optional[str] = None
    grouping_period: Optional[GroupingPeriods] = None
    user_id: Optional[Any] = NotSet
    offset: Optional[int] = None
    email_id: Optional[List[str]] = None
    email_type: Optional[List[str]] = None


class TinybirdClient:

    def __init__(self, token=None):
        self.session = requests.Session()
        token = token if token else app_config.TINYBIRD_ANALYTICS_TOKEN
        self.session.headers = {
            "Authorization": f'Bearer {token}',
        }
        self.session.hooks["response"].append(self.log_error_response_api_client)

    @staticmethod
    def log_error_response_api_client(response, *args, **kargs):
        """
        Checks if the response status code is acceptable.
        200: Returned status code by GET calls
        201: Returned status code by POST calls (DELETE is actually a POST call in Tinybird)
        """
        http_status = response.status_code

        if http_status not in [200, 201, 202]:
            content = response.content
            if http_status == 429:
                raise AnalyticsAPIRateLimitError(
                    code='tinybird_api_rate_limit_error',
                    message=content,
                )
            raise AnalyticsAPIError(
                code='tinybird_api_error',
                message=content,
            )

    def _create(self, data_source, data: List):

        logger.info('Sending a post request to Tinybird', data=data)
        url = CREATE_URL.format(data_source=data_source)

        # Send a POST request to Tiny bird as NDJSON format
        post_data = '\n'.join([json.dumps(item) for item in data])
        response = self.session.post(url, data=post_data)

        logger.info('Completed post request to Tinybird', response=response)

    def append_new_analytics_data_to_datasource(self, data):

        """appends new data to datasource: `analytics_events__v1`"""

        self._create(
            data_source=ANALYTICS_INPUT_DATASOURCE,
            data=data,
        )

    def delete_from_analytics_input_datasource(
        self, organization_id=None, event_id=None, user_id=None
    ):
        """Deletes analytics data from input datasource: `analytics_events__v1`"""
        self._delete(
            datasource=ANALYTICS_INPUT_DATASOURCE,
            organization_id=organization_id,
            event_id=event_id,
            user_id=user_id,
        )

    def delete_from_collection(
        self, collection: Collections, organization_id=None, event_id=None, user_id=None,
    ):
        """Deletes analytics data from materialized view datasources"""
        if collection in COLLECTION_TO_DATASOURCE_NAME:
            self._delete(
                datasource=COLLECTION_TO_DATASOURCE_NAME[collection],
                organization_id=organization_id,
                event_id=event_id,
                user_id=user_id,
            )

    def _delete(self, datasource, organization_id=None, event_id=None, user_id=None):
        conditions = []
        if organization_id:
            conditions.append(
                Condition(name='organization_id', value=str(organization_id)),
            )
        if event_id:
            conditions.append(
                Condition(name='context_type', value=ContextBoundaryTypes.events.value)
            )
            conditions.append(Condition(name='context_id', value=str(event_id)))

        if user_id:
            conditions.append(Condition(name='user_id', value=str(user_id)))

        payload = {'delete_condition': str(and_condition_formatter(conditions))}
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        url = DELETE_URL.format(datasource=datasource)
        self.session.post(url, headers=headers, data=payload)

    def analysis_operation(self, collection: Collections, params: CommonParameters):
        """ Performs an Analysis Operation on a Tinybird Collection based on provided Params.

        Note: The response structure is modified if a grouping_period is specified to include
        data_points grouped by time periods.
        """
        query = _build_query(collection, params)
        query_result = self._execute_sql_query(collection, query)
        if params.grouping_period:
            result = _build_timeline_result_values(collection, params, query_result)
        else:
            result = _build_result_values(collection, params, query_result)
        return result

    def _execute_sql_query(self, collection: Collections, sql_query):
        logger.info('Sending request to Tinybird', query=sql_query)
        pipe = COLLECTION_TO_SQL_PIPE[collection]
        query = urlencode({'q': sql_query, 'pipeline': pipe})
        url = SQL_URL + '?' + query
        response = self.session.request('GET', url)
        json_response = response.json()
        response_stats = {
            'num_rows': json_response.get('rows'),
            'rows_before_limit_at_least': json_response.get('rows_before_limit_at_least'),
            'statistics': json_response.get('statistics'),
        }
        logger.info('Completed request to Tinybird', **response_stats)
        return json_response['data']


def _build_query(collection: Collections, params: CommonParameters):

    # Map and expand incoming group_by fields to expected group_by fields
    group_by = []
    if params.group_by:
        for group in params.group_by:
            group_by.extend(GROUP_BY_INPUT_MAP[group])

    q = {
        'select': _build_select(collection=collection, params=params, group_by=group_by),
        'where': _build_where(collection=collection, params=params, group_by=group_by),
    }

    # If we have a grouping period, ensure we group by the "local_datetime" select analysis
    # set up in "_build_select"
    if params.grouping_period:
        group_by.insert(0, 'local_datetime')

    if group_by:
        q['group_by'] = 'GROUP BY ' + ','.join(group_by)

    # Add Limit Statement if present, however if we both a grouping period and
    # an additional group by the limit will be applied in a subquery in the where
    # statement, so we'll skip adding it here so we don't limit the number of time
    # range data points returned
    if params.limit and not (params.grouping_period and params.group_by):
        q['limit'] = f'LIMIT {params.limit}'

    if params.sort or params.grouping_period:
        q['order_by'] = _build_order_by(params)

    # Note: Not currently supported, but we will want this to support pagination for "people"
    # operation results which can be hundreds to thousands of rows.
    if params.offset is not None:
        q['offset'] = f'OFFSET {params.offset}'

    full_query = _format_sql_query(SQL_TEMPLATE, **q)

    return full_query


def _build_select(collection: Collections, params: CommonParameters, group_by: List) -> str:
    select_statements = []

    # Get default select analysis statements for the collection type
    for k, v in COLLECTION_ANALYSIS[collection].items():
        select_statements.append(f'{v} as {k}')

    # If we are grouping by time, we need to add some specific time related analysis to the
    # select statement, this will include the event timezone and the interval period we are
    # grouping by.
    if params.grouping_period:
        interval_period = GROUPING_PERIOD_TO_INTERVAL[params.grouping_period]
        for k, v in TIMELINE_ANALYSIS.items():
            fv = v.format(timezone=params.timezone, interval_period=interval_period)
            select_statements.append(f'{fv} as {k}')

    # We also need to return the fields we're grouping by
    select_statements.extend(group_by)

    select_query = ', '.join(select_statements)
    return select_query


def _build_where(collection: Collections, params: CommonParameters, group_by: List[str]) -> str:
    _conditions = _build_where_conditions(collection, params, group_by)

    if params.grouping_period and group_by:
        _conditions.append(_build_inner_where_subquery(collection, params, group_by))

    where_clause_str = 'WHERE ' + and_condition_formatter(_conditions)
    return where_clause_str


def _build_where_conditions(collection: Collections, params: CommonParameters, group_by: List[str]
                            ) -> List[Condition]:

    # Ensure datetimes are timezone naive
    naive_start_datetime = timeutils.convert_datetime_to_offset_naive(params.start_datetime)
    naive_end_datetime = timeutils.convert_datetime_to_offset_naive(params.end_datetime)

    sdt_iso = timeutils.iso_format_datetime(naive_start_datetime)
    edt_iso = timeutils.iso_format_datetime(naive_end_datetime)

    # Always applied conditions
    _conditions = [
        Condition(name='organization_id', value=str(params.organization_id)),
        Condition(name='timestamp', operator='>=', value=sdt_iso),
        Condition(name='timestamp', operator='<=', value=edt_iso),
    ]

    if params.context_type:
        _conditions.append(
            Condition(name='context_type', operator='=', value=params.context_type.value)
        )

    if params.context_id:
        _conditions.append(
            Condition(name='context_id', operator='=', value=params.context_id)
        )

    if params.product:
        _conditions.append(Condition(name='product', operator='=', value=params.product.value))

    if params.user_id is not NotSet:
        if params.user_id:
            _conditions.append(Condition(name='user_id', operator='in', value=params.user_id))
        else:
            _conditions.append(Condition(name='user_id', operator='=', value=None))

    if params.entity_type:
        _conditions.append(Condition(name='entity_type', operator='in', value=params.entity_type))

    if params.entity_id:
        _conditions.append(Condition(name='entity_id', operator='in', value=params.entity_id))

    if params.url:
        _conditions.append(Condition(name='url', operator='=', value=params.url))

    if params.video_id:
        _conditions.append(Condition(name='video_id', operator='in', value=params.video_id))

    if params.email_id:
        _conditions.append(Condition(name='email_id', operator='in', value=params.email_id))

    if params.email_type:
        _conditions.append(
            Condition(name='email_type', operator='in', value=params.email_type))

    # Ensure we filter out any NULL and empty values for fields we're grouping on
    for field in group_by:
        _conditions.append(Condition(name=field, operator='is not', value=None))
        _conditions.append(Condition(name=field, operator='!=', value=''))

    return _conditions


def _build_inner_where_subquery(collection: Collections, params: CommonParameters,
                                group_by: List[str]) -> Condition:
    """ Builds a subquery to use in a where statement.

    If we are grouping by both time and other fields, we run into a complex scenario.
    This can be thought of most easily as having a chart with multiple time series, where
    each of the series is the values for an entity such as a person or video.
    In this situation, an applied LIMIT and ORDER should apply to the timeseries. For
    instance if the user wants the top 3 videos by total views hour over hour, we
    should first determine which 3 videos have the most total views within the entire
    time period, limit our results to only those 3, and then break the data down into
    hour by hour data points. This approach requires a sub-query to determine the top
    videos, which the outer query will use in a where.

    Example of a query built:
        SELECT
            user_id
        FROM _
        WHERE
            organization_id = '044c1468-5dc4-4c12-b50b-3ed4e2990c43'
            AND timestamp >= '2022-09-01T06:00:00'
            AND timestamp <= '2022-11-30T06:00:00'
            AND video_id in ('82ed06cf-b059-4d26-ad7a-83523132f0c0')
            AND user_id is not NULL
        GROUP BY user_id
        ORDER BY toInt64(sum(watched_seconds)) DESC
        LIMIT 2
    """
    if len(group_by) != 1:
        raise Exception('Timeline view only allows grouping by one additional field')

    # Select statement should be the single additional field that we are grouping on, this will
    # be used like `where x in (select x from ....)` in the outer query
    q = {'select': group_by[0]}

    # Inner query uses mostly the same filters as the outer query
    conditions = _build_where_conditions(collection, params, group_by)
    q['where'] = 'WHERE ' + and_condition_formatter(conditions)

    if group_by:
        q['group_by'] = 'GROUP BY ' + ','.join(group_by)

    if params.limit:
        q['limit'] = f'LIMIT {params.limit}'

    if params.sort:
        # Order by here is a bit more complicated, we need to use an aggregate statement that
        # is normally in the select, but isn't in this case because we need the singular group_by
        # id there instead.
        desc = False
        if params.sort.startswith('-'):
            desc = True
            _order_field = params.sort[1:]
        else:
            _order_field = params.sort

        try:
            _order_by_aggregate = COLLECTION_ANALYSIS[collection][_order_field]
        except KeyError:
            # If our input sort field doesn't match one of the aggregate fields, then
            # skip adding and order by to the subquery
            pass
        else:
            q['order_by'] = f'ORDER BY {_order_by_aggregate}' + ' DESC' if desc else ''

    full_sub_query = _format_sql_query(SUBQUERY_TEMPLATE, **q)

    return Condition(name=group_by[0], operator='in', value=[full_sub_query], add_quotes=False)


def _build_order_by(params: CommonParameters) -> str:
    order_by = 'ORDER BY '

    if params.grouping_period:
        order_by += 'local_datetime'

    if params.sort:

        if params.grouping_period:
            order_by += ', '

        order_by_fields = []

        # Split the sort string by commas
        sorting_fields = params.sort.split(',')

        for field in sorting_fields:
            if field.startswith('-'):
                order_by_fields.append(f'{field[1:]} DESC')
            else:
                order_by_fields.append(field)

        # Join the fields with commas to form the ORDER BY clause
        order_by += ', '.join(order_by_fields)

    return order_by


def _format_sql_query(template, **query_dict):
    d = defaultdict(str, query_dict)
    sql_query = template.format(d=d)
    return sql_query


def _build_result_values(collection: Collections, params: CommonParameters,
                         query_result):
    results = []
    for qr in query_result:
        results.append({
            'value': _parse_collection_value(collection, qr),
            '_group': _parse_group_results(params, qr),
        })

    return results


def _build_timeline_result_values(collection: Collections, params: CommonParameters, query_result):
    results = []
    groups = groupby(query_result, lambda qr: (qr['start_datetime'], qr['end_datetime']))
    for (sdt, edt), data_points in groups:
        results.append({
            'start_datetime': timeutils.datetime_from_isoformat(sdt, True),
            'end_datetime': timeutils.datetime_from_isoformat(edt, True),
            'data_points': _build_result_values(collection, params, data_points)
        })
    return results


def _parse_collection_value(collection: Collections, query_result_row):
    """ Parse a tinybird result row based on requested collection analysis """
    result = {}
    for property_name in COLLECTION_ANALYSIS[collection].keys():
        result[property_name] = query_result_row[property_name]
    return result


def _parse_group_results(params: CommonParameters, query_result_row):
    if not params.group_by:
        return None

    result = {}
    for group in params.group_by:
        group_by = GROUP_BY_INPUT_MAP[group]
        for property_name in group_by:
            result[property_name] = query_result_row[property_name]
    return result
