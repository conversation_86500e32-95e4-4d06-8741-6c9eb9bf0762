"""
https://docs.github.com/en/rest/actions/workflows?apiVersion=2022-11-28
#create-a-workflow-dispatch-event
Parks implementation of github client for interacting with github from
flux over http
"""
import jwt
import time
import requests

from flux.adapters.logging import get_logger
from flux.apps.config import app_config
from flux.domains.common.errors import InternalError

logger = get_logger(__name__)


class GitHubAPIError(InternalError):
    """
    Exception for GitHub request failures
    """

    code = "github_request_failed"
    message = "Failed to process github request"


def log_error_response_api_client(response, *args, raise_exc=False, **kwargs):
    if not 200 <= response.status_code <= 299:
        content = response.content
        http_status = response.status_code
        message = "Error communicating with GitHub API."
        logger.error(
            message,
            content=content,
            http_status=http_status,
        )
        if raise_exc:
            msg_data = {
                "message": message,
                "http_status": http_status,
                "content": content,
            }
            msg = f"{msg_data}"
            raise GitHubAPIError(message=msg)


class GitHubClient:
    """
    Implements interfaces to interact with GitHub over http

    Ids of workflows that we have if new workflows are added
    need to get the id manually

    Todo: Implement a util in client to fetch workflow ids
    {
        'pull_request': 4352204,
        'stage': 4499701,
        'production': 4591232,
        'configuror': 4693091,
        'manual_deployment': 4693092,
        'run_flask_command': 4693093,
        'rollback': 9244109,
        'deploy_cron_server': 41138762,
        'stage_db_migration': 41138764,
        'requirements_check': 43284958,
    }
    """

    BASE_URL = 'https://api.github.com'
    REPO_OWNER = 'EventMobi'
    REPO = 'Flux'
    URL_MAP = {
        'workflows': f'{BASE_URL}/repos/{REPO_OWNER}/{REPO}/actions/workflows',
        'installations': 'https://api.github.com/app/installations',
    }

    def __init__(self):
        self.session = requests.Session()
        self.session.headers = {
            'Content-Type': 'application/json',
            'Authorization': None,
            'X-GitHub-Api-Version': '2022-11-28',
            'Accept': 'application/vnd.github+json',
        }  # type: ignore
        self.session.hooks["response"].append(log_error_response_api_client)

    def dispatch_workflow(self, ref='master', inputs=None, wf_id=None):
        """
        Dispatch a workflow
        """
        inputs = inputs if inputs else {}
        wf_id = wf_id if wf_id else app_config.GITHUB_RUN_COMMAND_WORKFLOW_ID
        url = f"{self.URL_MAP['workflows']}/{wf_id}/dispatches"
        request_payload = {'ref': ref, 'inputs': inputs}

        response = self._exec_request(
            method=self.session.post, url=url, request_payload=request_payload, retry=True
        )
        return response

    @staticmethod
    def _get_jwt_token():
        """
        Generate jwt token from pem
        """

        private_key = app_config.GITHUB_EXPORT_PRIVATE_KEY
        payload = {
            # Issued at time
            'iat': int(time.time()),
            # JWT expiration time (2 minutes maximum)
            'exp': int(time.time()) + 60 * 2,
            # GitHub App's identifier
            'iss': app_config.GITHUB_EXPORT_AUTH_APP_ID,
        }
        encoded_jwt = jwt.encode(payload, private_key, algorithm='RS256')
        return encoded_jwt

    def _get_access_token(self):
        """
        Get access token
        """
        jwt_token = self._get_jwt_token()
        self.session.headers['Authorization'] = f'Bearer {jwt_token}'
        url = self.URL_MAP['installations']
        response = self._exec_request(method=self.session.get, url=url)
        response = response.json()
        url = response[0]['access_tokens_url']
        response = self._exec_request(method=self.session.post, url=url)
        response = response.json()
        return response['token']

    def _set_auth_headers(self):
        """
        Get access token and update headers
        """
        access_token = self._get_access_token()
        self.session.headers['Authorization'] = f'token {access_token}'

    def _exec_request(self, method, url, request_payload=None, retry=False):
        """
        Execute the request
        """
        if self.session.headers['Authorization'] is None:
            self._set_auth_headers()

        def perform_request():
            if request_payload:
                return method(url=url, json=request_payload)
            return method(url=url)

        response = perform_request()
        if response.status_code == 401 and retry:
            self._set_auth_headers()
            response = perform_request()

        log_error_response_api_client(response, raise_exc=True)
        return response
