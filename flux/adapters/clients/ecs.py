import enum

import boto3

from flux.apps.config import app_config
from flux.utils.enum import StringEnum
from flux.domains.common.errors import (
    InternalError,
    InvalidOperationError,
    aws_exception,
)


class ECSAPIError(InternalError):
    pass


class InvalidECSEnvironmentError(InvalidOperationError):
    pass


class InvalidECSServiceError(InvalidOperationError):
    pass


class ECSEnvironment(StringEnum):
    dev = enum.auto()
    stage = enum.auto()
    production = enum.auto()


class ECSService(StringEnum):
    flux = enum.auto()


class ECS:

    def __init__(self):
        self._client = boto3.client(
            'ecs',
            region_name=app_config.AWS_REGION_NAME,
        )

    @aws_exception(ECSAPIError)
    def force_new_deployment(self, environment, service_name):
        if environment not in ECSEnvironment.all_values_in_string_list():
            raise InvalidECSEnvironmentError(message=f'{environment} is not valid environment')
        if service_name not in ECSService.all_values_in_string_list():
            raise InvalidECSServiceError(message=f'{environment} is not a valid service')
        return self._client.update_service(
            cluster=f'{environment}-fargate',
            service=f'{service_name}-{environment}',
            forceNewDeployment=True,
        )
