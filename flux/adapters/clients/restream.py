from http.client import RemoteDisconnected
import requests
from requests.exceptions import ConnectionError

from dataclasses import dataclass
from flux.utils.lazy_object_proxy import Proxy
from libthumbor import CryptoURL
from urllib.parse import urljoin

from flux.apps.config import app_config
from flux.utils.helpers import retry_on_exception
from flux.utils.timeutils import epoch_timestamp_seconds_from_naive_datetime

from flux.adapters.logging import get_logger

from flux.domains.common.errors import InternalError


class RestreamAPIError(InternalError):
    pass


class RestreamRetryAPIError(RestreamAPIError):
    pass


crypto = Proxy(lambda: CryptoURL(key=app_config.THUMBOR_SECURITY_KEY))

logger = get_logger(__name__)

__all__ = ['RestreamRtmpDestination', 'RestreamRoom', 'get_restream_client', 'RestreamAPIError']


@dataclass
class RestreamRtmpDestination:
    rtmpUrl: str
    streamKey: str


@dataclass
class RestreamRoom:
    id: str
    hostUrl: str
    guestUrl: str


class RestreamClient:
    def __init__(self):
        self.restream_client_id = app_config.RESTREAM_CLIENT_ID
        self.restream_client_secret = app_config.RESTREAM_CLIENT_SECRET
        self.url_base = app_config.RESTREAM_API_ENDPOINT
        self.request_session = requests.Session()
        self.default_avatar_url = self._get_default_avatar()

    @staticmethod
    def _get_default_avatar():
        thumbor_host = app_config.THUMBOR_HOST
        thumbor_url = crypto.generate(image_url=app_config.RESTREAM_DEFAULT_AVATAR)
        return urljoin(thumbor_host, thumbor_url)

    def create_restream_user(self) -> str:
        res = self._initiate_api_request('/users')
        return str(res['userId'])

    def create_restream_room(self, restream_user_id: str, title: str,
                             rtmp_destination: RestreamRtmpDestination) -> RestreamRoom:
        # Restream supports multiple destinations but for ease of use we are only implementing single destination
        destinations = [
            {'rtmpUrl': rtmp_destination.rtmpUrl, 'streamKey': rtmp_destination.streamKey}
        ]
        res = self._initiate_api_request(
            f'/users/{restream_user_id}/events',
            {
                'title': title,
                'destinations': destinations,
                'restartAllowed': True
            }
        )

        return RestreamRoom(
            id=res['eventId'],
            hostUrl=res['hostUrl'],
            guestUrl=res['guestUrl'],
        )

    def create_chat_message(self, restream_user_id, restream_room_id, display_name, image_url,
                            message_text, sent_datetime):
        res = self._initiate_api_request(
            f'/users/{restream_user_id}/events/{restream_room_id}/chat',
            {
                'type': 'text',
                'createdAt': epoch_timestamp_seconds_from_naive_datetime(sent_datetime) * 1000,
                'author': {
                    'name': display_name,
                    'avatarUrl': image_url if image_url else self.default_avatar_url
                },
                'text': message_text,
            }
        )
        return res['success']

    def get_restream_room(self, restream_user_id, restream_room_id) -> RestreamRoom:
        res = self._initiate_api_request(
            f'/users/{restream_user_id}/events/{restream_room_id}'
        )

        return RestreamRoom(
            id=res['eventId'],
            hostUrl=res['hostUrl'],
            guestUrl=res['guestUrl'],
        )

    def get_restream_host_access_token(self, restream_user_id) -> str:
        res = self._initiate_api_request(
            f'/users/{restream_user_id}/access-tokens'
        )

        return res['accessToken']

    @retry_on_exception(exceptions=RestreamRetryAPIError,
                        delay=1,
                        attempts=3)
    def _initiate_api_request(self, sub_url: str, request_body: dict = None):
        if not request_body:
            request_body = {}
        request_body['clientId'] = self.restream_client_id
        request_body['clientSecret'] = self.restream_client_secret

        try:
            res = self.request_session.post(
                self.url_base + sub_url,
                json=request_body
            )
            res_json = res.json()

            if res.status_code == 200:
                return res_json
            else:
                restream_error_message = ''
                if 'error' in res_json:
                    restream_error_message = res_json['error']['message']
                elif 'message' in res_json:
                    restream_error_message = res_json['message']

                try:
                    res.raise_for_status()
                except Exception as ex:
                    logger.error(
                        'Restream API HTTP Error',
                        error=f'{ex.__class__.__name__}: {restream_error_message}',
                        exc_info=ex
                    )
                    raise RestreamAPIError()
        except (RemoteDisconnected, ConnectionError) as e:
            raise RestreamRetryAPIError from e

        except Exception as e:
            raise RestreamAPIError from e


restream_client = Proxy(lambda: RestreamClient())


def get_restream_client() -> RestreamClient:
    return restream_client
