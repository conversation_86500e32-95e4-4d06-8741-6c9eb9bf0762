import datetime
from dataclasses import dataclass
from typing import Optional

import requests

from flux.adapters.logging import get_logger
from flux.domains.common import errors
from flux.domains.common.errors import InternalError, PublicError
from flux.domains.common.validation.validate import ValidationError

BASE_URL = 'https://api.hubapi.com'
UPDATE_CONTACT_ENDPOINT_URL = '/contacts/v1/contact/vid/{contact_id}/profile/'
CHAT_TOKEN_URL = '/conversations/v3/visitor-identification/tokens/create/'
SCHEMA_URL = '/crm/v3/schemas/{object_type}/'
SCHEMAS_URL = '/crm/v3/schemas/'
BASE_OBJECT_URL = '/crm/v3/objects/{object_type}'
OBJECT_URL = BASE_OBJECT_URL + '/{object_id}?properties={properties}'
ASSOCIATIONS_URL = '/crm/v3/objects/{from_object_type}/{object_id}/associations/{to_object_type}'
OWNERS_URL = '/crm/v3/owners/{user_id}?idProperty=userId'
OBJECT_ASSOCIATION_URL = ('/crm/v4/objects/{from_object_type}/{from_object_id}/associations'
                          '/default/{to_object_type}/{to_object_id}')
BOOL_VALUE_MAP = {  # Currently, hubspot is accepting boolean values as string TRUE and FALSE
    True: 'TRUE',
    False: 'FALSE',
    '1': 'TRUE',
    '0': 'FALSE',
}
DEAL_TO_ORGANIZATION_ASSOCIATION = 'deal_to_organization'
DEAL_TO_UCL_CONTRACTS_ASSOCIATION = 'deal_to_ucl_contracts'
EXP_ORGANIZATION_TO_UCL_CONTRACTS_ASSOCIATION = 'exp_organization_to_ucl_contracts'


logger = get_logger(__name__)


class HubSpotAPIError(InternalError):
    pass


class HubSpotNonExistentAssociationError(InternalError):
    pass


class HubspotRequiredParams(PublicError):
    source_kind = errors.ErrorSourceKinds.body
    code = 'missing_value'


@dataclass
class HubspotOrganization:
    object_type_id: str
    object_id: str
    exp_organization_id: str
    name: str
    license_type: str = 'event_credit'


@dataclass
class HubspotEvent:
    object_type_id: str
    object_id: str
    name: str
    package_template: Optional[str]
    provisioning_status: str
    credit_expiry_date: Optional[str]
    features: dict


@dataclass
class HubspotContract:
    object_type_id: str
    object_id: str
    exp_contract_id: str
    contract_start_date: str
    contract_end_date: str
    contract_is_available: str
    purchased_user_credits: str
    purchased_email_credits: str
    deal_id: str


@dataclass
class HubspotDeal:
    object_type_id: str
    object_id: str
    user_id: str


class HubSpotClient:
    access_token: str

    def __init__(self, access_token):
        self.access_token = access_token
        self.session = requests.Session()
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.access_token}'
        }
        self.session.headers = headers
        self.session.hooks['response'].append(
            log_error_response_api_client
        )

    def update_contact(self, contact_id, invite_link, event_code, trial_expiry):
        params = {'contact_id': contact_id}
        url = f'{BASE_URL}{UPDATE_CONTACT_ENDPOINT_URL}'.format(**params)
        d_truncated = _convert_date(
            datetime.date(trial_expiry.year, trial_expiry.month, trial_expiry.day)
        )
        logger.info(
            "Hubspot Client - update_contact",
            contact_id=contact_id,
            event_code=event_code,
            trial_expiry=d_truncated,
        )
        response = self.session.post(
            url=url,
            json={
                'properties': [
                    {
                        'property': 'organizer_activation_link',
                        'value': invite_link
                    },
                    {
                        'property': 'trial_event_link',
                        'value': f'https://eventmobi.com/{event_code}',
                    },
                    {
                        'property': 'trial_expire_date',
                        'value': d_truncated
                    }
                ]
            }
        )
        if response.status_code != 204:
            raise HubSpotAPIError(
                message='Error sending data to HubSpot API',
                http_status=response.status_code,
            )

    def create_chat_conversation_token(self, first_name, last_name, email):
        _validate_params(first_name=first_name, last_name=last_name, email=email)
        data = {
            'firstName': first_name,
            'lastName': last_name,
            'email': email,
        }
        url = f'{BASE_URL}{CHAT_TOKEN_URL}'
        response = self.session.post(
            url=url,
            json=data
        )
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error sending data to HubSpot API',
                http_status=response.status_code,
            )
        return response.json()

    def get_hubspot_deal_organizations(self, deal_object_type, deal_object_id,
                                       association_name=DEAL_TO_ORGANIZATION_ASSOCIATION):
        """
        Get all organizations from hubspot against provided hubspot deal id
        Extracts the org obj type id from deal_to_organization schema
        """
        organization_object_type = self.get_object_type_from_association(
            object_type=deal_object_type,
            association_name=association_name,
        )
        organization_object_ids = self.get_associated_object_id(
            from_object_type=deal_object_type,
            object_id=deal_object_id,
            to_object_type=organization_object_type
        )
        if not organization_object_ids:
            raise HubSpotNonExistentAssociationError

        return [self.get_hubspot_organization(
            object_type=organization_object_type,
            object_id=organization_object_id)
            for organization_object_id in organization_object_ids]

    def get_organization_contracts(self, organization_object_type, organization_object_id,
                                   association_name=EXP_ORGANIZATION_TO_UCL_CONTRACTS_ASSOCIATION):
        """
        Get all contracts from hubspot against provided hubspot organization id
        Extracts the contract obj type id from organization_to_contract schema
        """
        return self._get_associated_contracts(
            from_object_type=organization_object_type,
            from_object_id=organization_object_id,
            association_name=association_name,
        )

    def get_deal_contracts(self, deal_object_type, deal_object_id,
                           association_name=DEAL_TO_UCL_CONTRACTS_ASSOCIATION):
        """
        Get all contracts from hubspot against provided hubspot deal id
        """
        return self._get_associated_contracts(
            from_object_type=deal_object_type,
            from_object_id=deal_object_id,
            association_name=association_name,
        )

    def _get_associated_contracts(self, from_object_type, from_object_id, association_name):
        contract_object_type = self.get_object_type_from_association(
            object_type=from_object_type,
            association_name=association_name,
        )
        contract_object_ids = self.get_associated_object_id(
            from_object_type=from_object_type,
            object_id=from_object_id,
            to_object_type=contract_object_type
        )
        if not contract_object_ids:
            raise HubSpotNonExistentAssociationError

        return [self.get_hubspot_contract(
            object_type=contract_object_type,
            object_id=contract_object_id)
            for contract_object_id in contract_object_ids]

    def get_hubspot_contract(self, object_type, object_id):
        """
        Get hubspot org by org object id
        """
        params = {
            'object_type': object_type,
            'object_id': object_id,
            'properties': 'deal_id,exp_contract_id,contract_start_date,contract_end_date,'
                          'contract_is_available,purchased_user_credits,purchased_email_credits',
        }
        url = f'{BASE_URL}{OBJECT_URL}'.format(**params)
        response = self.session.get(url=url)
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error getting Contract Object from HubSpot API',
                http_status=response.status_code,
            )
        json = response.json()
        return HubspotContract(
            object_type_id=object_type,
            object_id=json['properties']['hs_object_id'],
            exp_contract_id=json['properties']['exp_contract_id'],
            contract_start_date=json['properties']['contract_start_date'],
            contract_end_date=json['properties']['contract_end_date'],
            contract_is_available=json['properties']['contract_is_available'],
            purchased_user_credits=json['properties']['purchased_user_credits'],
            purchased_email_credits=json['properties']['purchased_email_credits'],
            deal_id=json['properties']['deal_id'],
        )

    def get_hubspot_organization(self, object_type, object_id):
        """
        Get hubspot org by org object id
        """
        params = {
            'object_type': object_type,
            'object_id': object_id,
            'properties': 'name,exp_organization_id,license_type',
        }
        url = f'{BASE_URL}{OBJECT_URL}'.format(**params)
        response = self.session.get(url=url)
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error getting Organization Object from HubSpot API',
                http_status=response.status_code,
            )
        json = response.json()
        license_type = json['properties'].get('license_type', 'event_credit')
        if not license_type:
            license_type = 'event_credit'
        return HubspotOrganization(
            object_type_id=object_type,
            object_id=json['properties']['hs_object_id'],
            exp_organization_id=json['properties']['exp_organization_id'],
            name=json['properties']['name'],
            license_type=license_type
        )

    def update_hubspot_organization(self, object_type, object_id, exp_organization_id):
        properties = dict(
            properties=dict(exp_organization_id=exp_organization_id)
        )
        self._update_hubspot_object(object_type, object_id, properties)

    def get_hubspot_event(self, event_object_type, event_object_id, config_keys):
        features_to_override = ''
        for config_key in config_keys:
            features_to_override += config_key.key + ','

        params = {
            'object_type': event_object_type,
            'object_id': event_object_id,
            'properties': f'name,provisioning_status,package_template,credit_expiry_date,'
                          f'{features_to_override}',
        }
        url = f'{BASE_URL}{OBJECT_URL}'.format(**params)
        response = self.session.get(url=url)
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error getting Event Object from HubSpot API',
                http_status=response.status_code,
            )
        json = response.json()

        return HubspotEvent(
            object_type_id=event_object_type,
            object_id=json['properties']['hs_object_id'],
            name=json['properties']['name'],
            provisioning_status=json['properties']['provisioning_status'],
            package_template=json['properties']['package_template'],
            credit_expiry_date=json['properties']['credit_expiry_date'],
            features=self._get_hubspot_event_features(config_keys, json['properties'])
        )

    @staticmethod
    def _get_hubspot_event_features(config_keys, event_props):
        features = {}
        for config_key in config_keys:
            if config_key.key and config_key.key in event_props:
                value = event_props[config_key.key]
                if value:
                    features[config_key.key] = {
                        'value': bool(value) if config_key.type == 'boolean' else str(value)
                    }
        return features

    def get_user_email(self, user_id):
        params = {'user_id': user_id}
        url = f'{BASE_URL}{OWNERS_URL}'.format(**params)
        response = self.session.get(
            url=url
        )
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error getting Owner from HubSpot API',
                http_status=response.status_code,
            )
        json = response.json()
        return json['email']

    def update_event_basic_properties(self, event, provisioning_status=False):
        # if event not created from hubspot integration
        if not event.crm_event_id:
            return

        event_object_type, _ = self._get_event_properties_from_schema()

        properties_to_update = dict(
            properties=dict(
                event_id=event.shortcode,
                name=event.name,
                start_date=_convert_date(event.start_date),
                end_date=_convert_date(event.end_date),
                location_name=event.location_name,
                location_address=event.location_address,
                event_description=event.description,
                event_type=_validate_event_type(event.event_type),
            )
        )
        if provisioning_status:
            properties_to_update['provisioning_status'] = 'active'

        self._update_hubspot_object(event_object_type, event.crm_event_id, properties_to_update)

    def update_event_config_keys(self, event, config_cursor, configs_to_update=None):
        # if event not created from hubspot integration
        if not event.crm_event_id:
            return

        event_object_type, existing_properties = self._get_event_properties_from_schema()
        properties_to_update = _update_config_keys(
            event_id=event.id,
            config_cursor=config_cursor,
            existing_properties=existing_properties,
            configs_to_update=configs_to_update
        )
        self._update_hubspot_object(event_object_type, event.crm_event_id, properties_to_update)

    def create_hs_event(self, event, config_cursor, hs_deal_ids, hs_organization_id):
        event_object_type, existing_properties = self._get_event_properties_from_schema()
        properties = dict(
            properties=dict(
                event_id=event.shortcode,
                name=event.name,
                start_date=_convert_date(event.start_date),
                end_date=_convert_date(event.end_date),
                provisioning_status='active',
                location_name=event.location_name,
                location_address=event.location_address,
                event_description=event.description,
                event_type=_validate_event_type(event.event_type),
            )
        )
        configs_properties = _update_config_keys(
            event_id=event.id,
            config_cursor=config_cursor,
            existing_properties=existing_properties
        )['properties']

        properties['properties'].update(configs_properties)

        hs_event_object = self._create_hubspot_object(event_object_type, properties)

        # Associate the newly created 'Event' object in HS with HS deal objects
        for deal_id in hs_deal_ids:
            self.associate_objects(
                from_object_type=event_object_type,
                from_object_id=hs_event_object['id'],
                to_object_type='deal',
                to_object_id=deal_id,
            )
            logger.info(f"Associated 'Event' object with id {event_object_type} "
                        f"with Deal with id {deal_id}")

        # Associate the newly created 'Event' object with HS Organization object
        exp_org_object_schema = self._get_custom_object_schema(
            object_label='Experience Organization',
        )
        if not exp_org_object_schema:
            raise HubSpotAPIError(
                message='Error getting Experience Organization Schema from HubSpot API',
                http_status=404,
            )
        exp_org_object_type = exp_org_object_schema['objectTypeId']
        self.associate_objects(
            from_object_type=event_object_type,
            from_object_id=hs_event_object['id'],
            to_object_type=exp_org_object_type,
            to_object_id=hs_organization_id,
        )
        logger.info(f"Associated 'Event' object with id {event_object_type} "
                    f"with Experience Organization with id {hs_organization_id}")

        return hs_event_object

    def update_credit_event(self, event_object_type, event_object_id, credit):

        d_truncated = datetime.date(
            credit.created_at.year,
            credit.created_at.month,
            credit.created_at.day,
        )
        exp_truncated = datetime.date(
            credit.expires_at.year,
            credit.expires_at.month,
            credit.expires_at.day,
        )
        properties_body = dict(
            credit_id=str(credit.id),
            credit_created_at=_convert_date(d_truncated),
            provisioning_status='Credit created',
            exp_provision_response='Credit successfully created',
            credit_expiry_date=_convert_date(exp_truncated),
        )
        for feature in credit.features:
            properties_body[feature.config_key_key] = _convert_config_to_hs_values(
                config_type=feature.type,
                config_value=feature.value)

        properties = dict(
                    properties=properties_body
        )
        self._update_hubspot_object(event_object_type, event_object_id, properties)

    def update_hs_contract(
        self,
        contract_object_id: str,
        contract_properties: dict,
        contract_object_type: str = None,
    ):
        if not contract_object_type:
            custom_object_schema = self._get_custom_object_schema(object_label='UCL Contract')
            if not custom_object_schema:
                raise HubSpotAPIError(
                    message='Error getting UCL Contract Schema from HubSpot API',
                    http_status=404,
                )
            contract_object_type = custom_object_schema['objectTypeId']

        properties = dict(properties=contract_properties)
        self._update_hubspot_object(contract_object_type, contract_object_id, properties)

    def update_deal_error(self, object_type, object_id, message):
        properties = dict(
            properties=dict(experience_response=message)
        )
        self._update_hubspot_object(object_type, object_id, properties)

    def update_event_error(self, event_object_type, event_object_id, message):
        properties = dict(
            properties=dict(exp_provision_response=message, provisioning_status='Error')
        )
        self._update_hubspot_object(event_object_type, event_object_id, properties)

    def _create_hubspot_object(self, object_type, properties, ):
        url = BASE_URL + BASE_OBJECT_URL.format(object_type=object_type)
        response = self.session.post(
            url=url,
            json=properties
        )
        if response.status_code != 201:
            raise HubSpotAPIError(
                message='Error creating object in HubSpot API',
                http_status=response.status_code,
            )
        return response.json()

    def _update_hubspot_object(self, object_type, object_id, properties):
        if properties is None or properties['properties'] == {}:
            return
        logger.info(
            "Hubspot Client - update_hubspot_object",
            object_type=object_type,
            object_id=object_id,
            properties=properties,
        )
        params = {
            'object_type': object_type,
            'object_id': object_id,
            'properties': '',
        }
        url = f'{BASE_URL}{OBJECT_URL}'.format(**params)
        response = self.session.patch(
            url=url,
            json=properties
        )
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error sending data to HubSpot API',
                http_status=response.status_code,
            )

    def _get_event_properties_from_schema(self):
        event_object_type = None
        existing_properties = []
        custom_object_schema = self._get_custom_object_schema(object_label='Event')
        if custom_object_schema:
            event_object_type = custom_object_schema['objectTypeId']
            for prop in custom_object_schema['properties']:
                existing_properties.append(prop['name'])
        return event_object_type, existing_properties

    def _get_custom_object_schema(self, object_label):
        # GET SCHEMAS TO RECOVER EVENT OBJECT TYPE
        url = f'{BASE_URL}{SCHEMAS_URL}'
        response = self.session.get(url=url)
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error getting schemas from HubSpot API',
                http_status=response.status_code,
            )
        json = response.json()
        for custom_object_schema in json['results']:
            if custom_object_schema['labels']['singular'] == object_label:
                return custom_object_schema

        return None

    def get_object_type_from_association(self, object_type, association_name):
        params = {'object_type': object_type}
        url = f'{BASE_URL}{SCHEMA_URL}'.format(**params)
        response = self.session.get(
            url=url
        )
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error getting Event Schema from HubSpot API',
                http_status=response.status_code,
            )
        json = response.json()
        for association in json['associations']:
            from_event_association = association['fromObjectTypeId'] == object_type
            is_organization_to_event = association['name'] == association_name
            if from_event_association and is_organization_to_event:
                return association['toObjectTypeId']

        return None

    def associate_objects(self, from_object_type, from_object_id, to_object_type, to_object_id):
        params = {
            'from_object_type': from_object_type,
            'from_object_id': from_object_id,
            'to_object_type': to_object_type,
            'to_object_id': to_object_id,
        }
        url = f'{BASE_URL}{OBJECT_ASSOCIATION_URL}'.format(**params)
        response = self.session.put(url=url)
        json = response.json()
        if response.status_code != 200:
            raise HubSpotAPIError(
                message=f'Error associating objects in HubSpot API'
                        f'{json}',
                http_status=response.status_code,
            )

    def get_associated_object_id(self, from_object_type, object_id, to_object_type):
        params = {
            'from_object_type': from_object_type,
            'object_id': object_id,
            'to_object_type': to_object_type,
        }
        url = f'{BASE_URL}{ASSOCIATIONS_URL}'.format(**params)
        response = self.session.get(
            url=url
        )
        if response.status_code != 200:
            raise HubSpotAPIError(
                message='Error getting Event Associations from HubSpot API',
                http_status=response.status_code,
            )
        json = response.json()
        return [
            obj['id'] for obj in json['results']
        ]


def _validate_params(**payload):
    error_list = []
    for key, value in payload.items():
        if not value:
            error_list.append(HubspotRequiredParams(message=f'{key}: Required value'))

    if error_list:
        raise ValidationError(errors=error_list)


def get_hubspot_client(access_token):
    return HubSpotClient(access_token=access_token)


def log_error_response_api_client(response, *args, **kargs):
    if response.status_code not in [200, 201, 204]:
        content = response.content
        http_status = response.status_code
        logger.error(
            'Error communicating with HubSpot API.',
            content=content,
            http_status=http_status,
        )


def _convert_value_input(value_type, value):
    if value_type == 'boolean':
        value = 'TRUE' if value else ''
    elif value_type == 'integer' and isinstance(value, str):
        value = int(value) if value and value.isdigit() else value
    return value


def _convert_date(date):
    return int(datetime.datetime.strptime(str(date), "%Y-%m-%d").timestamp()) * 1000


def _update_config_keys(event_id, config_cursor, existing_properties, configs_to_update=None):
    properties_to_update = dict(properties={})
    all_config_keys = config_cursor.get_config_keys()
    config_keys = configs_to_update if configs_to_update else all_config_keys

    logger.info("Hubspot Client - All config keys", all_config_keys=all_config_keys)
    logger.info("Hubspot Client - configs_to_update", configs_to_update=configs_to_update)
    logger.info("Hubspot Client - existing_properties", existing_properties=existing_properties)

    for prop in existing_properties:
        if prop in config_keys:
            config_key = all_config_keys[prop]
            config_value = config_cursor.get(key_name=prop, owner_id=event_id)
            logger.info("Hubspot Client - config_key", key=config_key, value=config_value)
            if config_key:
                properties_to_update['properties'][config_key['key']] = _convert_value_input(
                    config_key['type'],
                    config_value,
                )
    return properties_to_update


def _convert_config_to_hs_values(config_type, config_value):
    """
    Convert the config values to hubspot acceptable values
    """
    if config_value is None:
        return config_value
    if config_type == 'boolean':
        hs_value = BOOL_VALUE_MAP.get(config_value)
        if hs_value:
            return hs_value
    return str(config_value)


def _validate_event_type(event_type):
    """
    Validate event type against allowed HubSpot event types.
    Returns 'Other' if event_type is not in the allowed list.
    """
    allowed_event_types = [
        'Award Ceremony',
        'Conference/Summit (no Expo)',
        'Conference/AGM (with Expo)',
        'Gala/Fundraiser',
        'Incentive Trip',
        'Internal Training',
        'Networking Event',
        'Product Launch',
        'Roadshow',
        'Sales Kick Off',
        'Seminar',
        'Town Hall',
        'Expo/Tradeshow',
        'User/Partner Conference',
        'Workshop'
    ]

    return event_type if event_type in allowed_event_types else 'Other'
