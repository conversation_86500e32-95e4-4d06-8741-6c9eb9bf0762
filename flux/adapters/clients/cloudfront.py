from dataclasses import dataclass

import boto3
from botocore.exceptions import ClientError

from flux.utils import enum
from flux.adapters.logging import get_logger
from flux.apps.config import app_config
from flux.domains.common.errors import (
    InternalError,
    aws_exception,
    NotFoundError,
)

logger = get_logger(__name__)


@dataclass
class CloudFrontDistribution:
    id: str
    arn: str
    domain_name: str


@dataclass
class CloudFrontDistributionTags:
    tags: str
    environment: str
    terraform: bool


class CloudFrontDistributionStatus(enum.Enum):
    Deployed = enum.auto()
    InProgress = enum.auto()


class CloudFrontAPIError(InternalError):
    pass


class CloudFrontNoSuchDistributionError(NotFoundError):
    message = 'The provided cloudfront distribution does not exist.'


class CloudFront:

    def __init__(self):
        if app_config.is_dev_environment_and_dev_aws():
            self._client = boto3.client('cloudfront', endpoint_url=app_config.AWS_DEV_ENDPOINT)
        else:
            self._client = boto3.client('cloudfront')

    def _get_distribution(self, distribution_id):
        return self._client.get_distribution(
            Id=distribution_id,
        )

    def _update_distribution(self, distribution_id, aliases, viewer_certificate, enabled):
        distribution = self._get_distribution(distribution_id)
        distribution_config = distribution['Distribution']['DistributionConfig']

        caller_reference = distribution_config['CallerReference']
        if enabled:
            caller_reference = aliases['Items'][0]

        self._client.update_distribution(
            DistributionConfig={
                'CallerReference': caller_reference,
                'Aliases': aliases,
                'DefaultRootObject': distribution_config['DefaultRootObject'],
                'Origins': distribution_config['Origins'],
                'OriginGroups': distribution_config['OriginGroups'],
                'DefaultCacheBehavior': distribution_config['DefaultCacheBehavior'],
                'CacheBehaviors': distribution_config['CacheBehaviors'],
                'CustomErrorResponses': distribution_config['CustomErrorResponses'],
                'Comment': distribution_config['Comment'],
                'Logging': distribution_config['Logging'],
                'PriceClass': distribution_config['PriceClass'],
                'Enabled': enabled,
                'ViewerCertificate': viewer_certificate,
                'Restrictions': distribution_config['Restrictions'],
                'WebACLId': distribution_config['WebACLId'],
                'HttpVersion': distribution_config['HttpVersion'],
                'IsIPV6Enabled': distribution_config['IsIPV6Enabled'],
            },
            Id=distribution_id,
            IfMatch=distribution['ETag'],
        )

    @aws_exception(CloudFrontAPIError)
    def create_distribution(self, alternate_domain_name, alternate_domain_cert_arn, event_id):
        distribution = self._client.create_distribution_with_tags(
            DistributionConfigWithTags={
                'DistributionConfig': {
                    'CallerReference': alternate_domain_name,
                    'Aliases': {
                        'Quantity': 1,
                        'Items': [
                            alternate_domain_name,
                        ]
                    },
                    'DefaultRootObject': '',
                    'Origins': {
                        'Quantity': 1,
                        'Items': [
                            {
                                'Id': alternate_domain_name,
                                'DomainName': app_config.MOBILEAPP_DOMAIN,
                                'CustomOriginConfig': {
                                    'HTTPPort': 80,
                                    'HTTPSPort': 443,
                                    'OriginProtocolPolicy': 'https-only',
                                    'OriginSslProtocols': {
                                        'Quantity': 1,
                                        'Items': [
                                            'TLSv1.2',
                                        ]
                                    },
                                    'OriginReadTimeout': 30,
                                    'OriginKeepaliveTimeout': 5
                                },
                                'ConnectionAttempts': 3,
                                'ConnectionTimeout': 10,
                                'OriginShield': {
                                    'Enabled': False
                                }
                            },
                        ]
                    },
                    'DefaultCacheBehavior': {
                        'TargetOriginId': alternate_domain_name,
                        'ViewerProtocolPolicy': 'redirect-to-https',
                        'AllowedMethods': {
                            'Quantity': 7,
                            'Items': [
                                'GET', 'HEAD', 'POST', 'PUT', 'PATCH', 'OPTIONS', 'DELETE'
                            ],
                            'CachedMethods': {
                                'Quantity': 2,
                                'Items': [
                                    'GET', 'HEAD'
                                ]
                            }
                        },
                        'SmoothStreaming': False,
                        'Compress': True,
                        'CachePolicyId': app_config.AWS_CLOUDFRONT_CACHE_POLICY_ID,
                        'OriginRequestPolicyId': app_config.AWS_CLOUDFRONT_ORIGIN_POLICY_ID,
                        'ResponseHeadersPolicyId': app_config.AWS_CLOUDFRONT_RESPONSE_POLICY_ID
                    },
                    'Comment': f'Custom Domain Distribution: {event_id} - {app_config.ENV}',
                    'PriceClass': 'PriceClass_100',
                    'Enabled': True,
                    'ViewerCertificate': {
                        'CloudFrontDefaultCertificate': False,
                        'ACMCertificateArn': alternate_domain_cert_arn,
                        'SSLSupportMethod': 'sni-only',
                        'MinimumProtocolVersion': 'TLSv1.2_2021'
                    },
                    'WebACLId': app_config.AWS_CLOUDFRONT_WAF_ACL_ARN,
                    'HttpVersion': 'http2',
                    'IsIPV6Enabled': True
                },
                'Tags': {
                    'Items': [
                        {
                            'Key': 'environment',
                            'Value': app_config.ENV
                        },
                        {
                            'Key': 'event',
                            'Value': f'{event_id}'
                        },
                        {
                            'Key': 'terraform',
                            'Value': 'false'
                        }
                    ]
                }
            }
        )
        return CloudFrontDistribution(
            id=distribution['Distribution']['Id'],
            arn=distribution['Distribution']['ARN'],
            domain_name=distribution['Distribution']['DomainName'],
        )

    @aws_exception(CloudFrontAPIError)
    def enable_distribution(
        self,
        distribution_id,
        alternate_domain_name,
        alternate_domain_cert_arn,
    ):
        self._update_distribution(
            distribution_id=distribution_id,
            aliases={
                'Quantity': 1,
                'Items': [
                    alternate_domain_name,
                ]
            },
            viewer_certificate={
                'CloudFrontDefaultCertificate': False,
                'ACMCertificateArn': alternate_domain_cert_arn,
                'SSLSupportMethod': 'sni-only',
                'MinimumProtocolVersion': 'TLSv1.2_2021'
            },
            enabled=True,
        )

    @aws_exception(CloudFrontAPIError)
    def disable_distribution(self, distribution_id):
        self._update_distribution(
            distribution_id=distribution_id,
            aliases={
                'Quantity': 0,
                'Items': [],
            },
            viewer_certificate={
                'CloudFrontDefaultCertificate': True,
                'SSLSupportMethod': 'sni-only',
                'MinimumProtocolVersion': 'TLSv1.2_2021'
            },
            enabled=False,
        )

    @aws_exception(CloudFrontAPIError)
    def delete_distribution(self, distribution_id):
        distribution = self._get_distribution(distribution_id)
        self._client.delete_distribution(
            Id=distribution_id,
            IfMatch=distribution['ETag']
        )

    @aws_exception(CloudFrontAPIError)
    def list_distribution_tags(self, distribution_arn):
        tags = self._client.list_tags_for_resource(
            Resource=distribution_arn,
        )['Tags']['Items']
        env = [tag['Value'] for tag in tags if tag['Key'].lower() == 'environment'][0]
        terraform = [tag['Value'] for tag in tags if tag['Key'].lower() == 'terraform'][0]
        return CloudFrontDistributionTags(
            tags=tags,
            environment=env,
            terraform=bool(terraform == 'true')
        )

    @aws_exception(CloudFrontAPIError)
    def list_distributions(self, max_items='300', marker=None):
        if marker:
            return self._client.list_distributions(
                Marker=marker,
                MaxItems=max_items,
            )
        return self._client.list_distributions(
            MaxItems=max_items,
        )

    @aws_exception(CloudFrontAPIError)
    def remove_non_prod_distributions(self, marker=None):
        distributions = self.list_distributions(marker=marker)
        for distribution in distributions['DistributionList']['Items']:
            try:
                tags = self.list_distribution_tags(distribution['ARN'])
                if not tags.terraform and tags.environment in ['dev', 'stage', 'deleted']:
                    try:
                        self.delete_distribution(distribution['Id'])
                        logger.info(f'Deleted CloudFront distribution: {distribution["ARN"]} - '
                                    f'{distribution["DomainName"]}')
                    except CloudFrontAPIError:
                        logger.warning(
                            f'Cannot delete CloudFront distribution {distribution["ARN"]} - '
                            f'{distribution["DomainName"]} - Trying to disable the distribution')
                        self.disable_distribution(distribution['Id'])
            except CloudFrontAPIError:
                logger.warning(f'Cannot check CloudFront distribution {distribution["ARN"]}')
        if 'NextMarker' in distributions['DistributionList']:
            self.remove_non_prod_distributions(
                marker=distributions['DistributionList']['NextMarker'],
            )

    @aws_exception(CloudFrontAPIError)
    def tag_resource(self, distribution_arn, distribution_tags):
        self._client.tag_resource(
            Resource=distribution_arn,
            Tags={
                'Items': [
                    {
                        'Key': key,
                        'Value': value
                    } for key, value in distribution_tags.items()
                ]
            }
        )

    @aws_exception(CloudFrontAPIError)
    def describe_distribution(self, distribution_id):
        try:
            return self._client.get_distribution(
                Id=distribution_id,
            )
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchDistribution':
                raise CloudFrontNoSuchDistributionError
            raise ClientError(error_response=e.response, operation_name=e.operation_name)

    @aws_exception(CloudFrontAPIError)
    def deploy_distribution(
        self, alternate_domain_name, alternate_domain_cert_arn, organization_id
    ):
        distribution = self._client.create_distribution_with_tags(
            DistributionConfigWithTags={
                'DistributionConfig': {
                    'CallerReference': alternate_domain_name,
                    'Aliases': {
                        'Quantity': 1,
                        'Items': [
                            alternate_domain_name,
                        ],
                    },
                    'DefaultRootObject': '',
                    'Origins': {
                        'Quantity': 1,
                        'Items': [
                            {
                                'Id': alternate_domain_name,
                                'DomainName': app_config.MOBILEAPP_DOMAIN,
                                'CustomOriginConfig': {
                                    'HTTPPort': 80,
                                    'HTTPSPort': 443,
                                    'OriginProtocolPolicy': 'https-only',
                                    'OriginSslProtocols': {
                                        'Quantity': 1,
                                        'Items': [
                                            'TLSv1.2',
                                        ],
                                    },
                                    'OriginReadTimeout': 30,
                                    'OriginKeepaliveTimeout': 5,
                                },
                                'ConnectionAttempts': 3,
                                'ConnectionTimeout': 10,
                                'OriginShield': {'Enabled': False},
                            },
                        ],
                    },
                    'DefaultCacheBehavior': {
                        'TargetOriginId': alternate_domain_name,
                        'ViewerProtocolPolicy': 'redirect-to-https',
                        'AllowedMethods': {
                            'Quantity': 7,
                            'Items': ['GET', 'HEAD', 'POST', 'PUT', 'PATCH', 'OPTIONS', 'DELETE'],
                            'CachedMethods': {'Quantity': 2, 'Items': ['GET', 'HEAD']},
                        },
                        'SmoothStreaming': False,
                        'Compress': True,
                        'CachePolicyId': app_config.AWS_CLOUDFRONT_CACHE_POLICY_ID,
                        'OriginRequestPolicyId': app_config.AWS_CLOUDFRONT_ORIGIN_POLICY_ID,
                        'ResponseHeadersPolicyId': app_config.AWS_CLOUDFRONT_RESPONSE_POLICY_ID,
                    },
                    'Comment': f'Custom Domain Distribution: {organization_id} - {app_config.ENV}',
                    'PriceClass': 'PriceClass_100',
                    'Enabled': True,
                    'ViewerCertificate': {
                        'CloudFrontDefaultCertificate': False,
                        'ACMCertificateArn': alternate_domain_cert_arn,
                        'SSLSupportMethod': 'sni-only',
                        'MinimumProtocolVersion': 'TLSv1.2_2021',
                    },
                    'WebACLId': app_config.AWS_CLOUDFRONT_WAF_ACL_ARN,
                    'HttpVersion': 'http2',
                    'IsIPV6Enabled': True,
                },
                'Tags': {
                    'Items': [
                        {'Key': 'environment', 'Value': app_config.ENV},
                        {'Key': 'organization_id', 'Value': f'{organization_id}'},
                        {'Key': 'terraform', 'Value': 'false'},
                    ]
                },
            }
        )
        return CloudFrontDistribution(
            id=distribution['Distribution']['Id'],
            arn=distribution['Distribution']['ARN'],
            domain_name=distribution['Distribution']['DomainName'],
        )
