"""
Parks implementation of Chromium client
Chromium client needs chromium binaries to run browser in headless mode
"""

from contextlib import contextmanager

from playwright.sync_api import Browser
from playwright.sync_api import TimeoutError as TimeError, Error

from flux.adapters.logging import get_logger
from flux.apps.config import app_config
from flux.domains.event.screen_capture.entities import (
    ScreenCaptureOptions,
    ScreenCapOutputTypes,
    MAX_TIMEOUT,
    PDFCaptureOptions,
    ImageCaptureOptions,
)
from flux.domains.event.screen_capture.exceptions import PageTimeOutError, PageCaptureError
from flux.utils.chromium_utils import set_executable_path
logger = get_logger(__name__)


@contextmanager
def chromium_browser(pw_context, executable_path=None):
    """
    Provide browser context
    """
    if not executable_path:
        config_path = app_config.PLAYWRIGHT_CHROMIUM_PATH
        executable_path = config_path if config_path else set_executable_path()

    logger.info(f"using chromium binaries from {executable_path}")
    browser = pw_context.chromium
    try:
        browser = browser.launch(
            executable_path=executable_path,
            headless=True,
            args=['--font-render-hinting=none']
        )
        yield browser
    except Error as e:
        raise PageCaptureError(message=e.message)
    finally:
        if isinstance(browser, Browser) and browser.is_connected():
            browser.close()


@contextmanager
def new_page(browser):
    """
    Provide browser page context
    """
    browser_page = (browser.new_page(ignore_https_errors=True)
                    if app_config.is_dev_environment_and_dev_aws() else browser.new_page())
    page = PageWrapper(page=browser_page)
    try:
        yield page
    finally:
        browser_page.close()


class PageWrapper:
    """
    Implements wrapper class for capturing provided page
    """

    def __init__(self, page):
        self._page = page

    def set_view_port_size(self, width, height):
        """
        Set browser page viewport
        Always accepts value in pixels
        """

        self._page.set_viewport_size({"width": int(width), "height": int(height)})

    def wait_for_timeout(self, timeout):
        """Add delay before capturing"""

        self._page.wait_for_timeout(timeout)

    def navigate(self, url, timeout=MAX_TIMEOUT):
        """
        Navigate to provided url
        """

        self._page.goto(url=url, timeout=timeout)

    def wait_for_state(self, state, timeout=MAX_TIMEOUT):
        """
        Wait for provided page state

        Not sensitive to error
        """

        try:
            self._page.wait_for_load_state(state=state, timeout=timeout)
        except TimeError as e:
            logger.info("Page state timeout")
            logger.info(e.message)

    def wait_for_selector(self, selector, state, timeout=MAX_TIMEOUT):
        """
        Wait for specific html element referenced by class or id
        satisfying provided state
        """

        try:
            self._page.locator(selector).wait_for(state=state, timeout=timeout)
        except TimeError as e:
            logger.error(e.message)
            raise PageTimeOutError

    def pdf(self, settings):
        """
        Capture current page with provided settings
        """
        return self.__execute(settings=settings, operation=self._page.pdf)

    def screenshot(self, settings):
        """
        Capture current page with provided settings
        """
        return self.__execute(settings=settings, operation=self._page.screenshot)

    @staticmethod
    def __execute(settings, operation):
        try:
            return operation(**settings)
        except Error as e:
            logger.error(e.message)
            raise PageCaptureError(message=e.message)


class ChromiumClient:
    """
    Implements interfaces to capture provide source url using
    provided capture settings
    """

    def __init__(self, browser=None):
        self._browser = browser

    @property
    def browser(self):
        return self._browser

    @browser.setter
    def browser(self, browser):
        self._browser = browser

    def capture(
        self, source_url, output_type: ScreenCapOutputTypes, options: ScreenCaptureOptions
    ):
        """
        Capture provided source url with provided options
        """
        with new_page(self._browser) as page:
            logger.info(f"Opening url {source_url}")
            page.navigate(url=source_url)
            if options.wait_for:
                if options.wait_for.page_state:
                    page_state = options.wait_for.page_state
                    logger.info(f"Waiting for page state {page_state.state}")
                    page.wait_for_state(state=page_state.state.name, timeout=page_state.timeout)

                if options.wait_for.selector:
                    page_selector = options.wait_for.selector
                    logger.info(f"Waiting for selector {page_selector.selector}")
                    page.wait_for_selector(
                        selector=page_selector.selector,
                        state=page_selector.state.name,
                        timeout=page_selector.timeout,
                    )
                if options.wait_for.timeout:
                    logger.info("add delay for specified timeout")
                    page.wait_for_timeout(options.wait_for.timeout)

            settings = self._transform_options(options=options)

            if output_type == ScreenCapOutputTypes.pdf:
                logger.info(f"Capturing pdf with settings: {settings}")
                return page.pdf(settings=settings)

            if output_type == ScreenCapOutputTypes.image:
                logger.info(f"Capturing screenshot with settings: {settings}")
                page.set_view_port_size(width=options.width, height=options.height)
                return page.screenshot(settings=settings)

        return None

    @staticmethod
    def _transform_options(options):
        """
        Transform options to chromium acceptable settings
        """

        settings = {}

        if isinstance(options, ImageCaptureOptions):
            if options.crop:
                clip = {
                    'x': options.crop.x,
                    'y': options.crop.y,
                    'width': options.crop.width,
                    'height': options.crop.height,
                }
                settings['clip'] = clip
            if options.full_page:
                settings['full_page'] = options.full_page

        if isinstance(options, PDFCaptureOptions):
            if options.print_background:
                settings['print_background'] = options.print_background
            if options.scale:
                settings['scale'] = options.scale
            if options.prefer_css_page_size:
                settings['prefer_css_page_size'] = options.prefer_css_page_size
            if options.landscape:
                settings['landscape'] = options.landscape

            if options.height:
                settings['height'] = f'{options.height}{options.unit.name}'

            if options.width:
                settings['width'] = f'{options.width}{options.unit.name}'

            if options.paper_format:
                settings['format'] = options.paper_format.name

            if options.margin:
                settings['margin'] = {
                    'top': f'{options.margin.top}{options.margin.unit.name}',
                    'bottom': f'{options.margin.bottom}{options.margin.unit.name}',
                    'left': f'{options.margin.left}{options.margin.unit.name}',
                    'right': f'{options.margin.right}{options.margin.unit.name}',
                }

        return settings
