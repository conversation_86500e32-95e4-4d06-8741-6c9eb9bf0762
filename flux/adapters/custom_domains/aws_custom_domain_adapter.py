from typing import List, Optional
from uuid import UUID

import dns.resolver

from flux.adapters.clients.acm import AC<PERSON>, ACMNoSuchCertificateError
from flux.adapters.clients.cloudfront import (
    CloudFront,
    CloudFrontDistribution,
    CloudFrontNoSuchDistributionError,
    CloudFrontDistributionStatus,
)
from flux.adapters.clients.route53 import Route53, Route53Action
from flux.apps.config import app_config
from flux.domains.organization.custom_domains.adapter import (
    CustomDomainAdapter,
    CustomDomainResource,
    DNSRecord,
    DNSRecordVerificationStatus,
    DNSRecordPurpose,
)


class AWSCustomDomainAdapter(CustomDomainAdapter):

    def __init__(self, acm_client: ACM, cloudfront_client: CloudFront, route53_client: Route53):
        self.acm_client = acm_client
        self.cloudfront_client = cloudfront_client
        self.route53_client = route53_client

    def start_domain_setup(self, domain_name: str, organization_id: UUID) -> str:
        return self.acm_client.request_ssl_certificate(
            domain_name=domain_name, organization_id=organization_id
        )

    def get_validation_records(
        self, custom_domain_id: UUID, organization_id: UUID, certificate_arn: str
    ) -> List[DNSRecord]:

        certificate = self.acm_client.describe_certificate(certificate_arn=certificate_arn)

        if certificate.cname_key is None or certificate.cname_value is None:
            # DNS record is not yet generated
            return []

        domain_cname_prefix = f'{custom_domain_id}-{app_config.ENV}'
        if app_config.is_production_environment():
            domain_cname_prefix = f'{custom_domain_id}'

        domain_cname = self.route53_client.record_name(domain_cname_prefix)

        return [
            DNSRecord(
                host=certificate.cname_key,
                value=certificate.cname_value,
                purpose=DNSRecordPurpose.certificate,
            ),
            DNSRecord(
                host=f'{certificate.domain_name}.',
                value=domain_cname,
                purpose=DNSRecordPurpose.domain,
            ),
        ]

    def verify_dns_record(self, host: str, value: str) -> DNSRecordVerificationStatus:
        try:
            dns_record = dns.resolver.resolve(host, 'CNAME')
            for record in dns_record:
                if str(record.target) == value:
                    return DNSRecordVerificationStatus.CORRECT
        except dns.resolver.NXDOMAIN:
            # Record does not exist
            return DNSRecordVerificationStatus.DOES_NOT_EXIST
        return DNSRecordVerificationStatus.INCORRECT

    def get_domain_resources(
        self, certificate_arn: Optional[str] = None, distribution_id: Optional[str] = None
    ) -> Optional[CustomDomainResource]:

        certificate = None
        if certificate_arn:
            certificate = self.acm_client.describe_certificate(certificate_arn=certificate_arn)

        cloudfront_distribution = None
        if distribution_id:
            cloudfront_distribution = self.cloudfront_client.describe_distribution(
                distribution_id=distribution_id
            )

        if not certificate and not cloudfront_distribution:
            return None

        return CustomDomainResource(certificate=certificate, distribution=cloudfront_distribution)

    def finalize_domain_setup(
        self,
        domain_cname: str,
        domain_name: str,
        organization_id: UUID,
        certificate_arn: str,
        distribution_id: Optional[str] = None,
    ) -> CloudFrontDistribution:
        if distribution_id:
            try:
                distribution = self.cloudfront_client.describe_distribution(distribution_id)[
                    'Distribution'
                ]

                # Check the status of the distribution
                distribution_enabled = distribution['DistributionConfig']['Enabled']
                attached_certificate = distribution['DistributionConfig']['ViewerCertificate'].get(
                    'ACMCertificateArn', ''
                )

                if distribution_enabled and attached_certificate == certificate_arn:
                    # Cloudfront distribution is already active and nothing to do!
                    return CloudFrontDistribution(
                        id=distribution['Id'],
                        arn=distribution['ARN'],
                        domain_name=distribution['DomainName'],
                    )

                if not distribution_enabled:
                    # Cloudfront distribution is in disabled state. Enable it.
                    self.cloudfront_client.enable_distribution(
                        distribution_id=distribution_id,
                        alternate_domain_name=domain_name,
                        alternate_domain_cert_arn=certificate_arn,
                    )

                    distribution = self.cloudfront_client.describe_distribution(distribution_id)[
                        'Distribution'
                    ]
                    return CloudFrontDistribution(
                        id=distribution['Id'],
                        arn=distribution['ARN'],
                        domain_name=distribution['DomainName'],
                    )
            except CloudFrontNoSuchDistributionError:
                # The provided distribution does not exist. Create a new one.
                pass

        new_distribution = self.cloudfront_client.deploy_distribution(
            alternate_domain_name=domain_name,
            alternate_domain_cert_arn=certificate_arn,
            organization_id=organization_id,
        )

        self.route53_client.change_resource_record_sets(
            action=Route53Action.CREATE.name,
            record_name=domain_cname,
            record_value=new_distribution.domain_name,
        )

        return new_distribution

    def disable_domain_resources(self, distribution_id: str):
        try:
            self.cloudfront_client.describe_distribution(distribution_id)
            self.cloudfront_client.disable_distribution(distribution_id)
        except CloudFrontNoSuchDistributionError:
            pass

    def delete_domain_resources(
        self,
        certificate_arn: str,
        domain_cname: Optional[str] = None,
        distribution_id: Optional[str] = None,
    ):

        if distribution_id:
            try:
                distribution = self.cloudfront_client.describe_distribution(distribution_id)
                distribution_enabled = distribution['Distribution']['DistributionConfig'][
                    'Enabled'
                ]
                distribution_status = distribution['Distribution']['Status']

                if distribution_enabled:
                    # Return here even though the distribution is immediately disabled
                    # but cloudfront takes time to propogate the changes.
                    # The CRON job will try deletion in the next run.
                    self.cloudfront_client.disable_distribution(distribution_id)
                    return False

                if distribution_status == CloudFrontDistributionStatus.InProgress.name:
                    return False

                self.cloudfront_client.delete_distribution(distribution_id)

                if domain_cname:
                    self.route53_client.change_resource_record_sets(
                        action=Route53Action.DELETE.name,
                        record_name=domain_cname,
                        record_value=distribution['Distribution']['DomainName'],
                    )
            except CloudFrontNoSuchDistributionError:
                pass

        try:
            self.acm_client.delete_ssl_certificate(certificate_arn)
        except ACMNoSuchCertificateError:
            pass
        return True
