import time

from . import redis

from flux.utils.serializer import dumps, loads
from flux.adapters.logging import get_event_logger

cache_logger = get_event_logger('FluxCacheEvent')


def _default_key_builder(*args, **kwargs):
    parts = list(map(str, args))
    for key in sorted(kwargs.keys()):
        parts.append(f'{key}={kwargs[key]}')
    return ':'.join(parts)


# The cache returns this object to indicate that there is no value for
# the requested key. Need to distinguish this from None which is a valid value.
NO_VALUE = object()

# We use this value when checking if cache needs to be updated. Our time should
# be accurate enough since we run NTP daemon on our instances.
CLOCK_DRIFT = 0.1


class RevisionCache():
    """
    This cache stores data revision as well as creation time in order to
    avoid unnecessary cache updates.
    """
    def __init__(self, client, read_client, ttl, namespace='flux_cache'):
        self._client = client
        self._read_client = read_client
        self._ttl = ttl
        self._namespace = namespace

    def get_or_create(self, key, creator, logger=None,
                      serializer=None, deserializer=None):
        """
        Get the value from cache if possible, create the value else.
        The function to create a value should return a tuple containing
        data revision and value itself.
        """
        raw_key = self._build_key(key)
        logger = self._bind_logger(logger, 'get_or_create', key, raw_key)
        data = self.get(key=key, raw_key=raw_key, bind_logger=False,
                        logger=logger, deserializer=deserializer)
        if data is NO_VALUE:
            data = self._create(raw_key, creator, logger,
                                serializer=serializer)
        return data

    def get(self, key, raw_key=None, bind_logger=True, logger=None,
            deserializer=None):
        raw_key = raw_key or self._build_key(key)
        raw_data = self._get_data(raw_key)
        data = NO_VALUE
        if raw_data is not None:
            if deserializer is None:
                data = loads(raw_data)
            else:
                data = deserializer(raw_data)
        if bind_logger:
            logger = self._bind_logger(logger, 'get', key, raw_key)
        logger.debug(cache_hit=data is not NO_VALUE)
        return data

    def refill(self, key, creator, revision=None, updated_at=None,
               force_refill=False, logger=None):
        raw_key = self._build_key(key)
        logger = self._bind_logger(logger, 'refill', key, raw_key)
        create_required = force_refill or self.is_cache_outdated(
            raw_key=raw_key,
            revision=revision,
            updated_at=updated_at,
        )
        if create_required:
            self._create(raw_key, creator, logger)
        logger.debug(created=create_required)

        return create_required

    def is_cache_outdated(self, key=None, raw_key=None, revision=None,
                          updated_at=None, logger=None):
        raw_key = raw_key or self._build_key(key)
        logger = self._bind_logger(logger, 'is_cache_outdated', key, raw_key)
        outdated = True
        cache_created_at, old_revision = self._get_metadata(raw_key)
        if revision is not None and old_revision is not None:
            if old_revision >= revision:
                outdated = False
        if updated_at is not None and cache_created_at is not None:
            if cache_created_at > updated_at + CLOCK_DRIFT:
                outdated = False
        logger.debug(outdated=outdated, updated_at=updated_at, cache_created_at=cache_created_at,
                     revision=revision, old_revision=old_revision)
        return outdated

    def invalidate(self, key):
        raw_key = self._build_key(key)
        self._client.delete(raw_key)

    @staticmethod
    def _bind_logger(logger, cache_event, key, raw_key):
        logger = logger or cache_logger
        extra = {}
        if isinstance(key, dict):
            extra = {subkey: str(value) for (subkey, value) in key.items()}
        logger = logger.bind(
            event=cache_event,
            cache_key=raw_key,
            **extra
        )
        return logger

    def _get_metadata(self, raw_key):
        cache_created_at, old_revision = self._read_client.hmget(
            raw_key,
            ('created_at', 'revision'),
        )
        if cache_created_at:
            cache_created_at = float(cache_created_at)
        if old_revision:
            old_revision = int(old_revision)
        else:
            old_revision = None
        return cache_created_at, old_revision

    def _get_data(self, raw_key):
        return self._read_client.hget(raw_key, 'data')

    def _create(self, raw_key, creator, logger, serializer=None):
        created_at = time.time()
        revision, data = creator()
        duration = time.time() - created_at
        if serializer is None:
            serialized_data = dumps(data)
        else:
            serialized_data = serializer(data)
        self._client.hmset(
            raw_key,
            {
                'created_at': created_at,
                'revision': revision or '',
                'data': serialized_data,
            },
        )
        self._client.expire(raw_key, self._ttl)
        logger.debug('created', duration=duration)
        return data

    def _build_key(self, key):
        if isinstance(key, dict):
            key = ':'.join(map(str, key.values()))
        return f'{self._namespace}:{key}'

    def cache_on_arguments(self, revision_getter=None, key_builder=None,
                           should_cache_func=None, serializer=None,
                           deserializer=None):
        """
        Decorator to cache function or method result.

        You can provide a function (revision_getter) which will return a
        data revision.

        In order to change the way cache key is generated, provide key_builder
        function. Note that key builder may return string with a key or
        it may return a dictionary which will be transformed to a key. The
        advantage of providing a dictionary is that it will provide more
        context for cache metrics. Only the dictionary values will be used
        as part of actual cache key however, and dictionary support relies
        on ordered dictionaries.

        In order to apply caching only in specific cases, provide
        should_cache_func which should return True or False.
        All the functions should take the same arguments as decorated
        function or method.

        When calling the decorated function, you need to make an extra
        call specifying the kind of action you want to do. Possible actions
        are get_or_create (for regular cache access) and refill_cache (when
        you need to refill the cache and don't need the data). Example:

        >>> value = cached_func(arg1, kwarg1=10).get_or_create()
        """
        key_builder = key_builder or _default_key_builder

        def decorator(func):
            return _CacheOnArgumentsWrapper(
                cache=self,
                func=func,
                revision_getter=revision_getter,
                key_builder=key_builder,
                should_cache_func=should_cache_func,
                serializer=serializer,
                deserializer=deserializer,
            )

        return decorator


class _CacheOnArgumentsWrapper():
    def __init__(self, cache, func, revision_getter=None, key_builder=None,
                 should_cache_func=None, parent_obj=None, serializer=None,
                 deserializer=None):
        self._cache = cache
        self._func = func
        self._args = self._unbound_args = self._kwargs = None
        self._revision_getter = revision_getter
        self._key_builder = key_builder
        self._should_cache_func = should_cache_func
        self._parent_obj = parent_obj
        self._serializer = serializer
        self._deserializer = deserializer

    def __get__(self, obj, type=None):
        """
        We implement descriptor protocol to support decorating methods.
        In case we decorate a method, we need to call it using
        descriptor protocol in order to pass self argument.
        This argument is also preserved in order to pass it to
        helper methods (or functions).
        """
        if obj is None:
            func = self._func
        else:
            func = self._func.__get__(obj, type)
        return self.__class__(
            cache=self._cache,
            func=func,
            revision_getter=self._revision_getter,
            key_builder=self._key_builder,
            should_cache_func=self._should_cache_func,
            parent_obj=obj,
            serializer=self._serializer,
            deserializer=self._deserializer,
        )

    def __call__(self, *args, **kwargs):
        self._args = args
        self._unbound_args = args
        if self._parent_obj is not None:
            self._unbound_args = (self._parent_obj,) + args
        self._kwargs = kwargs
        return self

    def get_or_create(self, logger=None):
        if self._should_skip_cache:
            return self._func(*self._args, **self._kwargs)

        return self._cache.get_or_create(
            self._key,
            self._creator,
            logger=logger,
            serializer=self._serializer,
            deserializer=self._deserializer,
        )

    def get_from_cache(self, logger=None):
        """
        Get the value from cache. If not found, returns NO_VALUE object.
        """
        if self._should_skip_cache:
            return

        return self._cache.get(self._key, logger=logger,
                               deserializer=self._deserializer)

    def refill_cache(self, revision=None, updated_at=None, force_refill=False,
                     logger=None):
        if self._should_skip_cache:
            return

        revision = self._build_revision(revision)
        return self._cache.refill(self._key, self._creator, revision=revision,
                                  updated_at=updated_at,
                                  force_refill=force_refill,
                                  logger=logger)

    def is_cache_outdated(self, revision=None, updated_at=None, logger=None):
        return self._cache.is_cache_outdated(key=self._key, revision=revision,
                                             updated_at=updated_at, logger=logger)

    def invalidate(self):
        return self._cache.invalidate(self._key)

    @property
    def _should_skip_cache(self):
        if self._should_cache_func is None:
            return False
        return not self._should_cache_func(
            *self._unbound_args,
            **self._kwargs
        )

    def _creator(self):
        revision = self._build_revision(revision=None)
        data = self._func(*self._args, **self._kwargs)
        return revision, data

    def _build_revision(self, revision):
        if revision:
            return revision
        if self._revision_getter:
            return self._revision_getter(*self._unbound_args, **self._kwargs)
        return None

    @property
    def _key(self):
        return self._key_builder(*self._unbound_args, **self._kwargs)


def init_cache(ttl, client=redis.clients.flux,
               read_client=redis.clients.flux_read):
    return RevisionCache(
        client=client,
        read_client=read_client,
        ttl=ttl,
    )


revision_cache = init_cache(ttl=60 * 60 * 24 * 14)
login_status_cache = init_cache(ttl=30)
config_cache = init_cache(ttl=5 * 60)
native_live_stream_chat_sync_cache = init_cache(ttl=60)
user_permissions_cache = init_cache(ttl=5 * 60)  # 5 minutes
auth_token_scope_cache = init_cache(ttl=5 * 60)  # 5 minutes
