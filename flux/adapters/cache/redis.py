from flux.utils.lazy_object_proxy import Proxy
from dataclasses import dataclass, fields

from redis import StrictRedis
from fakeredis import FakeServer, FakeStrictRedis

from flux.apps.config import app_config
from flux.utils.dot_dict import DotDict

"""
The workflow here is the following:
1) During import (i.e. in tests) redis clients are initialized with lazy
proxies. These proxies are used throughout the code on module level for
initialization, but not for making redis requests yet.
2) During application initialization actual redis client implementation is
provided using finish_redis_initialization function. After that, when
proxy is accessed, it will use _REDIS_CLS to initialize the connection.
"""


@dataclass
class RedisClients:
    flux: StrictRedis
    flux_read: StrictRedis
    asap: StrictRedis
    tasks: StrictRedis


_fake_redis_server = None


def _setup_redis_client(host, port, db, **params):
    def _setup():
        if _REDIS_CLS is None:
            raise RuntimeError('Redis implementation not provided yet')
        elif _REDIS_CLS == FakeStrictRedis:
            # Defining a fake redis server explicitly is needed after fakeredis 1.0
            global _fake_redis_server
            if not _fake_redis_server:
                _fake_redis_server = FakeServer()
            params["server"] = _fake_redis_server
        return _REDIS_CLS(
            host=str(host),
            port=str(port),
            db=str(db),
            **params,
        )
    return Proxy(_setup)


def init_redis_clients():
    flux_read = _setup_redis_client(
        host=app_config.get_lazy_or('FLUX_READ_REDIS_HOST', 'FLUX_REDIS_HOST'),
        port=app_config.get_lazy_or('FLUX_READ_REDIS_PORT', 'FLUX_REDIS_PORT'),
        db=app_config.get_lazy('FLUX_REDIS_DB'),
        decode_responses=True,
        socket_connect_timeout=1,
        retry_on_timeout=True,
        ssl=app_config.get_lazy('REDIS_SSL'),
    )

    r = _setup_redis_client(
        host=app_config.get_lazy('FLUX_REDIS_HOST'),
        port=app_config.get_lazy('FLUX_REDIS_PORT'),
        db=app_config.get_lazy('FLUX_REDIS_DB'),
        decode_responses=True,
        socket_connect_timeout=1,
        retry_on_timeout=True,
        ssl=app_config.get_lazy('REDIS_SSL'),
    )

    asap_r = _setup_redis_client(
        host=app_config.get_lazy('FLUX_REDIS_HOST'),
        port=app_config.get_lazy('FLUX_REDIS_PORT'),
        db=app_config.get_lazy('REDIS_ASAP_DB'),
        socket_connect_timeout=1,
        retry_on_timeout=True,
        decode_responses=True,
        ssl=app_config.get_lazy('REDIS_SSL'),
    )

    tasks_r = _setup_redis_client(
        host=app_config.get_lazy('REDIS_HOST'),
        port=app_config.get_lazy('REDIS_PORT'),
        db=app_config.get_lazy('TASKS_CACHE_DB'),
        decode_responses=True,
        socket_connect_timeout=3,
        retry_on_timeout=True,
        ssl=app_config.get_lazy('REDIS_SSL'),
    )

    return RedisClients(
        flux=r,
        flux_read=flux_read,
        asap=asap_r,
        tasks=tasks_r,
    )


_REDIS_CLS = None

clients = init_redis_clients()


def finish_redis_initialization(redis_cls):
    global _REDIS_CLS
    _REDIS_CLS = redis_cls


def list_clients():
    return [
        getattr(clients, field.name) for field in fields(clients)
    ]


keys = DotDict(
    # flux keys
    OAUTH_AUTH_CODE='F:OAC:{client_id}:{code}',

    # A serialized json dict containing data associated with the given
    # access_token.
    OAUTH_BEARER_TOKEN='F:OBT:{access_token}',
    OAUTH_CLIENT_CREDENTIALS_BEARER_TOKEN='F:CCBT:{client_id}:{client_secret}',

    # oauth2 clients
    OAUTH2_CLIENT='oauth2_client:{id}',

    # reactor custom styles
    TASKS_REACTOR_CUSTOM_CSS='tasks:reactor:cust_css',

    # email bounce back keys
    EVENT_EMAILS_BOUNCE_BACK_KEY='eventemailsbounceback:{from_address}',
    EVENT_EMAILS_BOUNCE_BACK_DEFAULT_EXPIRATION=24 * 60 * 60,  # 1 day

)
