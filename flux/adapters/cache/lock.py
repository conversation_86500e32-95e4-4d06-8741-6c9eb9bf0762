import time

from contextlib import contextmanager
from functools import wraps

import backoff
from redlock import Redlock, MultipleRedlockException

from flux.adapters.cache import redis
from flux.adapters.logging import get_event_logger, get_logger


logger = get_logger(__name__)

lock_event_logger = get_event_logger('FluxLockEvent')


@contextmanager
def try_lock(redis_client, key, timeout, event_logger=None):
    """
    Context manager which provides unblocking lock. Returned value can be
    checked for truth, if it is truth the lock has been acquired.
    Timeout is in milliseconds.
    """
    dlm = Redlock(connection_list=[redis_client])
    event_logger = _get_event_logger(key, timeout, event_logger)

    redis_failed_on_lock = redis_failed_on_unlock = False
    lock = None

    event_logger.info('try_lock_started')
    started_at = time.time()
    try:
        try:
            lock = dlm.lock(key, timeout)
        except MultipleRedlockException:
            redis_failed_on_lock = True
            logger.exception('Redis failed while locking', key=key)

        if not lock:
            event_logger.info('lock_not_acquired')
        yield lock

    finally:
        duration = time.time() - started_at
        try:

            if lock:
                dlm.unlock(lock)
        except MultipleRedlockException:
            redis_failed_on_unlock = True
            logger.exception('Redis failed while unlocking', key=key)

        event_logger.info('try_lock_finished', duration=duration,
                          lock_acquired=bool(lock),
                          redis_failed_on_lock=redis_failed_on_lock,
                          redis_failed_on_unlock=redis_failed_on_unlock)


class LockWaitTimeoutExpiredError(Exception):
    pass


def locked_execution(key, lock_timeout, redis_client=None, wait_timeout=None):
    redis_client = redis_client or redis.clients.flux

    def wrapped(func):
        @wraps(func)
        @backoff.on_exception(
            backoff.expo,
            LockWaitTimeoutExpiredError,
            max_time=wait_timeout or lock_timeout,
        )
        def inner_wrapped(*args, **kwargs):
            with try_lock(redis_client, key, lock_timeout) as locked:
                if locked:
                    return func(*args, **kwargs)

            raise LockWaitTimeoutExpiredError(
                f'Failed to acquire lock for key {key} within {wait_timeout}',
            )

        return inner_wrapped

    return wrapped


def _get_event_logger(key, timeout, event_logger=None):
    event_logger = event_logger or lock_event_logger
    return event_logger.bind(lock_key=key, lock_timeout=timeout)
