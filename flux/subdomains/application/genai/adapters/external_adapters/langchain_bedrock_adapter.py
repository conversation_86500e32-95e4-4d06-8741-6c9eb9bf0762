import json
import time
from typing import Dict, List, Any, Optional, Iterable
from uuid import UUID

import boto3
from botocore.config import Config
from langchain_community.chat_models import BedrockChat
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from langchain.agents import Agent<PERSON>xecutor, create_structured_chat_agent
from langchain.tools import BaseTool
from langchain.memory import ConversationBufferMemory
from langchain.callbacks.base import BaseCallbackHandler
from langchain_core.messages import BaseMessage
from langchain_core.prompts import ChatPromptTemplate

from flux.adapters.logging import get_logger
from flux.apps.config import app_config
from flux.domains.common.errors import InternalError, aws_exception
from flux.subdomains.application.genai.adapters.callbacks.token_counting_handler import (
    TokenCountingHandler
)
from flux.subdomains.application.genai.interfaces.llm_adapter import LLMAdapter, LLMResponse
from flux.subdomains.application.genai.adapters.callbacks.tool_tracking_handler import (
    ToolTrackingHandler
)
from flux.subdomains.application.genai.entities.context_providers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ContextProvider,
)
from flux.utils.enum import StringEnum

logger = get_logger(__name__)


class LangChainAPIError(InternalError):
    """Error raised when LangChain or underlying API calls fail"""


class BedrockModelID(StringEnum):
    """Enumeration of supported Bedrock model IDs"""
    CLAUDE_3_5_HAIKU = "claude-3.5-haiku"
    CLAUDE_3_7_SONNET = "claude-3.7-sonnet"


# Comprehensive configuration for all supported Bedrock models
BEDROCK_MODEL_CONFIG = {
    BedrockModelID.CLAUDE_3_5_HAIKU: {
        "model_id": "anthropic.claude-3-haiku-20240307-v1:0",
        "arn": "arn:aws:bedrock:us-east-1:275468266209:inference-profile/"
               "us.anthropic.claude-3-5-haiku-20241022-v1:0",
        "provider": "anthropic",
        "costs": {
            "input_cost_per_1k": 0.0008,  # Cost per 1K input tokens
            "output_cost_per_1k": 0.004,  # Cost per 1K output tokens
        },
        "capabilities": {
            "supports_system_parameter": False,
            "max_tokens": 5000,
        }
    },
    BedrockModelID.CLAUDE_3_7_SONNET: {
        "model_id": "anthropic.claude-3-sonnet-20240229-v1:0",
        "arn": "arn:aws:bedrock:us-east-1:275468266209:inference-profile/"
               "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        "provider": "anthropic",
        "costs": {
            "input_cost_per_1k": 0.003,  # Cost per 1K input tokens
            "output_cost_per_1k": 0.015,  # Cost per 1K output tokens
        },
        "capabilities": {
            "supports_system_parameter": True,
            "max_tokens": 5000,
        }
    }
}


class LangChainBedrockAdapter(LLMAdapter):
    """Implementation of LLMAdapter using LangChain with AWS Bedrock"""

    def __init__(self, model_id: BedrockModelID = BedrockModelID.CLAUDE_3_7_SONNET,
                 tools: Optional[List[BaseTool]] = None,
                 callbacks: Optional[List[BaseCallbackHandler]] = None,
                 context_providers: Optional[List[ContextProvider]] = None):
        """
        Initialize the LangChain Bedrock adapter

        Args:
            model_id: The model ID to use (defaults to Claude 3.5 Haiku)
            tools: Optional list of LangChain tools to use with the model
            callbacks: Optional list of callback handlers to track execution
            context_providers: Optional list of context providers to inject data into prompts
        """
        self.model_id = model_id
        self.tools = tools or []

        # Set up context providers
        self.context_providers = context_providers or []
        self.context_builder = ContextBuilder()
        for provider in self.context_providers:
            self.context_builder.add_provider(provider)

        # Initialize callbacks
        self.callbacks = callbacks or []

        # Automatically add a token counting handler if not already present
        has_token_counter = any(isinstance(cb, TokenCountingHandler) for cb in self.callbacks)
        if not has_token_counter:
            self.token_counter = TokenCountingHandler()
            self.callbacks.append(self.token_counter)
        else:
            # Find the existing token counter
            self.token_counter = next(
                (cb for cb in self.callbacks if isinstance(cb, TokenCountingHandler))
            )

        self.configs = Config(
            request_checksum_calculation='when_required',
        )
        self._client = None
        self._llm = None
        self._agent_executor = None

        # Track tool tracking handlers specifically
        self.tool_trackers = [cb for cb in self.callbacks if isinstance(cb, ToolTrackingHandler)]

        # Get model configuration
        self.model_config = BEDROCK_MODEL_CONFIG.get(model_id, {})
        if not self.model_config:
            logger.warning(f"No configuration found for model ID: {model_id}")

    def _get_bedrock_client(self):
        """Get or create the Bedrock client"""
        if self._client is None:

            # This code is commented out as in the original for now
            if app_config.is_dev_environment_and_dev_aws():
                self._client = boto3.client(
                    'bedrock-runtime',
                    region_name=app_config.AWS_REGION_NAME,
                    endpoint_url=app_config.AWS_DEV_ENDPOINT,
                    config=self.configs,
                )
            else:
                self._client = boto3.client(
                    service_name='bedrock-runtime',
                    region_name=app_config.AWS_REGION_NAME,
                    config=self.configs,
                )
        return self._client

    @property
    def llm(self):
        """Get or create the LangChain Bedrock LLM"""
        if self._llm is None:
            # Determine model-specific parameters
            langchain_model_arn = self.model_config.get("arn", str(self.model_id))
            max_tokens = (
                self.model_config.get("capabilities", {}).get("max_tokens")
            )
            provider = self.model_config.get("provider")

            self._llm = BedrockChat(
                client=self._get_bedrock_client(),
                model_id=langchain_model_arn,
                provider=provider,
                model_kwargs={
                    "temperature": 0.2,
                    "max_tokens": max_tokens,
                    "anthropic_version": "bedrock-2023-05-31",
                    # Add any additional model-specific parameters here
                    "top_p": 0.999,
                },
                callbacks=self.callbacks
            )
        return self._llm

    @property
    def agent_executor(self):
        """Get or create an agent executor with tools using structured chat agent"""
        if not self.tools:
            logger.info("No tools provided, agent executor will not be created.")
            return None

        # Ensure LLM is initialized
        if self._llm is None:
            # Trigger LLM initialization if not already done
            _ = self.llm
            if self._llm is None:
                logger.error("LLM could not be initialized. Agent executor cannot be created.")
                return None

        # Create memory object
        logger.debug("Creating ConversationBufferMemory for agent.")
        memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True,  # Structured chat agents work better with message objects
            output_key='output'  # Explicitly set output key, often needed by AgentExecutor
        )

        # --- Prompt Setup for Structured Chat ---
        logger.debug("Building context string for agent prompt.")
        context_string = self.context_builder.build_context_string()

        # This prompt structure guides the LLM to use a JSON blob for tool calls.
        # It's adapted from common structured chat agent prompts
        # (like hwchase17/structured-chat-agent).
        # Explicitly telling the LLM the expected JSON keys for tools is helpful.
        tool_schemas_example = ""
        if self.tools:
            try:
                infobooth_tool = next((t for t in self.tools if t.name == "get_event_infobooth"),
                                      None)
                if infobooth_tool and infobooth_tool.args_schema:
                    schema_fields = (
                        infobooth_tool.args_schema.model_json_schema()['properties'].keys()
                    )

                    # 1. Create the example dictionary
                    example_dict = {k: "..." for k in schema_fields}

                    # 2. Convert to JSON string (uses double quotes)
                    example_json_str = json.dumps(example_dict)
                    # example_json_str is now '{"event_id": "...", "infobooth_id": "..."}'

                    # 3. Escape the braces *in this specific string* for PromptTemplate
                    escaped_example_str = example_json_str.replace("{", "{{").replace("}", "}}")
                    # escaped_example_str is now '{{"event_id": "...", "infobooth_id": "..."}}'

                    # 4. Embed this pre-escaped string into the tool_schemas_example f-string
                    #    No special brace handling needed here anymore for the variable itself.
                    tool_schemas_example = (
                        f"For example, for the '{infobooth_tool.name}' tool, "
                        f"the action_input JSON should look like: {escaped_example_str}"
                    )
                    logger.debug(f"Generated tool schema example: {tool_schemas_example}")

            except Exception as e:
                logger.warning(f"Could not generate tool schema example for prompt: {e}",
                               exc_info=True)

        prompt_template_str = f"""
Respond to the human based on the following context and conversation history.

Context from the event app:
{context_string}

You have access to the following tools:
{{tools}}

Use a json blob to specify a tool by providing an action key (tool name) and an action_input key (tool input).
The input keys ('action_input') for the tool MUST EXACTLY MATCH the arguments required by the tool.
{tool_schemas_example}

Valid "action" values: "Final Answer" or {{tool_names}}

Provide only ONE action per JSON blob, as shown below:

```json
{{{{
  "action": $TOOL_NAME,
  "action_input": $INPUT_JSON_OBJECT
}}}}
```

Follow this exact format STRICTLY for EVERY turn:

Question: the input question you must answer
Thought: Think step-by-step. Analyze the question and available tools. Decide if a tool is needed and which one is most appropriate. Do not call tools unnecessarily because it is expensive to do so. Identify the exact inputs required for the tool based on its description and the question. DO NOT write conversational text here, only your reasoning.
Action:
```json
$JSON_BLOB
```
Observation: the result of the action.
... (this Thought/Action/JSON_BLOB/Observation sequence can repeat multiple times if needed)
Thought: I have gathered enough information and can now formulate the final answer. DO NOT write conversational text here.
Action:
```json
{{{{
  "action": "Final Answer",
  "action_input": "Provide the final, comprehensive answer to the original input question here. Ensure this is a single string value."
}}}}
```

IMPORTANT: Never output conversational text directly. Always use the 'Final Answer' action within the JSON blob structure when you have the answer for the user. Do not deviate from the Thought/Action/Observation format. Every response MUST contain the action and action_input JSON blob.

Begin!

Previous conversation history:
{{chat_history}}

New input:
Question: {{input}}
{{agent_scratchpad}}"""

        logger.debug(
            f"Creating PromptTemplate for structured chat agent. "
            f"Template:\n{prompt_template_str}..."
        )  # Log beginning of prompt
        prompt = ChatPromptTemplate.from_template(prompt_template_str)
        # Expected input variables: tools, tool_names, chat_history, input, agent_scratchpad

        # --- Agent Creation ---
        logger.info("Creating structured chat agent.")
        try:
            agent = create_structured_chat_agent(
                llm=self.llm,
                tools=self.tools,
                prompt=prompt,
            )
        except Exception as e:
            logger.error(f"Failed to create structured chat agent: {e}", exc_info=True)
            return None

        # --- Agent Executor ---
        logger.info("Creating AgentExecutor.")
        try:
            executor = AgentExecutor(
                agent=agent,
                tools=self.tools,
                memory=memory,
                verbose=True,  # Keep verbose=True for debugging
                handle_parsing_errors=True,
                # Keep for now, consider custom handler or False if issues persist
                callbacks=self.callbacks,
                # You might want to limit iterations, especially during testing:
                max_iterations=10,
                # return_intermediate_steps=True # Useful for debugging tool calls
            )
            logger.info("AgentExecutor created successfully.")
            return executor
        except Exception as e:
            logger.error(f"Failed to create AgentExecutor: {e}", exc_info=True)
            return None

    def _get_tool_usage_data(self) -> Optional[List[Dict[str, Any]]]:
        """Get tool usage data from tool tracking handlers

        Returns:
            List of tool invocations or None if no tracking data available
        """
        if not self.tool_trackers:
            return None

        # Use the first tool tracker (we typically only expect one)
        tool_tracker = self.tool_trackers[0]
        tool_usage = tool_tracker.get_tool_usage_report()

        if not tool_usage:
            return None

        return tool_usage

    @aws_exception(LangChainAPIError)
    def generate_text(self, prompt: str, max_tokens: int = 5000,
                      temperature: float = 0.7) -> LLMResponse:
        """
        Generate text response from LLM based on the prompt

        Args:
            prompt: The text prompt to send to the LLM
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness

        Returns:
            LLMResponse object containing the generated text and metadata
        """
        start_time = time.time()

        # Update model parameters for this request
        self.llm.model_kwargs.update({
            "temperature": temperature,
            "max_tokens": max_tokens,
        })

        # If tools are available, and we have an agent executor, use it
        if self.agent_executor:
            logger.info("Using agent executor with tools for generation")

            # 1) Invoke the agent; by default BedrockChat is non‑streaming,
            #    so you'll get back llm_output with usage info.
            logger.debug(f"Invoking text gen agent executor with prompt: {prompt}")
            result = self.agent_executor.invoke({"input": prompt})

            # 2) Extract text
            result_text = result["output"]

            # 3) Pull out token counts from the agent's llm_output
            llm_out = result.get("llm_output", {}) or {}
            usage = llm_out.get("token_usage", {})  # keys: input_tokens, completion_tokens

            input_token_count = usage.get("input_tokens", 0)
            output_token_count = usage.get("completion_tokens", 0)
        else:
            # Generate response using the standard LLM
            logger.info("Using standard LLM for generation (no tools)")
            logger.debug(f"Invoking non-agent text gen with input: {prompt}")
            response = self.llm.invoke(prompt)
            result_text = response.content
            usage = response.response_metadata['usage']
            input_token_count = usage['prompt_tokens']
            output_token_count = usage['completion_tokens']

        # Calculate elapsed time
        elapsed_time = time.time() - start_time

        # Get tool usage data
        tool_usage = self._get_tool_usage_data()

        # Prepare metadata
        metadata = {
            'model_id': self.model_id,
            'langchain_model_id': self.model_config.get("model_id"),
            'elapsed_time': elapsed_time,
            'temperature': temperature,
            'max_tokens': max_tokens,
            'used_tools': bool(self.agent_executor and tool_usage),
            'usage': {
                'input_tokens': input_token_count,
                'output_tokens': output_token_count,
                'total_tokens': input_token_count + output_token_count,
            },
            'cost': self._calculate_cost(input_token_count, output_token_count),
        }

        return LLMResponse(
            text=result_text,
            metadata=metadata,
            tool_usage=tool_usage
        )

    @aws_exception(LangChainAPIError)
    def chat(self,
             conversation_id: UUID,
             messages: List[Dict[str, Any]],
             max_tokens: int = 5000,
             temperature: float = 0.7,
             message_id: Optional[UUID] = None) -> LLMResponse:
        """Process a conversation and generate a response.

        Args:
            conversation_id: The conversation ID
            messages: List of message dictionaries with role and content
            max_tokens: Maximum tokens to generate in response
            temperature: Controls randomness in output generation

        Returns:
            LLMResponse with the generated text and metadata

        Raises:
            LangChainAPIError: On any LangChain or API error
        """
        start_time = time.time()

        # Process messages to find and enhance system message with contexts
        has_system = False
        for i, msg in enumerate(messages):
            if msg.get("role") == "system":
                has_system = True
                # Add context data to the system message if available
                context_string = self.context_builder.build_context_string()
                if context_string:
                    # Only add if there's actual context to add
                    messages[i]["content"] = f"{msg['content']}\n\n{context_string}"
                break

        # If no system message exists, add one with the context
        if not has_system and self.context_providers:
            context_string = self.context_builder.build_context_string()
            if context_string:
                # Insert a new system message at the beginning with context
                messages.insert(0, {
                    "role": "system",
                    "content": f"You are a helpful AI assistant for a conference‑event "
                               f"app.\n\n{context_string}"
                })
                logger.debug(f"Added system message with context: {context_string}")

        # Convert to LangChain format
        langchain_messages: List[BaseMessage] = []
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")

            if role == "system":
                langchain_messages.append(SystemMessage(content=content))
            elif role == "user":
                langchain_messages.append(HumanMessage(content=content))
            elif role == "assistant":
                langchain_messages.append(AIMessage(content=content))
            else:
                # Log warnings for unknown/invalid roles but skip to avoid errors
                logger.warning(f"Unknown message role: {role}")

        # If we have a tool-enabled LLM, use the agent
        if self.tools and self.agent_executor:
            result = self._chat_with_tools(messages)
        else:
            # For regular chat without tools, use the LLM directly
            response = self.llm.invoke(
                langchain_messages,
                max_tokens=max_tokens,
                temperature=temperature,
            )

            result = response.content

        # Calculate time, cost, tokens
        end_time = time.time()
        time_taken = end_time - start_time

        # Get token usage from counter
        token_usage = self._get_token_usage()

        # Cost calculation
        cost = self._calculate_cost(token_usage.get("prompt_tokens", 0),
                                    token_usage.get("completion_tokens", 0))

        # Get tool usage
        tool_usage = self._get_tool_usage_data()

        metadata = {
            "model": str(self.model_id),
            "time_taken": time_taken,
            "token_usage": token_usage,
            "cost": cost,
        }

        if tool_usage:
            metadata["tool_usage"] = tool_usage

        return LLMResponse(
            text=result,
            metadata=metadata
        )

    def chat_stream(
        self,
        conversation_id: UUID,
        messages: List[Dict[str, Any]],
        max_tokens: int = 1000,
        temperature: float = 0.7,
        message_id: Optional[UUID] = None,
    ) -> Iterable[str]:
        """
        Stream chat responses for a conversation

        Args:
            conversation_id: The conversation ID
            messages: List of message dictionaries with role and content
            max_tokens: Maximum tokens to generate in response
            temperature: Controls randomness in output generation

        Returns:
            Iterable generator yielding response chunks as strings
        """
        # Note: Streaming is not implemented in this adapter yet.
        raise NotImplementedError("Streaming chat responses is not supported yet.")

    def _calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Calculate cost based on token usage"""
        try:
            # Convert token counts to integers
            input_count = int(input_tokens)
            output_count = int(output_tokens)

            # Get cost rates for the model
            model_config = BEDROCK_MODEL_CONFIG.get(self.model_id)
            if not model_config:
                return 0.0

            costs = model_config["costs"]
            input_cost = (input_count / 1000) * costs["input_cost_per_1k"]  # type: ignore
            output_cost = (output_count / 1000) * costs["output_cost_per_1k"]  # type: ignore

            return input_cost + output_cost
        except (ValueError, TypeError):
            return 0.0

    def _get_token_usage(self) -> Dict[str, int]:
        """
        Get the token usage from all available sources

        Returns:
            Dictionary with input_tokens, output_tokens, and total_tokens
        """
        # Start with zeros
        input_tokens = 0
        output_tokens = 0

        # First check our token counter callback if available
        if self.token_counter:
            usage = self.token_counter.get_token_usage()
            input_tokens = usage.get("input_tokens", 0)
            output_tokens = usage.get("completion_tokens", 0)

        # If we still don't have any tokens, and we're using llm directly,
        # check if the latest response has usage info
        if input_tokens == 0 and hasattr(self.llm, "last_response"):
            last_resp = self.llm.last_response
            if isinstance(last_resp, dict) and "usage" in last_resp:
                usage = last_resp["usage"]
                input_tokens = usage.get("input_tokens", 0) or usage.get("prompt_tokens", 0)
                output_tokens = usage.get("output_tokens", 0) or usage.get("completion_tokens", 0)

        # Calculate total
        total_tokens = input_tokens + output_tokens

        # Reset token counter for next request if needed
        if self.token_counter:
            self.token_counter.reset()

        return {
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "total_tokens": total_tokens
        }

    def _chat_with_tools(self, messages: List[Dict[str, Any]]) -> str:
        """
        Process chat using tools via agent executor

        Args:
            messages: List of message dictionaries with role and content

        Returns:
            Generated text result
        """
        # Extract the last user message to use as input
        last_user_message = ""
        for msg in reversed(messages):
            if msg.get('role', '').lower() == 'user':
                last_user_message = msg.get('content', '')
                break

        if not last_user_message:
            logger.warning("No user message found in chat history")
            return "I couldn't find a user message to respond to."

        # Get a fresh agent_executor for this request
        agent_executor = self.agent_executor

        # Format conversation history for the agent
        chat_history = ""
        for i in range(len(messages) - 1):  # Process all but the last message
            msg = messages[i]
            role = msg.get('role', '').lower()
            content = msg.get('content', '')

            # Skip system messages and the last user message (which will be the input)
            if role == 'system' or (role == 'user' and content == last_user_message):
                continue

            if role == 'user':
                chat_history += f"Question: {content}\n"
                # If there's a following assistant message, add it as a Final Answer
                if i + 1 < len(messages) and messages[i + 1].get('role',
                                                                 '').lower() == 'assistant':
                    assistant_resp = messages[i + 1].get('content', '')
                    chat_history += f"Final Answer: {assistant_resp}\n\n"

        # Save the chat history to memory if not empty
        if chat_history.strip():
            logger.debug(f"Chat history saved to memory: {chat_history.strip()}")
            agent_executor.memory.save_context({"input": ""}, {"output": chat_history.strip()})

        try:
            # Invoke agent with the last user message
            logger.debug(f"Invoking chat agent with user message: {last_user_message}")
            response = agent_executor.invoke({"input": last_user_message})

            # Extract output text from response
            if isinstance(response, dict) and "output" in response:
                return response["output"]
            return str(response)

        except Exception as e:
            logger.error(f"Agent execution failed: {str(e)}")
            raise e


def get_llm_adapter(
    model_id: BedrockModelID = BedrockModelID.CLAUDE_3_5_HAIKU,
    tools: Optional[List[BaseTool]] = None,
    callbacks: Optional[List[BaseCallbackHandler]] = None,
    context_providers: Optional[List[ContextProvider]] = None
) -> LLMAdapter:
    """
    Factory function to get an LLM service instance

    Args:
        model_id: The Bedrock model ID to use
        tools: Optional list of LangChain tools to use with the model
        callbacks: Optional list of callback handlers to track execution
        context_providers: Optional list of context providers to inject data into prompts

    Returns:
        LLMAdapter instance
    """
    return LangChainBedrockAdapter(model_id=model_id, tools=tools, callbacks=callbacks,
                                   context_providers=context_providers)
