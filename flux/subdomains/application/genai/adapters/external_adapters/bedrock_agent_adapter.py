"""
Adapter for AWS Bedrock Agent
"""
import time
import uuid
from typing import Dict, List, Optional, Any
from uuid import UUID
from flask import request, has_request_context

import boto3

from flux.adapters.logging import get_logger
from flux.apps.config import app_config
from flux.subdomains.application.genai.entities.context_providers import (
    Context<PERSON><PERSON><PERSON>,
    ContextBuilder,
)
from flux.subdomains.application.genai.interfaces.llm_adapter import LLMAdapter, LLMResponse

logger = get_logger(__name__)

QUERY_TEMPLATE = """
Context For the User and Event:
{context}

Additional User Session Attributes:
{session_attributes}

User Query:
{query}
"""


BEDROCK_MODEL_CONFIG = {
    'anthropic.claude-3-5-haiku-20241022-v1:0': {
        "costs": {
            "input_cost_per_1k": 0.0008,  # Cost per 1K input tokens
            "output_cost_per_1k": 0.004,  # Cost per 1K output tokens
        },
    },
    'anthropic.claude-3-sonnet-20240229-v1:0': {
        "costs": {
            "input_cost_per_1k": 0.003,  # Cost per 1K input tokens
            "output_cost_per_1k": 0.015,  # Cost per 1K output tokens
        },
    }
}


class BedrockAgentLLMAdapter(LLMAdapter):
    """
    Adapter for AWS Bedrock Agent

    This is a stub implementation that will be fleshed out later.
    """

    def __init__(self, event_id, agent_type, agent_id, agent_alias_id,
                 context_providers: Optional[List[ContextProvider]] = None):
        """
        Initialize the Bedrock Agent adapter

        Args:
            event_id: The event ID for the current request
            agent_type: The type of invocation (e.g., "agent", "flow")
            agent_id: The ID of the specific agent or flow
            agent_alias_id: The invocation alias ID for the current request
        """
        self.event_id = event_id
        self.agent_type = agent_type
        self.agent_id = agent_id
        self.agent_alias_id = agent_alias_id

        self._client = self._get_bedrock_client()

        # Set up context providers
        logger.debug(
            f"Setting up context providers for Bedrock Agent adapter. {context_providers}"
        )
        self.context_providers = context_providers or []
        self.context_builder = ContextBuilder()
        for provider in self.context_providers:
            self.context_builder.add_provider(provider)

    @staticmethod
    def _get_bedrock_client():
        """
        Get or create the Bedrock client

        Returns:
            Bedrock client instance
        """

        # This code is commented out as in the original for now
        if app_config.is_dev_environment_and_dev_aws():
            client = boto3.client(
                'bedrock-agent-runtime',
                region_name=app_config.AWS_REGION_NAME,
                endpoint_url=app_config.AWS_DEV_ENDPOINT,
            )
        else:
            client = boto3.client(
                service_name='bedrock-agent-runtime',
                region_name=app_config.AWS_REGION_NAME,
            )
        return client

    @property
    def model_id(self):
        return f'{self.agent_type}:{self.agent_id}:{self.agent_alias_id}'

    def generate_text(self, prompt: str, max_tokens: int = 1000,
                      temperature: float = 0.7) -> LLMResponse:
        """
        Generate text response from the Bedrock Agent based on the prompt

        Args:
            prompt: The text prompt to send to the LLM
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness

        Returns:
            LLMResponse object containing the generated text and metadata
        """

        session_id = uuid.uuid4()

        logger.info(f"Invoking Bedrock Agent text generation with prompt: {prompt}")
        try:
            response_text, metadata = self._invoke_bedrock_agent(
                input_text=prompt,
                history=[],
                session_id=str(session_id),
            )
        except Exception as e:
            logger.error(f"Failed to invoke Bedrock Agent: {e}", exc_info=True)
            return LLMResponse(text="Error invoking Bedrock Agent", metadata={})

        return LLMResponse(text=response_text, metadata=metadata)

    def chat(self, conversation_id: UUID, messages: List[Dict[str, Any]], max_tokens: int = 1000,
             temperature: float = 0.7, message_id: Optional[UUID] = None) -> LLMResponse:
        """
        Generate response based on a conversation history using Bedrock Agent

        Args:
            conversation_id: The conversation ID
            messages: List of message dictionaries with 'role' and 'content' keys
                     roles should be 'system', 'user', or 'assistant'
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness

        Returns:
            LLMResponse object containing the generated text and metadata
        """
        input_text = messages.pop(-1).get('content')
        if not input_text:
            logger.error("No user message found in chat history")
            raise Exception("No user message found in chat history")

        logger.info(f"Invoking Bedrock Agent chat with user message: {input_text}")
        try:
            response_text, metadata = self._invoke_bedrock_agent(
                input_text=input_text,
                history=messages,
                session_id=str(conversation_id),
                message_id=message_id,
            )
        except Exception as e:
            logger.error(f"Failed to invoke Bedrock Agent: {e}", exc_info=True)
            return LLMResponse(text="Error invoking Bedrock Agent", metadata={})

        return LLMResponse(text=response_text, metadata=metadata)

    def chat_stream(
        self,
        conversation_id: UUID,
        messages: List[Dict[str, Any]],
        max_tokens: int = 1000,
        temperature: float = 0.7,
        message_id: Optional[UUID] = None,
    ):
        """Yield response chunks as they are received from Bedrock"""
        input_text = messages.pop(-1).get('content')
        if not input_text:
            logger.error("No user message found in chat history")
            raise Exception("No user message found in chat history")

        logger.info(f"Streaming Bedrock Agent chat with user message: {input_text}")
        try:
            yield from self._invoke_bedrock_agent_stream(
                input_text=input_text,
                history=messages,
                session_id=str(conversation_id),
                message_id=message_id,
            )
        except Exception as e:
            logger.error(f"Failed to invoke Bedrock Agent: {e}", exc_info=True)
            yield "Error invoking Bedrock Agent"

    def _invoke_bedrock_agent(self, input_text: str, history: List[Dict[str, str]],
                              session_id: str = None, message_id: Optional[UUID] = None):
        """
        Invoke the Bedrock Agent with the given input text and session ID

        Args:
            input_text: The text to send to the agent
            session_id: The session ID for the current conversation

        Returns:
            The response text from the agent
        """

        # Set-up session attributes
        session_attributes = {
            'event_id': str(self.event_id),
        }
        if message_id:
            session_attributes['message_id'] = str(message_id)

        # Get cookie from flask request (Messy)
        _login_cookies = []
        if has_request_context():
            _login_cookies = [
                f'{c}={v}' for c, v in request.cookies.items() if c.endswith('em-login')
            ]
            if _login_cookies:
                logger.info(f"Found cookie: {_login_cookies[0]}")
                session_attributes['user_cookie'] = _login_cookies[0]

        logger.info(f"session attributes: {session_attributes}")

        context = ""
        if hasattr(self, 'context_builder'):
            context = self.context_builder.build_context_string()
            logger.debug(f"Context: {context}")

        # Build the prompt
        prompt = QUERY_TEMPLATE.format(
            context=context,
            session_attributes=session_attributes,
            query=input_text,
        )
        logger.debug(f"Bedrock Agent prompt: {prompt}")

        start_time = time.time()

        # Prepare invoke parameters
        invoke_params = {
            'agentId': self.agent_id,
            'agentAliasId': self.agent_alias_id,
            'sessionId': session_id,
            'inputText': prompt,
            'enableTrace': True  # Set to True to get detailed trace information
        }

        # Add session state if history is provided
        if history:
            invoke_params['sessionState'] = {
                'conversationHistory': {
                    'messages': self._format_history_messages(history)
                }
            }

        # Invoke the agent
        response = self._client.invoke_agent(**invoke_params)

        response_text = 'Error receiving response from agent.'
        input_tokens = output_tokens = model_id = None
        for r in response['completion']:
            # For now, log out all trace information
            logger.info(f'Bedrock Agent response trace: {str(r)}')

            # Extract the response content
            if 'chunk' in r and 'bytes' in r['chunk']:
                # The response is in the bytes field of the chunk object
                response_text = r['chunk']['bytes'].decode('utf-8')

            # Attempt to get token usage from trace
            if not input_tokens:
                input_tokens, output_tokens = self._get_token_usage(r)

            # Attempt to get model ID from trace
            if not model_id:
                model_id = self._get_model_id(r)

        # get invocation time in milliseconds
        elapsed_time = int((time.time() - start_time) * 1000)

        metadata = {
            "model_id": model_id,
            "elapsed_time": elapsed_time,
            'usage': {
                'input_tokens': input_tokens,
                'output_tokens': output_tokens,
            },
            "cost": self._calculate_cost(model_id, input_tokens, output_tokens),
        }

        return response_text, metadata

    def _invoke_bedrock_agent_stream(
        self,
        input_text: str,
        history: List[Dict[str, str]],
        session_id: str | None = None,
        message_id: Optional[UUID] = None,
    ):
        """Stream response chunks from the Bedrock Agent"""

        session_attributes = {
            'event_id': str(self.event_id),
        }
        if message_id:
            session_attributes['message_id'] = str(message_id)

        _login_cookies = []
        if has_request_context():
            _login_cookies = [
                f'{c}={v}' for c, v in request.cookies.items() if c.endswith('em-login')
            ]
            if _login_cookies:
                session_attributes['user_cookie'] = _login_cookies[0]

        context = ""
        if hasattr(self, 'context_builder'):
            context = self.context_builder.build_context_string()

        prompt = QUERY_TEMPLATE.format(
            context=context,
            session_attributes=session_attributes,
            query=input_text,
        )

        invoke_params = {
            'agentId': self.agent_id,
            'agentAliasId': self.agent_alias_id,
            'sessionId': session_id,
            'inputText': prompt,
            'enableTrace': True,
            'streamingConfigurations': {
                'streamFinalResponse': True,
            },
        }

        if history:
            invoke_params['sessionState'] = {
                'conversationHistory': {
                    'messages': self._format_history_messages(history)
                }
            }

        start_time = time.time()
        first_byte_time = None
        response = self._client.invoke_agent(**invoke_params)

        total_input_tokens = total_output_tokens = 0
        model_id = None
        for r in response['completion']:
            if 'chunk' not in r:
                logger.info(f'Bedrock Agent (stream) response trace: {str(r)}')
                # Attempt to get token usage from trace
                input_tokens, output_tokens = self._get_token_usage(r)
                if input_tokens:
                    total_input_tokens += input_tokens
                if output_tokens:
                    total_output_tokens += output_tokens
                if not model_id:
                    model_id = self._get_model_id(r)

            if 'chunk' in r and 'bytes' in r['chunk']:
                if first_byte_time is None:
                    first_byte_time = int((time.time() - start_time) * 1000)
                yield r['chunk']['bytes'].decode('utf-8')

        elapsed_time = int((time.time() - start_time) * 1000)

        metadata = {
            "model_id": model_id,
            "elapsed_time": elapsed_time,
            "first_byte_time": first_byte_time,
            'usage': {
                'input_tokens': total_input_tokens,
                'output_tokens': total_output_tokens,
            },
            "cost": self._calculate_cost(model_id, total_input_tokens, total_output_tokens),
        }
        logger.info(f"Bedrock Agent stream completed. Metadata: {metadata}")

        # Return the final metadata as a single chunk
        yield metadata

    @staticmethod
    def _get_token_usage(data):
        try:
            usage = (
                data.get('trace', {})
                .get('trace', {})
                .get('orchestrationTrace', {})
                .get('modelInvocationOutput', {})
                .get('metadata', {})
                .get('usage', {})
            )
            input_tokens = usage.get('inputTokens')
            output_tokens = usage.get('outputTokens')
            return input_tokens, output_tokens
        except Exception:
            return None, None

    def _get_model_id(self, data):
        try:
            model = (
                data.get('trace', {})
                .get('trace', {})
                .get('orchestrationTrace', {})
                .get('modelInvocationInput', {})
                .get('foundationModel')
            )
            return model or self.model_id
        except Exception:
            return self.model_id

    @staticmethod
    def _calculate_cost(model_id: str | None, input_tokens: int | None, output_tokens: int | None
                        ) -> float | None:
        """Calculate cost based on token usage"""
        if model_id is None or input_tokens is None or output_tokens is None:
            return None

        try:
            # Convert token counts to integers
            input_count = int(input_tokens)
            output_count = int(output_tokens)

            # Get cost rates for the model
            model_config = BEDROCK_MODEL_CONFIG.get(model_id)
            if not model_config:
                return None

            costs = model_config["costs"]
            input_cost = (input_count / 1000) * costs["input_cost_per_1k"]  # type: ignore
            output_cost = (output_count / 1000) * costs["output_cost_per_1k"]  # type: ignore

            return input_cost + output_cost
        except (ValueError, TypeError):
            return None

    @staticmethod
    def _format_history_messages(history) -> List[Dict[str, Any]]:
        """Format the conversation history for Bedrock Agent

        Expected format for Bedrock is:
            'messages': [
                {
                    'content': [
                        {
                            'text': 'string'
                        },
                    ],
                    'role': 'user'|'assistant'
                },
            ]
        """
        messages = []
        for msg in history:
            messages.append({
                'content': [
                    {
                        'text': msg['content']
                    },
                ],
                'role': 'user' if msg['role'] == 'user' else 'assistant'
            })
        logger.debug(f"Formatted history messages: {messages}")
        return messages


def get_llm_adapter(event_id, agent_type, agent_id, agent_alias_id) -> BedrockAgentLLMAdapter:
    """
    Factory function to get an LLM service instance for Bedrock Agent

    Returns:
        BedrockAgentLLMAdapter instance
    """
    return BedrockAgentLLMAdapter(event_id, agent_type, agent_id, agent_alias_id)
