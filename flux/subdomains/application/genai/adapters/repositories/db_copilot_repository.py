"""
Repository implementation for the Attendee Copilot feature
"""
from typing import List, Optional
from uuid import UUID
from datetime import datetime, timezone

from flux.domains.common.pagination import PaginationInfo
from flux.subdomains.application.genai.entities.copilot_entities import Conversation
from flux.subdomains.application.genai.entities.copilot_entities import Message
from flux.subdomains.application.genai.adapters.repositories.models import (
    ConversationModel,
    MessageModel,
    MessageReferencedEntityModel,
)
from flux.subdomains.application.genai.interfaces.copilot_repository import CopilotRepository
from flux.utils import timeutils


class CopilotRepositoryImpl(CopilotRepository):
    """
    Repository implementation for managing Copilot conversations and messages
    """

    def __init__(self):
        self.conversation_model = ConversationModel
        self.message_model = MessageModel
        self.ref_entity_model = MessageReferencedEntityModel

    def get_conversation(self, conversation_id: UUID, event_id: Optional[int] = None,
                         user_id: Optional[int] = None) -> Optional[Conversation]:
        """Get a conversation by ID with optional event and user filters"""
        model = self.conversation_model.get_by_id(conversation_id, event_id, user_id)
        if not model:
            return None
        return model.as_entity(Conversation)

    def get_conversations(
        self, event_id: int, user_id: int, page_info: Optional[PaginationInfo] = None
    ) -> List[Conversation]:
        """Get all conversations for a user in an event"""
        models = self.conversation_model.get_by_user_id(event_id, user_id, page_info=page_info)
        return [model.as_entity(Conversation) for model in models]

    def create_conversation(self, id: UUID, event_id: int, user_id: int,
                            title: Optional[str] = None) -> Conversation:
        """Create a new conversation"""
        now = datetime.now(timezone.utc)

        model = self.conversation_model(
            id=id,
            event_id=event_id,
            user_id=user_id,
            title=title,
            created_at=now,
            updated_at=now
        )
        self.conversation_model.save(model)
        return model.as_entity(Conversation)

    def update_conversation_title(self, conversation_id: UUID, title: str
                                  ) -> Optional[Conversation]:
        """Update a conversation's title"""
        model = self.conversation_model.get_by_id(conversation_id)
        if not model:
            raise ValueError(f"Conversation {conversation_id} not found")
        model.title = title
        self.conversation_model.save(model)
        return model.as_entity(Conversation)

    def add_message(self, conversation_id: UUID, role: str, content: str,
                    metadata: Optional[dict] = None, id: Optional[UUID] = None
                    ) -> Optional[Message]:
        """Add a message to a conversation"""
        now = datetime.now(timezone.utc)

        model = self.message_model(
            id=id,
            conversation_id=conversation_id,
            role=role,
            content=content,
            message_metadata=metadata,
            created_at=now
        )
        self.message_model.save(model)
        return model.as_entity()

    def add_referenced_entities(self, message_id: UUID, referenced_entities: List[dict]):
        """Persist referenced entities for a message"""
        saved = []
        for item in referenced_entities:
            etype = item.get("entity_type")
            ids = item.get("entity_ids", [])
            if not etype or not ids:
                continue
            for eid in ids:
                model = self.ref_entity_model(
                    message_id=message_id,
                    entity_type=etype,
                    entity_id=eid,
                    created_at=timeutils.now(),
                )
                try:
                    self.ref_entity_model.save(model)
                    saved.append(model)
                except Exception:
                    # ignore duplicates or other errors
                    pass

    def get_messages(self, conversation_id: UUID, page_info: Optional[PaginationInfo] = None
                     ) -> List[Message]:
        """
        Get messages for a conversation with pagination support

        Args:
            conversation_id: The conversation ID
            page_info: Optional pagination information (includes sorting)

        Returns:
            List of messages with pagination
        """
        # Get messages with pagination
        models = self.message_model.get_by_conversation(
            conversation_id=conversation_id,
            page_info=page_info
        )

        messages = [model.as_entity() for model in models]
        msg_ids = [m.id for m in messages]
        ref_map = self.get_referenced_entities(msg_ids)
        for m in messages:
            m.referenced_entities = ref_map.get(m.id, [])
        return messages

    def get_message(self, conversation_id: UUID, message_id: UUID) -> Optional[Message]:
        """Retrieve a single message by id including referenced entities"""
        try:
            model = self.message_model.get_by_id(message_id, conversation_id=conversation_id)
        except Exception:
            return None

        message = model.as_entity()
        refs = self.get_referenced_entities([message.id])
        message.referenced_entities = refs.get(message.id, [])
        return message

    def get_referenced_entities(self, message_ids: List[UUID]) -> dict:
        if not message_ids:
            return {}
        rows = self.ref_entity_model.query.filter(
            self.ref_entity_model.message_id.in_(message_ids)
        ).all()
        data: dict[UUID, dict[str, List[str]]] = {}
        for row in rows:
            data.setdefault(
                row.message_id, {}
            ).setdefault(
                row.entity_type, []
            ).append(str(row.entity_id))
        return {
            mid: [
                {"entity_type": et, "entity_ids": ids}
                for et, ids in groups.items()
            ]
            for mid, groups in data.items()
        }

    def delete_conversation(self, conversation_id: UUID) -> bool:
        """Delete a conversation and all its messages"""
        model = self.conversation_model.get_by_id(conversation_id)
        if not model:
            return False
        self.conversation_model.delete(model)
        return True
