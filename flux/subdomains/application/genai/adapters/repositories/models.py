"""
SQLAlchemy models for the Attendee Copilot feature
"""

from sqlalchemy import and_

from flux.adapters.db import db, UUID, EntityModel, EntityModelNoUpdates

from flux.subdomains.application.genai.entities.copilot_entities import Message


class MessageModel(EntityModel):
    """
    SQLAlchemy model for Messages
    """
    __tablename__ = 'copilot_messages'

    _sortable_fields = {
        'created_at': None,
        'role': None,
    }

    # Column definitions
    conversation_id = db.Column(
        UUID,
        db.ForeignKey('copilot_conversations.id', ondelete='CASCADE'),
        nullable=False,
    )
    role = db.Column(db.VARCHAR(50), nullable=False, default='user')  # 'user' or 'assistant'
    content = db.Column(db.TEXT, nullable=False)
    message_metadata = db.Column(db.JSON, nullable=True)

    @classmethod
    def get_by_conversation(cls, conversation_id, page_info=None):
        """
        Get messages for a conversation with pagination support

        Args:
            conversation_id: The conversation ID
            page_info: Pagination information (includes sorting)

        Returns:
            List of message models
        """
        query = cls.query.filter(cls.conversation_id == conversation_id)

        q = cls._sort_and_paginate(
            query=query,
            page_info=page_info,
            default_sort=cls.created_at
        )

        return q

    def as_entity(self):
        """Convert the model to a domain entity"""

        return Message(
            id=self.id,
            conversation_id=self.conversation_id,
            role=self.role,
            content=self.content,
            created_at=self.created_at,
            metadata=self.message_metadata or {}
        )


class MessageReferencedEntityModel(EntityModelNoUpdates):
    """SQLAlchemy model for referenced entities linked to a message"""

    __tablename__ = 'copilot_message_referenced_entities'
    __table_args__ = (
        db.UniqueConstraint('message_id', 'entity_type', 'entity_id',
                            name='uix_copilot_msg_ref_entities'),
    )

    message_id = db.Column(UUID, nullable=False, index=True)
    entity_type = db.Column(db.VARCHAR(50), nullable=False)
    entity_id = db.Column(UUID, nullable=False)


class ConversationModel(EntityModel):
    """
    SQLAlchemy model for Conversations
    """
    __tablename__ = 'copilot_conversations'

    _searchable_fields = {
        'title': None,
    }
    _sortable_fields = {
        'title': None,
        'created_at': None,
        'updated_at': None,
    }

    # Column definitions
    event_id = db.Column(db.INTEGER, nullable=False)
    user_id = db.Column(db.INTEGER, nullable=False)
    title = db.Column(db.VARCHAR(255), nullable=True)

    # Relationships
    messages = db.relationship(
        MessageModel,
        uselist=True,
        lazy='select',
        cascade='all, delete-orphan',
        backref='conversation',
        order_by=MessageModel.created_at,
    )

    @classmethod
    def get_by_user_id(cls, event_id, user_id, page_info=None):
        """Get all conversations for a user in an event"""
        query = cls.query.filter(
            and_(
                cls.event_id == event_id,
                cls.user_id == user_id
            )
        )
        q = cls._sort_and_paginate(
            query=query,
            page_info=page_info,
            default_sort=cls.created_at
        )

        return q

    @classmethod
    def get_by_id(cls, conversation_id, event_id=None, user_id=None):
        """Get a conversation by ID with optional event and user filters"""
        filters = [cls.id == conversation_id]
        if event_id is not None:
            filters.append(cls.event_id == event_id)
        if user_id is not None:
            filters.append(cls.user_id == user_id)
        return cls.query.filter(and_(*filters)).first()

    def as_entity(self, conversation_entity_cls):
        """Convert the model to a domain entity"""
        return conversation_entity_cls(
            id=self.id,
            event_id=self.event_id,
            user_id=self.user_id,
            title=self.title,
            created_at=self.created_at,
            updated_at=self.updated_at,
            messages=[msg.as_entity() for msg in self.messages]
        )
