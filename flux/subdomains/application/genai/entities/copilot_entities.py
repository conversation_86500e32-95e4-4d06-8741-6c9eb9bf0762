"""
Entities for the Attendee Copilot feature
"""
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
import uuid
from flux.utils import timeutils
from uuid import UUID


class QueryIntent(str, Enum):
    """Enum for different types of user query intents"""
    SESSION_SEARCH = "SESSION_SEARCH"
    SCHEDULE_VIEW = "SCHEDULE_VIEW"
    SCHEDULE_ADD = "SCHEDULE_ADD"
    SCHEDULE_REMOVE = "SCHEDULE_REMOVE"
    GENERAL_QUESTION = "GENERAL_QUESTION"
    UNKNOWN = "UNKNOWN"


@dataclass
class Query:
    """Represents a processed user query with identified intent and parameters"""
    raw_text: str
    intent: QueryIntent = QueryIntent.UNKNOWN
    parameters: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the query to a dictionary representation"""
        return {
            "raw_text": self.raw_text,
            "intent": str(self.intent),
            "parameters": self.parameters
        }


@dataclass
class SuggestedAction:
    """Represents a suggested action for the user"""
    action_type: str
    label: str
    payload: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the suggested action to a dictionary representation"""
        return {
            "action_type": self.action_type,
            "label": self.label,
            "payload": self.payload
        }


@dataclass
class ReferencedEntity:
    """Represents an entity referenced in a response"""
    entity_type: str
    entity_id: str
    display_name: str
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the referenced entity to a dictionary representation"""
        return {
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "display_name": self.display_name,
            "metadata": self.metadata
        }


@dataclass
class Response:
    """Represents a structured response before formatting for delivery"""
    content: str
    referenced_entities: List[ReferencedEntity] = field(default_factory=list)
    suggested_actions: List[SuggestedAction] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the response to a dictionary representation"""
        return {
            "content": self.content,
            "referenced_entities": [entity.to_dict() for entity in self.referenced_entities],
            "suggested_actions": [action.to_dict() for action in self.suggested_actions]
        }


@dataclass
class Message:
    """Represents an individual message in a conversation"""
    id: UUID = field(default_factory=uuid.uuid4)
    conversation_id: Optional[UUID] = None
    role: str = "user"  # "user" or "assistant"
    content: str = ""
    created_at: datetime = field(default_factory=timeutils.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    referenced_entities: List[Dict[str, Any]] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the message to a dictionary representation"""
        return {
            "id": self.id,
            "conversation_id": self.conversation_id,
            "role": self.role,
            "content": self.content,
            "created_at": (
                self.created_at.isoformat()
                if isinstance(self.created_at, datetime) else self.created_at
            ),
            "metadata": self.metadata,
            "referenced_entities": self.referenced_entities,
        }


@dataclass
class Conversation:
    """Represents a complete conversation session between an attendee and the AI assistant"""
    id: UUID = field(default_factory=uuid.uuid4)
    event_id: Optional[int] = None
    user_id: Optional[int] = None
    title: Optional[str] = None
    created_at: datetime = field(default_factory=timeutils.now)
    updated_at: datetime = field(default_factory=timeutils.now)
    messages: List[Message] = field(default_factory=list)

    @property
    def message_count(self) -> int:
        """Get the number of messages in the conversation"""
        return len(self.messages)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the conversation to a dictionary representation"""
        return {
            "id": self.id,
            "event_id": self.event_id,
            "user_id": self.user_id,
            "title": self.title,
            "created_at": (
                self.created_at.isoformat()
                if isinstance(self.created_at, datetime) else self.created_at
            ),
            "updated_at": (
                self.updated_at.isoformat()
                if isinstance(self.updated_at, datetime) else self.updated_at
            ),
            "message_count": self.message_count,
            "messages": [message.to_dict() for message in self.messages] if self.messages else []
        }
