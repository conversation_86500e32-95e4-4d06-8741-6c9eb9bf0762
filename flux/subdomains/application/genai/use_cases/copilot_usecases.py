"""
Use cases for the Copilot feature
"""
import uuid
from typing import List, Optional, Dict, Any, Tuple, Iterable
from uuid import UUID

from flux.domains.common.pagination import PaginationInfo
from flux.subdomains.application.genai.entities.copilot_entities import (
    Conversation,
    Message,
    Query,
    QueryIntent,
)
from flux.subdomains.application.genai.interfaces.copilot_repository import CopilotRepository
from flux.subdomains.application.genai.services.copilot_service import CopilotService
from flux.utils.timeutils import now
from flux.domains.common.errors import (
    NotFoundError,
    InvalidOperationError,
)
from flux.domains.common.error_codes import ErrorCodes


class ConversationNotFoundError(NotFoundError):
    """Raised when a conversation is not found"""
    entity = 'conversation'
    message = 'Conversation not found.'


class EmptyMessageError(InvalidOperationError):
    """Raised when an empty message is provided"""
    code = ErrorCodes.invalid_value
    message = 'Content cannot be empty.'


class CopilotUseCase:
    """
    Use case for handling Copilot interactions
    """
    def __init__(
        self,
        context,
        repository: CopilotRepository,
        copilot_service: CopilotService,
    ):
        self.context = context
        self.repository = repository
        self.copilot_service = copilot_service

    def handle_chat_message(
        self,
        event_id: int,
        user_id: int,
        content: Optional[str] = None,
        conversation_id: Optional[UUID] = None,
    ) -> Dict[str, Any]:
        """
        Handle a chat message from a user

        Args:
            event_id: The event ID
            user_id: The user ID
            content: The message content (optional if creating an empty conversation)
            conversation_id: Optional conversation ID to continue

        Returns:
            Dictionary containing conversation_id, content, and other response data

        Raises:
            ConversationNotFoundError: If the specified conversation doesn't exist
            EmptyMessageError: If message is empty or invalid when content is provided
        """
        # If no content and no conversation_id, create an empty conversation
        create_empty = content is None and conversation_id is None

        # Only validate content if it's provided and we're not creating an empty conversation
        if not create_empty and (not content or not content.strip()):
            raise EmptyMessageError()

        # Get or create conversation
        conversation = None
        is_new_conversation = False
        previous_messages = []

        if conversation_id:
            conversation = self.repository.get_conversation(conversation_id, event_id, user_id)
            if not conversation:
                raise ConversationNotFoundError()

            # Get conversation history for context
            previous_messages = self.repository.get_messages(conversation.id)

        if not conversation:
            coversation_id = uuid.uuid4()  # Generate a new UUID for the conversation
            conversation = self.repository.create_conversation(coversation_id, event_id, user_id)
            is_new_conversation = True
            # Initialize empty messages list
            conversation.messages = []

        if not content:
            return {
                "title": None,
                "conversation_id": conversation.id,
                "conversation_created_at": conversation.created_at.isoformat(),
                "content": None,
                "message_id": None,
                "message_created_at": None,
                "referenced_entities": [],
                "suggested_actions": []
            }

        # Create query
        query = Query(
            raw_text=content,
            intent=QueryIntent.GENERAL_QUESTION,
            parameters={"timestamp": now().isoformat()}
        )

        # Add user message
        user_message = self.repository.add_message(
            conversation_id=conversation.id,
            role="user",
            content=content,
            metadata={"query": query.to_dict()},
        )

        if user_message:
            conversation.messages.append(user_message)

        ai_message_id = uuid.uuid4()

        # Get AI response - pass conversation history
        response = self.copilot_service.process_query(
            query,
            event_id,
            user_id,
            conversation.id,
            previous_messages,
            message_id=ai_message_id,
        )

        # Add AI message
        ai_message = self.repository.add_message(
            conversation_id=conversation.id,
            role="assistant",
            content=response.content,
            metadata={"response": response.to_dict()},
            id=ai_message_id,
        )

        if ai_message:
            conversation.messages.append(ai_message)

        # Generate title if this is a new conversation
        if is_new_conversation or not conversation.title:
            title = self._generate_title(content)
            self.repository.update_conversation_title(conversation.id, title)

        # Format response for the controller
        return {
            "title": title if is_new_conversation else conversation.title,
            "conversation_id": conversation.id,
            "conversation_created_at": conversation.created_at.isoformat(),
            "content": response.content,
            "message_id": ai_message.id if ai_message else None,
            "message_created_at": ai_message.created_at.isoformat() if ai_message else None,
            "suggested_actions": response.suggested_actions,
            "referenced_entities": response.referenced_entities
        }

    def handle_chat_message_stream(
        self,
        event_id: int,
        user_id: int,
        content: Optional[str] = None,
        conversation_id: Optional[UUID] = None,
    ) -> Tuple[UUID, Optional[UUID], "Iterable[str]"]:
        """Stream an AI response for a chat message"""

        create_empty = content is None and conversation_id is None
        if not create_empty and (not content or not content.strip()):
            raise EmptyMessageError()

        conversation = None
        is_new_conversation = False
        previous_messages = []

        if conversation_id:
            conversation = self.repository.get_conversation(conversation_id, event_id, user_id)
            if not conversation:
                raise ConversationNotFoundError()
            previous_messages = self.repository.get_messages(conversation.id)

        if not conversation:
            conversation = Conversation(
                event_id=event_id,
                user_id=user_id,
                title=None,
                messages=[],
            )
            is_new_conversation = True

        if not content:
            # If no content, create the conversation (empty) if new and return empty stream
            if is_new_conversation:
                self.repository.create_conversation(conversation.id, event_id, user_id, title=None)
            return conversation.id, None, iter([])

        query = Query(
            raw_text=content,
            intent=QueryIntent.GENERAL_QUESTION,
            parameters={"timestamp": now().isoformat()},
        )

        user_message = Message(
            conversation_id=conversation.id,
            role="user",
            content=content,
            metadata={"intent": query.intent.value},
        )

        ai_message_id = uuid.uuid4()

        raw_stream = self.copilot_service.process_query_stream(
            query,
            event_id,
            user_id,
            conversation.id,
            previous_messages,
            message_id=ai_message_id,
        )

        def stream_wrapper():
            chunks: List[str] = []
            metadata = {}
            for part in raw_stream:
                if isinstance(part, dict):
                    metadata = part
                    break
                chunks.append(part)
                yield part

            final_text = "".join(chunks)
            ai_message = Message(
                conversation_id=conversation.id,
                role="assistant",
                content=final_text,
                metadata=metadata,
                id=ai_message_id,
            )

            title = None
            if is_new_conversation or not conversation.title:
                title = self._generate_title(content)

            # Now persist everything at once if new conversation
            if is_new_conversation:
                self.repository.create_conversation(conversation.id, event_id, user_id,
                                                    title=title)
            else:
                if title and (not conversation.title):
                    self.repository.update_conversation_title(conversation.id, title)

            # Persist user message
            self.repository.add_message(
                conversation_id=conversation.id,
                role=user_message.role,
                content=user_message.content,
                metadata=user_message.metadata,
            )

            # Persist AI message
            self.repository.add_message(
                conversation_id=conversation.id,
                role=ai_message.role,
                content=ai_message.content,
                metadata=ai_message.metadata,
                id=ai_message_id,
            )

        return conversation.id, ai_message_id, stream_wrapper()

    def get_conversation(self, conversation_id: UUID, event_id: int, user_id: int
                         ) -> Optional[Conversation]:
        """
        Get a conversation by ID

        Args:
            conversation_id: The conversation ID
            event_id: The event ID
            user_id: The user ID

        Returns:
            Conversation if found, None otherwise
        """
        conversation = self.repository.get_conversation(conversation_id, event_id, user_id)
        if not conversation:
            return None

        # Load messages (without pagination for full conversation)
        messages = self.repository.get_messages(conversation.id)
        conversation.messages = messages

        return conversation

    def get_conversations(
        self, event_id: int, user_id: int, page_info: Optional[PaginationInfo] = None
    ) -> List[Conversation]:
        """
        Get all conversations for a user in an event

        Args:
            event_id: The event ID
            user_id: The user ID

        Returns:
            List of conversations
        """
        return self.repository.get_conversations(event_id, user_id, page_info)

    def get_messages(self, conversation_id: UUID, page_info: Optional[PaginationInfo] = None
                     ) -> List[Message]:
        """
        Get messages for a conversation with pagination support

        Args:
            conversation_id: The conversation ID
            page_info: Optional pagination information for limiting and sorting results

        Returns:
            List of messages with pagination
        """
        return self.repository.get_messages(conversation_id, page_info)

    def get_message(self, conversation_id: UUID, message_id: UUID) -> Optional[Message]:
        """Retrieve a single message"""
        return self.repository.get_message(conversation_id, message_id)

    def add_referenced_entities(self, message_id: UUID, referenced_entities: List[dict]):
        """Persist referenced entities for a message"""
        self.repository.add_referenced_entities(message_id, referenced_entities)

    def _generate_title(self, first_message: str) -> str:
        """
        Generate a title for a conversation based on the first message

        Args:
            first_message: The first user message

        Returns:
            A generated title
        """
        prompt = (
            f"Generate a short title (max 50 chars) for a "
            f"conversation that starts with: {first_message}"
        )
        response = self.copilot_service.generate_text(prompt)
        return response.strip()[:100]
