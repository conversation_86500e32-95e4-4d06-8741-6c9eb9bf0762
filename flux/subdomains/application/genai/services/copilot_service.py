"""
Domain service for the Attendee Copilot feature
"""

from typing import List, Optional, Dict, Any
from uuid import UUID

from flux.adapters.logging import get_logger
from flux.subdomains.application.genai.entities.copilot_entities import (
    Query, Response, Message
)
from flux.subdomains.application.genai.interfaces.llm_adapter import LLMAdapter


logger = get_logger(__name__)


class CopilotService:
    """Domain service for coordinating AI assistant operations"""

    def __init__(
        self,
        llm_adapter: LLMAdapter,
    ):
        self.llm_adapter = llm_adapter

    def process_query(self, query: Query, event_id: int, user_id: int,
                      conversation_id: UUID, conversation_messages: Optional[List[Message]] = None,
                      message_id: Optional[UUID] = None) -> Response:
        """
        Process a user query and generate a response with conversation history

        Args:
            query: The processed user query
            event_id: The event context
            user_id: The user ID making the query
            conversation_id: The conversation ID
            conversation_messages: Previous messages in the conversation

        Returns:
            Structured response object
        """

        # Format messages for chat - including history
        formatted_messages: List[Dict[str, Any]] = []

        # Add conversation history
        if conversation_messages:
            for msg in conversation_messages:
                formatted_messages.append({
                    "role": msg.role,
                    "content": msg.content,
                    "metadata": {  # type: ignore
                        "timestamp": msg.created_at.isoformat()
                        if hasattr(msg.created_at, 'isoformat')
                        else str(msg.created_at)
                    }
                })

        # Add the current query
        formatted_messages.append({
            "role": "user",
            "content": query.raw_text,
            "metadata": {  # type: ignore
                "intent": str(query.intent),
                "parameters": query.parameters
            }
        })

        # Get response using chat interface with history
        llm_response = self.llm_adapter.chat(
            conversation_id=conversation_id,
            messages=formatted_messages,
            max_tokens=1000,
            temperature=0.2,
            message_id=message_id,
        )

        logger.info(
            "Attendee Copilot - Query processed",
            event_id=event_id,
            user_id=user_id,
            **llm_response.metadata
        )

        return Response(
            content=llm_response.text,
            referenced_entities=[],
            suggested_actions=[]
        )

    def process_query_stream(
        self,
        query: Query,
        event_id: int,
        user_id: int,
        conversation_id: UUID,
        conversation_messages: Optional[List[Message]] = None,
        message_id: Optional[UUID] = None,
    ):
        """Stream LLM response for the given query"""

        formatted_messages: List[Dict[str, Any]] = []
        if conversation_messages:
            for msg in conversation_messages:
                formatted_messages.append(
                    {
                        "role": msg.role,
                        "content": msg.content,
                        "metadata": {
                            "timestamp": msg.created_at.isoformat()
                            if hasattr(msg.created_at, "isoformat")
                            else str(msg.created_at)
                        },
                    }
                )

        formatted_messages.append(
            {
                "role": "user",
                "content": query.raw_text,
                "metadata": {
                    "intent": str(query.intent),
                    "parameters": query.parameters,
                },
            }
        )

        return self.llm_adapter.chat_stream(
            conversation_id=conversation_id,
            messages=formatted_messages,
            max_tokens=1000,
            temperature=0.2,
            message_id=message_id,
        )

    def generate_text(self, prompt: str, max_tokens: int = 1000,
                      temperature: float = 0.7) -> str:
        """
        Generate text using the LLM adapter for non-conversational tasks
        (e.g., generating titles, summarizing content)

        Args:
            prompt: The text prompt to send to the LLM
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0 = deterministic, 1.0 = more random)

        Returns:
            Generated text
        """
        response = self.llm_adapter.generate_text(prompt, max_tokens, temperature)
        return response.text
