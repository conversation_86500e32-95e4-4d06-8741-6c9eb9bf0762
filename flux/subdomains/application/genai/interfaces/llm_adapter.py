"""
Interface for LLM communication adapters
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Iterable
from uuid import UUID


@dataclass
class LLMResponse:
    """Data class for standardized LLM response"""
    text: str
    metadata: Dict[str, Any]
    tool_usage: Optional[List[Dict[str, Any]]] = field(default=None)

    def has_tool_usage(self) -> bool:
        """Check if this response contains tool usage data"""
        return self.tool_usage is not None and len(self.tool_usage) > 0

    def get_tool_names(self) -> List[str]:
        """Get a list of tool names that were used"""
        return [tool.get("tool", "unknown") for tool in getattr(self, "tool_usage", [])]


class LLMAdapter(ABC):
    """Interface for LLM communication adapters"""

    @abstractmethod
    def generate_text(self, prompt: str, max_tokens: int = 1000,
                      temperature: float = 0.7) -> LLMResponse:
        """
        Generate text response from the LLM based on the prompt

        Args:
            prompt: The text prompt to send to the LLM
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0 = deterministic, 1.0 = more random)

        Returns:
            LLMResponse object containing the generated text and metadata
        """

    @abstractmethod
    def chat_stream(
        self,
        conversation_id: UUID,
        messages: List[Dict[str, Any]],
        max_tokens: int = 1000,
        temperature: float = 0.7,
        message_id: Optional[UUID] = None,
    ) -> Iterable[str]:
        """Stream response chunks based on a conversation history"""

    @abstractmethod
    def chat(self,
             conversation_id: UUID,
             messages: List[Dict[str, Any]],
             max_tokens: int = 1000,
             temperature: float = 0.7,
             message_id: Optional[UUID] = None) -> LLMResponse:
        """
        Generate response based on a conversation history

        Args:
            conversation_id: The conversation ID
            messages: List of message dictionaries with 'role' and 'content' keys
                     roles should be 'system', 'user', or 'assistant'
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0 = deterministic, 1.0 = more random)

        Returns:
            LLMResponse object containing the generated text and metadata
        """
