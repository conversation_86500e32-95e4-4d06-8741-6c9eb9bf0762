"""
Interface for the Copilot repository
"""
from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from flux.domains.common.pagination import PaginationInfo
from flux.subdomains.application.genai.entities.copilot_entities import Conversation, Message


class CopilotRepository(ABC):
    """
    Interface for managing Copilot conversations and messages
    """

    @abstractmethod
    def get_conversation(self, conversation_id: UUID, event_id: Optional[int] = None,
                         user_id: Optional[int] = None) -> Optional[Conversation]:
        """Get a conversation by ID with optional event and user filters"""

    @abstractmethod
    def get_conversations(
        self, event_id: int, user_id: int, page_info: Optional[PaginationInfo] = None
    ) -> List[Conversation]:
        """Get all conversations for a user in an event"""

    @abstractmethod
    def create_conversation(self, id: UUID, event_id: int, user_id: int,
                            title: Optional[str] = None) -> Conversation:
        """Create a new conversation"""

    @abstractmethod
    def update_conversation_title(self, conversation_id: UUID, title: str
                                  ) -> Optional[Conversation]:
        """Update a conversation's title"""

    @abstractmethod
    def add_message(self, conversation_id: UUID, role: str, content: str,
                    metadata: Optional[dict] = None, id: Optional[UUID] = None
                    ) -> Optional[Message]:
        """Add a message to a conversation"""

    @abstractmethod
    def get_messages(self, conversation_id: UUID, page_info: Optional[PaginationInfo] = None
                     ) -> List[Message]:
        """
        Get messages for a conversation with pagination support

        Args:
            conversation_id: The conversation ID
            page_info: Optional pagination information (includes sorting)

        Returns:
            List of messages with pagination
        """

    @abstractmethod
    def get_message(self, conversation_id: UUID, message_id: UUID) -> Optional[Message]:
        """Retrieve a single message"""

    @abstractmethod
    def delete_conversation(self, conversation_id: UUID) -> bool:
        """Delete a conversation and all its messages"""

    @abstractmethod
    def add_referenced_entities(self, message_id: UUID, referenced_entities: List[dict]
                                ) -> List[dict]:
        """Persist referenced entities for a message and return them"""

    @abstractmethod
    def get_referenced_entities(self, message_ids: List[UUID]) -> dict:
        """Retrieve referenced entities grouped by message id"""
