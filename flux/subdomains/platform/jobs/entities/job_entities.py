import enum

from flux.utils.enum import StringEnum
from flux.domains.common import Entity, EntitySchema, ef, MatchOnAttribute
from flux.domains.event.commons.entities import CreatedAtMixin, UpdatedAtMixin


class JobStatus(StringEnum):
    """Status enum for jobs and job tasks"""
    pending = enum.auto()
    running = enum.auto()
    completed = enum.auto()
    failed = enum.auto()
    partially_completed = enum.auto()


class JobNotFoundError(Exception):
    """Raised when a job is not found"""
    pass


class JobTaskNotFoundError(Exception):
    """Raised when a job task is not found"""
    pass


class BaseJobEntitySchema(EntitySchema):
    """Abstract base schema with common fields for Job and JobTask entities"""

    id = ef.ID(metadata={'readonly': True})
    name = ef.String(required=True, max_length=255)
    status = ef.Enum(JobStatus, required=True, default=JobStatus.pending)
    started_at = ef.DateTime(allow_none=True)
    completed_at = ef.DateTime(allow_none=True)
    message = ef.Dict(allow_none=True)
    created_at = ef.DateTime(metadata={'readonly': True})
    updated_at = ef.DateTime(metadata={'readonly': True})


class BaseJobEntity(CreatedAtMixin, UpdatedAtMixin, Entity):
    """Abstract base entity with common fields for Job and JobTask"""

    match_on = MatchOnAttribute(['id'])


class JobTask(BaseJobEntity):
    """Entity representing a single task within a job"""

    class Schema(BaseJobEntitySchema):
        job_id = ef.ID(required=True, metadata={'readonly': True})
        task_meta = ef.Dict(allow_none=True)
        chunk_count = ef.Integer(allow_none=True)
        is_callback = ef.Boolean(default=False)


class Job(BaseJobEntity):
    """Entity representing an async job"""

    class Schema(BaseJobEntitySchema):
        context_type = ef.String(allow_none=True, max_length=100)
        context_id = ef.String(allow_none=True, max_length=100)
        created_by = ef.ID(allow_none=True)
        tasks = ef.List(ef.Nested(JobTask.Schema), allow_none=True, metadata={'readonly': True, 'is_include': True})


