"""
Entities for the Jobs domain
"""
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
import uuid
from flux.utils import timeutils
from uuid import UUID


class JobStatus(str, Enum):
    """Enum for job status values"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIALLY_COMPLETED = "partially_completed"


@dataclass
class BaseJobEntity:
    """Base class with common attributes for Job and JobTask entities"""
    id: UUID = field(default_factory=uuid.uuid4)
    name: Optional[str] = None
    status: JobStatus = JobStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    message: Optional[Dict[str, Any]] = field(default_factory=dict)
    created_at: datetime = field(default_factory=timeutils.now)
    updated_at: datetime = field(default_factory=timeutils.now)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the entity to a dictionary representation with common fields"""
        return {
            "id": str(self.id),
            "name": self.name,
            "status": str(self.status),
            "started_at": (
                self.started_at.isoformat()
                if isinstance(self.started_at, datetime) else self.started_at
            ),
            "completed_at": (
                self.completed_at.isoformat()
                if isinstance(self.completed_at, datetime) else self.completed_at
            ),
            "message": self.message,
            "created_at": (
                self.created_at.isoformat()
                if isinstance(self.created_at, datetime) else self.created_at
            ),
            "updated_at": (
                self.updated_at.isoformat()
                if isinstance(self.updated_at, datetime) else self.updated_at
            ),
        }


@dataclass
class JobTask(BaseJobEntity):
    """Entity representing a job task"""
    job_id: UUID
    task_meta: Optional[Dict[str, Any]] = field(default_factory=dict)
    chunk_index: Optional[int] = None
    is_callback: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert the job task to a dictionary representation"""
        # Get base dictionary from parent class
        result = super().to_dict()

        # Add JobTask-specific fields
        result.update({
            "job_id": str(self.job_id),
            "task_meta": self.task_meta,
            "chunk_index": self.chunk_index,
            "is_callback": self.is_callback,
        })

        return result


@dataclass
class Job(BaseJobEntity):
    """Entity representing an async job"""
    context_type: Optional[str] = None
    context_id: Optional[str] = None
    created_by: Optional[int] = None
    tasks: Optional[List[JobTask]] = field(default_factory=list)

    @property
    def task_count(self) -> int:
        """Get the number of tasks in the job"""
        return len(self.tasks) if self.tasks else 0

    def to_dict(self) -> Dict[str, Any]:
        """Convert the job to a dictionary representation"""
        # Get base dictionary from parent class
        result = super().to_dict()

        # Add Job-specific fields
        result.update({
            "context_type": self.context_type,
            "context_id": self.context_id,
            "created_by": self.created_by,
            "task_count": self.task_count,
            "tasks": [task.to_dict() for task in self.tasks] if self.tasks else []
        })

        return result


