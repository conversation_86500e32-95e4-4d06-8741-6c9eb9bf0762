"""
Interface for the Jobs repository
"""
from abc import ABC, abstractmethod
from typing import Type
from uuid import UUID

from flux.subdomains.platform.jobs.entities.job_entities import Job


class JobRepository(ABC):
    """
    Interface for managing Job entities and their tasks
    """

    @abstractmethod
    def __init__(self, job_entity_cls: Type[Job]):
        """Constructor taking a Job Entity class"""

    @abstractmethod
    def create_job(self, job: Job) -> Job:
        """Create a new Job entity and return the persisted entity"""

    @abstractmethod
    def get_job_by_id(self, job_id: UUID) -> Job:
        """Get a Job entity by its ID"""

    @abstractmethod
    def save_job(self, job: Job) -> None:
        """Save/update an existing Job entity"""

    @abstractmethod
    def delete_job(self, job_id: UUID) -> None:
        """Delete a Job entity by its ID"""
