"""
Interface for the Jobs repository
"""
from abc import ABC, abstractmethod
from typing import Type, Set

from flux.subdomains.platform.jobs.entities.job_entities import Job


class JobRepository(ABC):
    """
    Interface for managing Job entities and their tasks
    """

    @abstractmethod
    def create_job(self, job: Job) -> Job:
        """Create a new Job entity and return the persisted entity"""
