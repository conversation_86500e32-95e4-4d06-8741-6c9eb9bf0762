"""
SQLAlchemy models for the Jobs domain
"""


from flux.adapters.db import db, UUID, EntityModel


class TrackableTaskModel(EntityModel):
    """
    Abstract SQLAlchemy model base class for trackable tasks with common fields
    """
    __abstract__ = True
    
    # Common column definitions for tasks that can be tracked
    name = db.Column(db.VARCHAR(255), nullable=False)
    status = db.Column(db.VARCHAR(50), nullable=False, default='pending')  # 'pending', 'running', 'completed', 'failed'
    started_at = db.Column(db.TIMESTAMP, nullable=True)
    message = db.Column(db.TEXT, nullable=True)  # For error messages or status updates
    completed_at = db.Column(db.TIMESTAMP, nullable=True)



class JobTaskModel(TrackableTaskModel):
    """
    SQLAlchemy model for Job Tasks
    """
    __tablename__ = 'job_tasks'

    _sortable_fields = {
        'created_at': None,
        'name': None,
        'status': None,
    }

    # Column definitions
    job_id = db.Column(
        UUID,
        db.<PERSON>ey('jobs.id', ondelete='CASCADE'),
        nullable=False,
    )
    task_metadata = db.Column(db.JSON, nullable=True)

    @classmethod
    def get_by_job_id(cls, job_id, page_info=None):
        """
        Get tasks for a job with pagination support

        Args:
            job_id: The job ID
            page_info: Pagination information (includes sorting)

        Returns:
            List of task models
        """
        query = cls.query.filter(cls.job_id == job_id)

        q = cls._sort_and_paginate(
            query=query,
            page_info=page_info,
            default_sort=cls.created_at
        )

        return q

    def as_entity(self):
        """Convert the model to a domain entity"""
        from flux.subdomains.platform.jobs.entities.task import Task
        
        return Task(
            id=self.id,
            job_id=self.job_id,
            name=self.name,
            status=self.status,
            started_at=self.started_at,
            completed_at=self.completed_at,
            message=self.message,
            created_at=self.created_at,
            updated_at=self.updated_at,
            metadata=self.task_metadata or {}
        )


class JobModel(TrackableTaskModel):
    """
    SQLAlchemy model for Jobs
    """
    __tablename__ = 'jobs'

    _searchable_fields = {
        'name': None,
    }
    _sortable_fields = {
        'name': None,
        'status': None,
        'created_at': None,
        'updated_at': None,
    }

    # Column definitions
    context_type = db.Column(db.VARCHAR(100), nullable=True)  # Type of the context (e.g., "event", "organization")
    context_id = db.Column(db.VARCHAR(100), nullable=True)   # ID of the related context

    # Relationships
    tasks = db.relationship(
        JobTaskModel,
        uselist=True,
        lazy='select',
        cascade='all, delete-orphan',
        backref='job',
        order_by=JobTaskModel.created_at,
    )

    @classmethod
    def get_by_context(cls, context_type, context_id, page_info=None):
        """Get all jobs for a specific context"""
        query = cls.query.filter(
            and_(
                cls.context_type == context_type,
                cls.context_id == context_id
            )
        )
        q = cls._sort_and_paginate(
            query=query,
            page_info=page_info,
            default_sort=cls.created_at.desc()
        )

        return q

    @classmethod
    def get_by_id(cls, job_id, context_type=None, context_id=None):
        """Get a job by ID with optional context filters"""
        filters = [cls.id == job_id]
        if context_type is not None:
            filters.append(cls.context_type == context_type)
        if context_id is not None:
            filters.append(cls.context_id == context_id)
        return cls.query.filter(and_(*filters)).first()

    def as_entity(self):
        """Convert the model to a domain entity"""
        from flux.subdomains.platform.jobs.entities.job import Job
        
        return Job(
            id=self.id,
            name=self.name,
            status=self.status,
            started_at=self.started_at,
            completed_at=self.completed_at,
            message=self.message,
            context_type=self.context_type,
            context_id=self.context_id,
            created_at=self.created_at,
            updated_at=self.updated_at,
            tasks=[task.as_entity() for task in self.tasks]
        )