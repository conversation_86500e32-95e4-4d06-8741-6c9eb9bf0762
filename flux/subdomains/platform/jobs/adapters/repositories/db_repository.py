"""
Database repository implementation for Jobs domain
"""

from typing import Type
from uuid import UUID

from flux.adapters.db import db
from flux.subdomains.platform.jobs.entities.job_entities import Job, JobNotFoundError
from flux.subdomains.platform.jobs.interfaces.job_repository import JobRepository
from flux.subdomains.platform.jobs.adapters.repositories.models import JobModel


class JobRepositoryImpl(JobRepository):
    """Database repository implementation for Job operations"""

    def __init__(self, job_entity_cls: Type[Job]):
        self.job_entity_cls = job_entity_cls

    def create_job(self, job: Job) -> Job:
        """Create a new Job entity and return the persisted entity"""
        model = JobModel.from_entity(job)
        db.session.add(model)
        db.session.flush()
        return model.as_entity(self.job_entity_cls)

    def get_job_by_id(self, job_id: UUID) -> Job:
        """Get a Job entity by its ID"""
        model = JobModel.get_by_id(job_id)
        if not model:
            raise JobNotFoundError(f"Job with ID {job_id} not found")
        return model.as_entity(self.job_entity_cls)

    def save_job(self, job: Job) -> None:
        """Save/update an existing Job entity"""
        # Get existing model or create new one
        existing_model = JobModel.get_by_id(job.id)
        if existing_model:
            # Update existing model with new data
            updated_model = JobModel.from_entity(job)
            # Merge the updated model
            db.session.merge(updated_model)
        else:
            # Create new model if it doesn't exist
            new_model = JobModel.from_entity(job)
            db.session.add(new_model)
        db.session.flush()

    def delete_job(self, job_id: UUID) -> None:
        """Delete a Job entity by its ID"""
        JobModel.delete_by_id(job_id)
