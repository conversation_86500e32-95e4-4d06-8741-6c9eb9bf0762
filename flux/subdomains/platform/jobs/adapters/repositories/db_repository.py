"""
Database repository implementation for Jobs domain
"""

from typing import Type
from uuid import UUID

from flux.adapters.db import db
from flux.subdomains.platform.jobs.entities.job_entities import Job, JobNotFoundError
from flux.subdomains.platform.jobs.interfaces.job_repository import JobRepository
from flux.subdomains.platform.jobs.adapters.repositories.models import JobModel


class JobRepositoryImpl(JobRepository):
    """Database repository implementation for Job operations"""

    def __init__(self, job_entity_cls: Type[Job]):
        self.job_entity_cls = job_entity_cls

    def create_job(self, job: Job) -> Job:
        """Create a new Job entity and return the persisted entity"""
        model = JobModel.from_entity(job)
        db.session.add(model)
        db.session.flush()
        # Include tasks in response if they were provided in the original entity
        include_tasks = hasattr(job, 'tasks') and job.tasks is not None
        return model.as_entity(self.job_entity_cls, include_tasks=include_tasks)

    def get_job_by_id(self, job_id: UUID, include_tasks: bool = False) -> Job:
        """Get a Job entity by its ID"""
        if include_tasks:
            # Eagerly load tasks when requested
            model = JobModel.query.options(db.joinedload(JobModel.tasks)).filter_by(id=job_id).first()
        else:
            model = JobModel.get_by_id(job_id)

        if not model:
            raise JobNotFoundError(f"Job with ID {job_id} not found")
        return model.as_entity(self.job_entity_cls, include_tasks=include_tasks)

    def save_job(self, job: Job) -> None:
        """Save/update an existing Job entity"""
        # Get existing model or create new one
        existing_model = JobModel.get_by_id(job.id)
        if existing_model:
            # Update existing model with new data
            updated_model = JobModel.from_entity(job)
            # Merge the updated model
            db.session.merge(updated_model)
        else:
            # Create new model if it doesn't exist
            new_model = JobModel.from_entity(job)
            db.session.add(new_model)
        db.session.flush()

    def delete_job(self, job_id: UUID) -> None:
        """Delete a Job entity by its ID"""
        JobModel.delete_by_id(job_id)
