"""
Database repository implementation for Jobs domain
"""

from typing import Type
from uuid import UUID

from flux.adapters.db import db
from flux.subdomains.platform.jobs.entities.job_entities import Job, JobTask, JobNotFoundError, JobTaskNotFoundError
from flux.subdomains.platform.jobs.adapters.repositories.repository import <PERSON>Rep<PERSON>, JobTaskRepo
from flux.subdomains.platform.jobs.adapters.repositories.models import JobModel, JobTaskModel


def _persist_model(model):
    """Helper function to persist a model to the database"""
    db.session.add(model)
    db.session.flush()


class JobDBRepo(JobRepo):
    """Database repository implementation for Job operations"""

    def __init__(self, job_entity_cls: Type[Job]):
        self.job_entity_cls = job_entity_cls

    def create_job(self, job: Job) -> Job:
        """Create a new Job entity and return the persisted entity"""
        model = JobModel.create_from_entity(job)
        _persist_model(model)
        return model.as_entity(self.job_entity_cls)

    def get_job_by_id(self, job_id: UUID) -> Job:
        """Get a Job entity by its ID"""
        model = JobModel.get_by_id(job_id)
        if not model:
            raise JobNotFoundError(f"Job with ID {job_id} not found")
        return model.as_entity(self.job_entity_cls)

    def save_job(self, job: Job) -> None:
        """Save/update an existing Job entity"""
        model = db.session.merge(JobModel(id=job.id))
        model.update_from_entity(job)
        _persist_model(model)

    def delete_job(self, job_id: UUID) -> None:
        """Delete a Job entity by its ID"""
        JobModel.delete_by_id(job_id)


class JobTaskDBRepo(JobTaskRepo):
    """Database repository implementation for JobTask operations"""

    def __init__(self, job_task_entity_cls: Type[JobTask]):
        self.job_task_entity_cls = job_task_entity_cls

    def create_job_task(self, job_task: JobTask) -> JobTask:
        """Create a new JobTask entity and return the persisted entity"""
        model = JobTaskModel.create_from_entity(job_task)
        _persist_model(model)
        return model.as_entity(self.job_task_entity_cls)

    def get_job_task_by_id(self, job_task_id: UUID) -> JobTask:
        """Get a JobTask entity by its ID"""
        model = JobTaskModel.get_by_id(job_task_id)
        if not model:
            raise JobTaskNotFoundError(f"JobTask with ID {job_task_id} not found")
        return model.as_entity(self.job_task_entity_cls)

    def save_job_task(self, job_task: JobTask) -> None:
        """Save/update an existing JobTask entity"""
        model = db.session.merge(JobTaskModel(id=job_task.id))
        model.update_from_entity(job_task)
        _persist_model(model)

    def delete_job_task(self, job_task_id: UUID) -> None:
        """Delete a JobTask entity by its ID"""
        JobTaskModel.delete_by_id(job_task_id)
