"""
Database repository implementation for Jobs domain
"""

from typing import Type, Set

from flux.adapters.db import db
from flux.subdomains.platform.jobs.entities.job_entities import Job
from flux.subdomains.platform.jobs.interfaces.job_repository import JobRepository
from flux.subdomains.platform.jobs.adapters.repositories.models import JobModel


class JobRepositoryImpl(JobRepository):
    """Database repository implementation for Job operations"""

    def __init__(self, job_entity_cls: Type[Job]):
        self.job_entity_cls = job_entity_cls

    def create_job(self, job: Job, include: Set[str] = None) -> Job:
        """Create a new Job entity and return the persisted entity"""
        if include is None:
            include = {'all'}  # Default to include all

        model = JobModel.from_entity(job)
        db.session.add(model)
        db.session.flush()
        return model.as_entity(self.job_entity_cls, include=include)
