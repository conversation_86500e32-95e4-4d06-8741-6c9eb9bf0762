"""
Database repository implementation for Jobs domain
"""

from typing import Set

from flux.subdomains.platform.jobs.entities.job_entities import Job
from flux.subdomains.platform.jobs.interfaces.job_repository import JobRepository
from flux.subdomains.platform.jobs.adapters.repositories.models import JobModel


class JobRepositoryImpl(JobRepository):
    """Database repository implementation for Job operations"""

    def __init__(self, entity_cls: Type[Job]):
        self.job_model = JobModel
        self.entity_cls = Job

    def create_job(self, job: Job, include: Set[str] = None) -> Job:
        """Create a new Job entity and return the persisted entity"""
        if include is None:
            include = {'all'}  # Default to include all

        model = self.job_model.from_entity(job)
        self.job_model.save(model)
        return model.as_entity(Job, include=include)
