"""
Database repository implementation for Jobs domain
"""

from typing import Set, Type

from flux.subdomains.platform.jobs.entities.job_entities import Job
from flux.subdomains.platform.jobs.interfaces.job_repository import JobRepository
from flux.subdomains.platform.jobs.adapters.repositories.models import JobModel


class JobRepositoryImpl(JobRepository):
    """Database repository implementation for Job operations"""

    def __init__(self, job_entity_cls: Type[Job]):
        self.job_entity_cls = job_entity_cls
        self.job_model = JobModel

    @property
    def include(self):
        return self.job_entity_cls.get_active_includes()

    def create_job(self, job: Job, include: Set[str] = None) -> Job:
        """Create a new Job entity and return the persisted entity"""
        if include is None:
            include = self.include  # Use the entity's active includes

        model = self.job_model.from_entity(job)

        # Set immutable fields that can't be updated once created
        model.context_type = job.context_type
        model.context_id = job.context_id
        model.created_by = job.created_by
        model.created_at = job.created_at

        self.job_model.save(model)
        return model.as_entity(self.job_entity_cls, include=include)
