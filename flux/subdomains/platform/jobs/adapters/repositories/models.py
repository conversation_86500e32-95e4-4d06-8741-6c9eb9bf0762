"""
SQLAlchemy models for the Jobs domain
"""

from flux.adapters.db import db, UUID, EntityModel, ProperEnum
from flux.adapters.db.types import JSON
from flux.subdomains.platform.jobs.entities.job_entities import JobStatus, Job, JobTask
from flux.utils.timeutils import make_timezone_aware


class BaseJobModel(EntityModel):
    """
    Abstract base model with common fields for Job and JobTask models
    """
    __abstract__ = True

    _sortable_fields = {
        'started_at': None,
        'completed_at': None,
    }

    # Common column definitions
    name = db.Column(db.VARCHAR(255), nullable=False)
    status = db.Column(
        ProperEnum(50, enum_cls=JobStatus),
        nullable=False,
        default=JobStatus.pending,
    )
    started_at = db.Column(db.TIMESTAMP, nullable=True)
    completed_at = db.Column(db.TIMESTAMP, nullable=True)
    message = db.Column(JSON, nullable=True)


class JobTaskModel(BaseJobModel):
    """
    SQLAlchemy model for Job Tasks
    """
    __tablename__ = 'job_tasks'

    # Add indexes for better query performance
    __table_args__ = (
        db.Index('idx_job_tasks_job_id', 'job_id'),
        db.Index('idx_job_tasks_status', 'status'),
        db.Index('idx_job_tasks_started_at', 'started_at'),
        db.Index('idx_job_tasks_completed_at', 'completed_at'),
    )

    # JobTask-specific columns
    job_id = db.Column(
        UUID,
        db.ForeignKey('jobs.id', ondelete='CASCADE'),
        nullable=False,
    )
    task_meta = db.Column(JSON, nullable=True)
    chunk_index = db.Column(db.INTEGER, nullable=True)
    is_callback = db.Column(db.BOOLEAN, nullable=False, default=False)

    def as_entity(self):
        """Convert model to JobTask entity"""

        return JobTask(
            id=self.id,
            job_id=self.job_id,
            name=self.name,
            status=self.status,
            started_at=make_timezone_aware(self.started_at) if self.started_at else None,
            completed_at=make_timezone_aware(self.completed_at) if self.completed_at else None,
            message=self.message,
            task_meta=self.task_meta,
            chunk_index=self.chunk_index,
            is_callback=self.is_callback,
            created_at=make_timezone_aware(self.created_at),
            updated_at=make_timezone_aware(self.updated_at),
        )

    @classmethod
    def from_entity(cls, job_task_entity):
        """Create model from JobTask entity"""
        return cls(
            name=job_task_entity.name,
            status=job_task_entity.status,
            started_at=job_task_entity.started_at,
            completed_at=job_task_entity.completed_at,
            message=job_task_entity.message,
            task_meta=job_task_entity.task_meta,
            chunk_index=job_task_entity.chunk_index,
            is_callback=job_task_entity.is_callback,
            updated_at=job_task_entity.updated_at,
        )


class JobModel(BaseJobModel):
    """
    SQLAlchemy model for Jobs
    """
    __tablename__ = 'jobs'

    # Add indexes for better query performance
    __table_args__ = (
        db.Index('idx_jobs_context_id', 'context_id'),
        db.Index('idx_jobs_status', 'status'),
        db.Index('idx_jobs_started_at', 'started_at'),
        db.Index('idx_jobs_completed_at', 'completed_at'),
        db.Index('idx_jobs_created_by', 'created_by'),
    )

    # Job-specific columns
    context_type = db.Column(db.VARCHAR(100), nullable=True)
    context_id = db.Column(db.VARCHAR(100), nullable=True)
    created_by = db.Column(
        db.Integer,
        db.ForeignKey('user_accounts.id', ondelete='SET NULL'),
        nullable=True
    )

    # Relationships
    tasks = db.relationship(
        JobTaskModel,
        uselist=True,
        lazy='select',
        cascade='all, delete-orphan',
        backref='job',
        order_by=JobTaskModel.created_at,
    )

    def as_entity(self, job_entity_cls):
        """Convert model to Job entity"""
        entity_data = {
            'id': self.id,
            'name': self.name,
            'status': self.status,
            'started_at': make_timezone_aware(self.started_at) if self.started_at else None,
            'completed_at': make_timezone_aware(self.completed_at) if self.completed_at else None,
            'message': self.message,
            'context_type': self.context_type,
            'context_id': self.context_id,
            'created_by': self.created_by,
            'created_at': make_timezone_aware(self.created_at),
            'updated_at': make_timezone_aware(self.updated_at),
            
        }
        include = job_entity_cls.include
        if include and 'tasks' in include and self.tasks:
            entity_data['tasks'] = [task.as_entity(job_entity_cls.Task) for task in self.tasks]
        return job_entity_cls(**entity_data)

    @classmethod
    def from_entity(cls, job_entity):
        """Create model from Job entity"""
        # Create the main job model
        job_model = cls(
            name=job_entity.name,
            status=job_entity.status,
            started_at=job_entity.started_at,
            completed_at=job_entity.completed_at,
            message=job_entity.message,
            updated_at=job_entity.updated_at,
        )

        # Create task models if tasks exist
        if job_entity.tasks:
            job_model.task = [JobTaskModel.from_entity(task) for task in job_entity.tasks]

        return job_model
