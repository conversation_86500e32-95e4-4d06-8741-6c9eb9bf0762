"""
SQLAlchemy models for the Jobs domain
"""

from flux.adapters.db import db, UUID, EntityModel, ProperEnum
from flux.adapters.db.types import JSON
from flux.subdomains.platform.jobs.entities.job_entities import JobStatus


class BaseJobModel(EntityModel):
    """
    Abstract base model with common fields for Job and JobTask models
    """
    __abstract__ = True

    _searchable_fields = {
        'name': None,
    }
    _sortable_fields = {
        'name': None,
        'status': None,
        'created_at': None,
        'updated_at': None,
    }

    # Common column definitions
    name = db.Column(db.VARCHAR(255), nullable=False)
    status = db.Column(
        ProperEnum(32, enum_cls=JobStatus),
        nullable=False,
        default=JobStatus.pending,
    )
    started_at = db.Column(db.TIMESTAMP, nullable=True)
    completed_at = db.Column(db.TIMESTAMP, nullable=True)
    message = db.Column(JSON, nullable=True)


class JobTaskModel(BaseJobModel):
    """
    SQLAlchemy model for Job Tasks
    """
    __tablename__ = 'job_tasks'

    # JobTask-specific columns
    job_id = db.Column(
        UUID,
        db.ForeignKey('jobs.id', ondelete='CASCADE'),
        nullable=False,
    )
    task_meta = db.Column(JSON, nullable=True)
    chunk_count = db.Column(db.INTEGER, nullable=True)
    is_callback = db.Column(db.BOOLEAN, nullable=False, default=False)

    def as_entity(self, job_task_entity_cls):
        """Convert model to JobTask entity"""

        entity_data = {
            'id': self.id,
            'job_id': self.job_id,
            'name': self.name,
            'status': self.status,
            'started_at': self.started_at,
            'completed_at': self.completed_at,
            'message': self.message,
            'task_meta': self.task_meta,
            'chunk_count': self.chunk_count,
            'is_callback': self.is_callback,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
        }

        return job_task_entity_cls(**entity_data)

    @classmethod
    def create_from_entity(cls, job_task_entity):
        """Create model from JobTask entity"""
        model = cls(
            id=job_task_entity.id,
            job_id=job_task_entity.job_id,
        )
        model.update_from_entity(job_task_entity)
        return model

    def update_from_entity(self, job_task_entity):
        """Update model from JobTask entity"""
        self.name = job_task_entity.name
        self.status = job_task_entity.status
        self.started_at = job_task_entity.started_at
        self.completed_at = job_task_entity.completed_at
        self.message = job_task_entity.message
        self.task_meta = job_task_entity.task_meta
        self.chunk_count = job_task_entity.chunk_count
        self.is_callback = job_task_entity.is_callback
        self.created_at = job_task_entity.created_at
        self.updated_at = job_task_entity.updated_at


class JobModel(BaseJobModel):
    """
    SQLAlchemy model for Jobs
    """
    __tablename__ = 'jobs'

    # Job-specific columns
    context_type = db.Column(db.VARCHAR(100), nullable=True)
    context_id = db.Column(db.VARCHAR(100), nullable=True)
    created_by = db.Column(UUID, nullable=True)

    # Relationships
    tasks = db.relationship(
        JobTaskModel,
        uselist=True,
        lazy='select',
        cascade='all, delete-orphan',
        backref='job',
        order_by=JobTaskModel.created_at,
    )

    def as_entity(self, job_entity_cls):
        """Convert model to Job entity"""
        entity_data = {
            'id': self.id,
            'name': self.name,
            'status': self.status,
            'started_at': self.started_at,
            'completed_at': self.completed_at,
            'message': self.message,
            'context_type': self.context_type,
            'context_id': self.context_id,
            'created_by': self.created_by,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
        }

        # Include tasks if they are loaded
        if hasattr(self, 'tasks') and self.tasks:
            entity_data['tasks'] = [
                task.as_entity(job_entity_cls.JobTask) for task in self.tasks
            ]

        return job_entity_cls(**entity_data)

    @classmethod
    def create_from_entity(cls, job_entity):
        """Create model from Job entity"""
        model = cls(id=job_entity.id)
        model.update_from_entity(job_entity)
        return model

    def update_from_entity(self, job_entity):
        """Update model from Job entity"""
        self.name = job_entity.name
        self.status = job_entity.status
        self.started_at = job_entity.started_at
        self.completed_at = job_entity.completed_at
        self.message = job_entity.message
        self.context_type = job_entity.context_type
        self.context_id = job_entity.context_id
        self.created_by = job_entity.created_by
        self.created_at = job_entity.created_at
        self.updated_at = job_entity.updated_at
