"""
Repository interface for Jobs domain
"""

from abc import ABC, abstractmethod
from typing import Type
from uuid import UUID

from flux.subdomains.platform.jobs.entities.job_entities import Job, JobTask


class JobRepo(ABC):
    """Abstract repository interface for Job operations"""

    @abstractmethod
    def __init__(self, job_entity_cls: Type[Job]):
        """Constructor taking a Job entity class"""

    @abstractmethod
    def create_job(self, job: Job) -> Job:
        """Create a new Job entity and return the persisted entity"""

    @abstractmethod
    def get_job_by_id(self, job_id: UUID) -> Job:
        """Get a Job entity by its ID"""

    @abstractmethod
    def save_job(self, job: Job) -> None:
        """Save/update an existing Job entity"""

    @abstractmethod
    def delete_job(self, job_id: UUID) -> None:
        """Delete a Job entity by its ID"""


class JobTaskRepo(ABC):
    """Abstract repository interface for JobTask operations"""

    @abstractmethod
    def __init__(self, job_task_entity_cls: Type[JobTask]):
        """Constructor taking a JobTask entity class"""

    @abstractmethod
    def create_job_task(self, job_task: JobTask) -> JobTask:
        """Create a new JobTask entity and return the persisted entity"""

    @abstractmethod
    def get_job_task_by_id(self, job_task_id: UUID) -> JobTask:
        """Get a JobTask entity by its ID"""

    @abstractmethod
    def save_job_task(self, job_task: JobTask) -> None:
        """Save/update an existing JobTask entity"""

    @abstractmethod
    def delete_job_task(self, job_task_id: UUID) -> None:
        """Delete a JobTask entity by its ID"""
