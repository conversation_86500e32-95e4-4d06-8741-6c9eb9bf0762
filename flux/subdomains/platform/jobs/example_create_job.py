"""
Example usage of CreateJobUseCase with include set functionality
"""

from uuid import uuid4

from flux.subdomains.platform.jobs.entities.job_entities import Job, JobStatus, JobTask
from flux.subdomains.platform.jobs.adapters.repositories.db_repository import JobRepositoryImpl
from flux.subdomains.platform.jobs.usecases.job_usecases import CreateJobUseCase


def example_create_job_with_tasks():
    """Example of creating a job with tasks using include set"""
    
    # Set up the repository and use case
    job_repo = JobRepositoryImpl(Job)
    create_job_use_case = CreateJobUseCase(job_repo, Job)
    
    # Prepare job data with tasks
    job_data = {
        'name': 'Bulk People Import',
        'status': JobStatus.pending,
        'context_type': 'event',
        'context_id': str(uuid4()),
        'created_by': str(uuid4()),
        'message': {'description': 'Importing 1000 people from CSV file'},
        'tasks': [
            {
                'name': 'Process Chunk 1',
                'status': JobStatus.pending,
                'task_meta': {'start_index': 0, 'end_index': 100},
                'chunk_count': 1,
                'is_callback': False
            },
            {
                'name': 'Process Chunk 2', 
                'status': JobStatus.pending,
                'task_meta': {'start_index': 100, 'end_index': 200},
                'chunk_count': 1,
                'is_callback': False
            }
        ]
    }
    
    # Example 1: Create job with default include (include={'all'})
    # This will include tasks in the returned entity
    print("Creating job with default include={'all'}...")
    # created_job = create_job_use_case.run(job_data)
    # print(f"Created job: {created_job.name}")
    # print(f"Tasks included: {len(created_job.tasks) if created_job.tasks else 0}")
    
    # Example 2: Create job with explicit include={'tasks'}
    print("\nCreating job with include={'tasks'}...")
    # created_job = create_job_use_case.run(job_data, include={'tasks'})
    # print(f"Created job: {created_job.name}")
    # print(f"Tasks included: {len(created_job.tasks) if created_job.tasks else 0}")
    
    # Example 3: Create job without tasks (include=set())
    print("\nCreating job with include=set() (no tasks)...")
    # created_job = create_job_use_case.run(job_data, include=set())
    # print(f"Created job: {created_job.name}")
    # print(f"Tasks included: {created_job.tasks}")  # Should be None
    
    print("\nJob creation examples ready!")
    print("Key points:")
    print("- Default include={'all'} includes tasks in response")
    print("- include={'tasks'} also includes tasks")
    print("- include=set() excludes tasks (tasks=None)")
    print("- Repository and models handle the include set automatically")


if __name__ == "__main__":
    example_create_job_with_tasks()
