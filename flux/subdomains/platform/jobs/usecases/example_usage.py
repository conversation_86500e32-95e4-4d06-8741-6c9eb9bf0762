"""
Example usage of the Jobs domain components
This file demonstrates how to use the repository and use case together
"""

from uuid import uuid4

from flux.subdomains.platform.jobs.entities.job_entities import Job, JobStatus
from flux.subdomains.platform.jobs.adapters.repositories.db_repository import JobDBRepo
from flux.subdomains.platform.jobs.usecases.job_usecases import CreateJobUseCase


def example_create_job():
    """Example of how to create a job using the use case"""
    
    # Set up the repository and use case
    job_repo = JobDBRepo(Job)
    create_job_use_case = CreateJobUseCase(job_repo, Job)
    
    # Prepare job data
    job_data = {
        'name': 'Bulk People Import',
        'status': JobStatus.pending,
        'context_type': 'event',
        'context_id': str(uuid4()),
        'created_by': str(uuid4()),
        'message': {'description': 'Importing 1000 people from CSV file'}
    }
    
    # Create the job using the use case
    # Note: This would need a proper context object in real usage
    # created_job = create_job_use_case(context, job_data=job_data)
    
    print("Job creation use case is ready to use!")
    print(f"Job data structure: {job_data}")
    
    return job_data


if __name__ == "__main__":
    example_create_job()
