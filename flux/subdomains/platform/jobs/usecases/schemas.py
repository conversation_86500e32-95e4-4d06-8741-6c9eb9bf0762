"""
Schemas for Jobs domain use cases
"""

from marshmallow import Schema

from flux.domains.common.validation.custom_fields import <PERSON><PERSON><PERSON><PERSON><PERSON>s as ef
from flux.subdomains.platform.jobs.entities.job_entities import JobStatus


class CreateJobSchema(Schema):
    """Schema for creating a new job"""

    job_data = ef.Dict(required=True)
    include = ef.Set(ef.String(), load_default={'all'})


class CreateJobTaskSchema(Schema):
    """Schema for creating a new job task"""
    
    job_id = ef.UUID(required=True)
    job_task_data = ef.Dict(required=True)


class GetJobTaskByIdSchema(Schema):
    """Schema for getting a job task by ID"""
    
    job_task_id = ef.UUID(required=True)


class UpdateJobTaskSchema(Schema):
    """Schema for updating a job task"""
    
    job_task_id = ef.UUID(required=True)
    job_task_data = ef.Dict(required=True)


class DeleteJobTaskSchema(Schema):
    """Schema for deleting a job task"""
    
    job_task_id = ef.UUID(required=True)
