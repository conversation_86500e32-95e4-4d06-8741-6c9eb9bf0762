"""
Use cases for Jobs domain
"""

from typing import Type, Set

from flux.domains.common import UseCase
from flux.subdomains.platform.jobs.entities.job_entities import Job
from flux.subdomains.platform.jobs.interfaces.job_repository import JobRepository
from flux.subdomains.platform.jobs.usecases.schemas import CreateJobSchema


class CreateJobUseCase(UseCase):
    """Use case for creating a new job"""

    schema = [CreateJobSchema]
    raises = []

    def __init__(self, job_repo: JobRepository, job_entity_cls: Type[Job]):
        super().__init__()
        self.job_repo = job_repo
        self.job_entity_cls = job_entity_cls

    def run(self, job_data, include: Set[str] = None):
        """Create a new job"""
        if include is None:
            include = {'all'}  # Default to include all for create job

        # Create the job entity from the provided data
        job = self.job_entity_cls.create(**job_data)

        # Persist the job using the repository
        return self.job_repo.create_job(job, include=include)


