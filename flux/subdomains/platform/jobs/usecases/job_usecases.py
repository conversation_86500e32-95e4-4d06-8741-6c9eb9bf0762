"""
Use cases for Jobs domain
"""

from typing import Type

from flux.domains.common import UseCase
from flux.subdomains.platform.jobs.entities.job_entities import Job, JobNotFoundError
from flux.subdomains.platform.jobs.adapters.repositories.repository import JobRepo
from flux.subdomains.platform.jobs.usecases.schemas import (
    CreateJobSchema,
    GetJobByIdSchema,
    UpdateJobSchema,
    DeleteJobSchema,
)


class JobUseCase(UseCase):
    """Base use case for Job operations"""

    def __init__(self, job_repo: JobRepo, job_entity_cls: Type[Job]):
        super().__init__()
        self.job_repo = job_repo
        self.job_entity_cls = job_entity_cls


class CreateJobUseCase(JobUseCase):
    """Use case for creating a new job"""
    
    schema = [CreateJobSchema]
    raises = []

    def run(self, job_data):
        """Create a new job"""
        # Create the job entity from the provided data
        job = self.job_entity_cls.create(**job_data)
        
        # Persist the job using the repository
        persisted_job = self.job_repo.create_job(job)
        
        return persisted_job


class GetJobByIdUseCase(JobUseCase):
    """Use case for getting a job by ID"""
    
    schema = [GetJobByIdSchema]
    raises = [JobNotFoundError]

    def run(self, job_id):
        """Get a job by its ID"""
        return self.job_repo.get_job_by_id(job_id)


class UpdateJobUseCase(JobUseCase):
    """Use case for updating a job"""
    
    schema = [UpdateJobSchema]
    raises = [JobNotFoundError]

    def run(self, job_id, job_data):
        """Update an existing job"""
        # Get the existing job
        existing_job = self.job_repo.get_job_by_id(job_id)
        
        # Update the job with new data
        updated_job = existing_job.set_data(**job_data)
        
        # Save the updated job
        self.job_repo.save_job(updated_job)
        
        return updated_job


class DeleteJobUseCase(JobUseCase):
    """Use case for deleting a job"""
    
    schema = [DeleteJobSchema]
    raises = [JobNotFoundError]

    def run(self, job_id):
        """Delete a job by its ID"""
        # Verify the job exists before deleting
        self.job_repo.get_job_by_id(job_id)
        
        # Delete the job
        self.job_repo.delete_job(job_id)
        
        return {"success": True, "message": f"Job {job_id} deleted successfully"}
