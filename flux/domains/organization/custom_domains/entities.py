import uuid
from dataclasses import dataclass
from typing import Callable
from datetime import timezone

from dateutil.relativedelta import relativedelta
from marshmallow import validate

from flux.domains.common import Entity, EntitySchema, MatchOnAttribute, SchemaEntityCollection, ef
from flux.domains.event.commons.entities import CreatedAtMixin, SimpleEvent, UpdatedAtMixin
from flux.domains.event import config
from flux.domains.organization.organizations.entities import LicenseTypes

from flux.domains.organization.custom_domains.adapter import (
    CustomDomainAdapter,
    DNSRecordVerificationStatus,
    DNSRecordPurpose,
)
from flux.domains.organization.custom_domains.errors import (
    OrgCustomDomainInvalidError,
    OrgCustomDomainLimitReachedError,
    OrgCustomDomainNotFoundError,
    OrgCustomDomainContractRequiredError,
)
from flux.utils import enum, timeutils


class DomainVerificationStatus(enum.StringEnum):
    pending = enum.auto()
    validation_timed_out = enum.auto()
    verified = enum.auto()
    failed = enum.auto()
    expired = enum.auto()


class DNSRecordType(enum.StringEnum):
    cname = enum.auto()


class ACMCertificateStatus(enum.StringEnum):
    PENDING_VALIDATION = enum.auto()
    VALIDATION_TIMED_OUT = enum.auto()
    FAILED = enum.auto()
    ISSUED = enum.auto()


class OrgCustomDomainDNSRecord(Entity):

    DNS_RECORD_STATE_TRANSITION = {
        DNSRecordVerificationStatus.CORRECT: {
            DomainVerificationStatus.pending: DomainVerificationStatus.verified,
            DomainVerificationStatus.verified: DomainVerificationStatus.verified,
            DomainVerificationStatus.expired: DomainVerificationStatus.expired,
            DomainVerificationStatus.failed: DomainVerificationStatus.verified,
        },
        DNSRecordVerificationStatus.DOES_NOT_EXIST: {
            DomainVerificationStatus.pending: DomainVerificationStatus.pending,
            DomainVerificationStatus.verified: DomainVerificationStatus.failed,
            DomainVerificationStatus.failed: DomainVerificationStatus.failed,
            DomainVerificationStatus.expired: DomainVerificationStatus.expired,
        },
        DNSRecordVerificationStatus.INCORRECT: {
            DomainVerificationStatus.pending: DomainVerificationStatus.failed,
            DomainVerificationStatus.verified: DomainVerificationStatus.failed,
            DomainVerificationStatus.expired: DomainVerificationStatus.expired,
            DomainVerificationStatus.failed: DomainVerificationStatus.failed,
        },
    }

    match_on = MatchOnAttribute(
        [
            'host',
        ]
    )

    @dataclass
    class Dependencies:
        custom_domain_adapter: CustomDomainAdapter

    deps: Dependencies

    class Schema(EntitySchema):
        type = ef.Enum(
            enum=DNSRecordType, load_default=DNSRecordType.cname, metadata={'readonly': True}
        )
        host = ef.String(validate=validate.Length(min=1, max=255))
        value = ef.String(validate=validate.Length(min=1, max=255))
        purpose = ef.Enum(enum=DNSRecordPurpose, load_only=True, metadata={'immutable': True})
        status = ef.Enum(
            enum=DomainVerificationStatus,
            load_default=DomainVerificationStatus.pending,
        )

    def verify_dns(self):
        """
        Verify the DNS record.
        """
        dns_record_status = self.deps.custom_domain_adapter.verify_dns_record(
            host=self.host, value=self.value
        )
        status = self.DNS_RECORD_STATE_TRANSITION.get(
            dns_record_status, DNSRecordVerificationStatus.DOES_NOT_EXIST
        ).get(self.status)
        return self.set_data(status=status)


def validate_domain_name(domain_name: str):
    """
    Validate that the domain name is the subdomain and not the Apex domain.
    """
    if len(domain_name.split('.')) < 3:
        raise OrgCustomDomainInvalidError
    return domain_name


class OrgCustomDomain(UpdatedAtMixin, CreatedAtMixin, Entity):

    match_on = MatchOnAttribute(
        [
            'id',
        ]
    )

    @dataclass
    class Dependencies:
        custom_domain_adapter: CustomDomainAdapter
        config_cursor: Callable
        get_allowed_custom_domain_count: Callable
        get_configured_custom_domain_count: Callable
        get_active_contract: Callable
        get_organization: Callable

    deps: Dependencies

    class Schema(UpdatedAtMixin.Schema, CreatedAtMixin.Schema, EntitySchema):
        id = ef.ID(load_default=uuid.uuid4, metadata={'readonly': True})
        organization_id = ef.UUID(required=True, metadata={'immutable': True}, load_only=True)
        domain_name = ef.WebDomain(
            required=True, metadata={'immutable': True}, validate=validate_domain_name
        )
        certificate_arn = ef.String(load_default=None, load_only=True)
        cloudfront_id = ef.String(load_default=None, load_only=True)
        cloudfront_arn = ef.String(load_default=None, load_only=True)
        cloudfront_domain = ef.String(load_default=None, load_only=True)
        verification_status = ef.Enum(
            enum=DomainVerificationStatus, load_default=DomainVerificationStatus.pending
        )
        failure_reason = ef.String(load_default=None)
        resources_removed = ef.Bool(load_default=False, load_only=True)
        expired_at = ef.DateTime(load_default=None)
        deleted_at = ef.DateTime(load_default=None, load_only=True)
        root_domain_redirect_url = ef.URL(
            validate=validate.Length(min=1, max=255),
            load_default=None,
            allow_none=True,
            absolute=True,
            schemes=['https'],
        )
        is_validation_required = ef.Bool(load_default=False, load_only=True)
        failure_email_sent_at = ef.DateTime(load_default=None, load_only=True)

        dns_records = ef.Nested(
            OrgCustomDomainDNSRecord.Schema,
            load_default=list,
            many=True,
            metadata={'readonly': True},
        )
        linked_events = ef.Nested(SimpleEvent.Schema, load_default=list, many=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.STATE_TRANSITION_MAP = {
            ACMCertificateStatus.PENDING_VALIDATION.name: self._handle_pending_validation,
            ACMCertificateStatus.FAILED.name: self._handle_failed_certificate,
            ACMCertificateStatus.VALIDATION_TIMED_OUT.name: self._handle_validation_timeout,
            ACMCertificateStatus.ISSUED.name: self._handle_issued_certificate,
        }

    @classmethod
    def create(cls, **data):
        """
        1. Raise error when number of custom domains at org exceeds or equals the config value.
        2. Request SSL certificate for the domain name.
        3. Return the updated entity.
        """
        if data.get('domain_name'):
            data['domain_name'] = data['domain_name'].lower()

        base_entity = super().create(**data)

        # Get the count of allowed custom domains
        allowed_custom_domain_count = cls.deps.get_allowed_custom_domain_count(
            organization_id=base_entity.organization_id
        )

        # Get the number of custom domains configured at org level
        configured_custom_domain_count = cls.deps.get_configured_custom_domain_count(
            organization_id=base_entity.organization_id
        )

        if configured_custom_domain_count >= allowed_custom_domain_count:
            raise OrgCustomDomainLimitReachedError

        # If the license_type is user_credit, check for active contract
        org = cls.deps.get_organization(organization_id=base_entity.organization_id)
        if (
            org and org.license_type == LicenseTypes.user_credit and
            not cls.deps.get_active_contract(organization_id=base_entity.organization_id)
        ):
            raise OrgCustomDomainContractRequiredError

        # Request for the SSL certificate for the domain name
        ssl_cert = cls.deps.custom_domain_adapter.start_domain_setup(
            domain_name=base_entity.domain_name, organization_id=base_entity.organization_id
        )

        return base_entity.set_data(certificate_arn=ssl_cert)

    def update(self, **updates):

        if 'linked_events' in updates:
            current_linked_events = [e.id for e in self.linked_events]
            newly_linked_events = [e.id for e in updates['linked_events']]

            common = set(newly_linked_events).intersection(current_linked_events)
            events_to_enable = set(newly_linked_events).difference(common)
            events_to_disable = set(current_linked_events).difference(newly_linked_events)

            # Enable custom domain for the provided events.
            for event_id in events_to_enable:
                cc = self.deps.config_cursor(event_id)
                already_configured = cc.get(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED)
                # Can be enabled since it can be attached to other custom domain.
                if not already_configured:
                    cc.update(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, True)

            # Disable custom domain for provided events.
            for event_id in events_to_disable:
                cc = self.deps.config_cursor(event_id)
                cc.update(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, False)

        return super().update(**updates)

    def _disable_linked_events(self):
        """Turn off the event config so the event can't use the domain"""
        for e in self.linked_events:
            cc = self.deps.config_cursor(e.id)
            cc.update(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, False)

    def _get_dns_records(self):
        if len(self.dns_records) == 0:
            # Return empty dictionary if no dns records are present.
            return {}

        record1, record2 = self.dns_records
        if (
            record1.purpose == DNSRecordPurpose.certificate
            and record2.purpose == DNSRecordPurpose.domain
        ):
            return {DNSRecordPurpose.certificate: record1, DNSRecordPurpose.domain: record2}

        return {DNSRecordPurpose.certificate: record2, DNSRecordPurpose.domain: record1}

    def _handle_pending_validation(self, resource, user_initiated):
        dns_records = [dr.verify_dns() for dr in self.dns_records]
        new_state = {
            'verification_status': DomainVerificationStatus.pending,
            'is_validation_required': True,
            'dns_records': dns_records,
        }
        return self.set_data(**new_state)

    def _handle_failed_certificate(self, resource, user_initiated):
        # ACM certificate validation is failed.
        dns_records = [dr.verify_dns() for dr in self.dns_records]
        new_state = {
            'verification_status': DomainVerificationStatus.failed,
            'dns_records': dns_records,
            'failure_reason': resource.certificate.failure_reason,
        }

        return self.set_data(**new_state)

    def _handle_validation_timeout(self, resource, user_initiated):
        """
        Recreate the domain resources if requested by user.
        Otherwise the system will keep on recreating the resources if user forgot.
        In other cases, set the status to validation_timed_out so user request new records.
        """

        if not user_initiated:
            return self.set_data(verification_status=DomainVerificationStatus.validation_timed_out)

        if self.verification_status == DomainVerificationStatus.validation_timed_out:
            self.deps.custom_domain_adapter.delete_domain_resources(
                certificate_arn=self.certificate_arn
            )
            new_certificate = self.deps.custom_domain_adapter.start_domain_setup(
                domain_name=self.domain_name, organization_id=self.organization_id
            )
            new_state = {
                'verification_status': DomainVerificationStatus.pending,
                'dns_records': [],
                'is_validation_required': False,
                'certificate_arn': new_certificate,
                'resources_removed': False,
            }
            return self.set_data(**new_state)

        return self

    def _handle_issued_certificate(self, resource, user_initiated):
        dns_records = [dr.verify_dns() for dr in self.dns_records]
        updated_entity = self.set_data(dns_records=dns_records)
        dr = updated_entity._get_dns_records()
        domain_record = dr.get(DNSRecordPurpose.domain)
        certificate_record = dr.get(DNSRecordPurpose.certificate)

        status_actions = {
            DomainVerificationStatus.pending: lambda: updated_entity,
            DomainVerificationStatus.failed: lambda: updated_entity.set_data(
                verification_status=DomainVerificationStatus.failed
            ),
            DomainVerificationStatus.expired: lambda: updated_entity.set_data(
                verification_status=DomainVerificationStatus.expired,
                is_validation_required=False,
                dns_records=[
                    self.dns_records[0].set_data(status=DomainVerificationStatus.expired),
                    self.dns_records[1].set_data(status=DomainVerificationStatus.expired),
                ],
            ),
        }

        # Iterate through the statuses and execute the corresponding action if a match is found
        for status, action in status_actions.items():
            if status in (domain_record.status, certificate_record.status):
                return action()

        # Both DNS records are verified.
        distribution = self.deps.custom_domain_adapter.finalize_domain_setup(
            domain_cname=domain_record.value,
            domain_name=updated_entity.domain_name,
            organization_id=updated_entity.organization_id,
            certificate_arn=updated_entity.certificate_arn,
            distribution_id=updated_entity.cloudfront_id,
        )

        new_state = {
            'verification_status': DomainVerificationStatus.verified,
            'is_validation_required': False,
            'cloudfront_id': distribution.id,
            'cloudfront_arn': distribution.arn,
            'cloudfront_domain': distribution.domain_name,
        }

        return updated_entity.set_data(**new_state)

    def process_custom_domain(self, user_initiated=False):
        """
        1. If the DNS records are not yet generated -
           - Generate the DNS records and return
        2. If the ACM certificate status is PENDING_VALIDATION -
           - Check the DNS record status & cerfificate status
           - Return updated entity
        2. If the ACM certificate status is FAILED -
           - Mark status as failed, Update the dns record status and failure reason
           - Return updated entity
        3. If the ACM certificate status is VALIDATION_TIMED_OUT -
           - Delete and Request new certificate
           - Return updated entity
        4. If ACM certificate status is ISSUED -
           - Check the DNS record status & cerfificate status
           - If both are fine, provision custom domain resources.
           - Return updated entity
        """

        if (
            self.verification_status == DomainVerificationStatus.pending
            and len(self.dns_records) == 0
        ):
            return self.set_dns_records()

        # Get the SSL certificate status
        resource = self.deps.custom_domain_adapter.get_domain_resources(
            certificate_arn=self.certificate_arn
        )

        if not resource:
            return self

        certificate_status = resource.certificate.status

        # Find and execute the appropriate transition function
        if certificate_status in self.STATE_TRANSITION_MAP:
            return self.STATE_TRANSITION_MAP[certificate_status](resource, user_initiated)

        return self

    def set_dns_records(self):
        """
        If the status == pending and there are no dns records:
           a. Update the entity with the DNS records if they are generated.
        Otherwise return the entity as it is
        """
        if (
            self.verification_status == DomainVerificationStatus.pending
            and not self.is_validation_required
        ):

            # Get the DNS records generated
            records = self.deps.custom_domain_adapter.get_validation_records(
                custom_domain_id=self.id,
                organization_id=self.organization_id,
                certificate_arn=self.certificate_arn,
            )

            # Get the SSL certificate status
            resource = self.deps.custom_domain_adapter.get_domain_resources(
                certificate_arn=self.certificate_arn
            )

            if not resource:
                return self

            certificate_status = resource.certificate.status

            if len(records) == 0 and certificate_status == ACMCertificateStatus.FAILED.name:
                # Certificate in failed state without any dns records.
                return self._handle_failed_certificate(resource, False)

            if len(records) == 0:
                # Records are not generated yet
                return self

            custom_domain_dns_records = OrgCustomDomainDNSRecord.setup(
                custom_domain_adapter=self.deps.custom_domain_adapter
            )

            dns_records = []
            for record in records:
                dns_records.append(
                    custom_domain_dns_records.create(
                        host=record.host,
                        value=record.value,
                        status=DomainVerificationStatus.pending,
                        purpose=record.purpose,
                    )
                )

            return self.set_data(is_validation_required=True, dns_records=dns_records)

        return self

    def _should_mark_for_expiry(self, older_event_count):
        if len(self.linked_events) == 0:
            return timeutils.now() > self.created_at + relativedelta(months=6)
        return len(self.linked_events) == older_event_count

    def expire_custom_domain(self):
        """
        Custom domain should be expired -
        1. Older than 3 months.
        2. Not part of any active or upcoming events for at least 6 months.
        - Active event: today < event end date + post-event access days.
        """

        # Return if the custom domain is not older than 3 months.
        if self.created_at + relativedelta(months=3) > timeutils.now():
            return self

        current_date = timeutils.now().date()
        older_event_count = 0
        for event in self.linked_events:

            if event.start_date > current_date:
                # Custom domain is associated with an upcoming event
                return self

            cc = self.deps.config_cursor(event.id)
            post_event_access_date = cc.get(config.keys.EVENTAPP_POST_EVENT_ACCESS_DAYS)

            if post_event_access_date is None:
                post_event_access_date = 1

            if (
                event.start_date
                <= current_date
                < event.end_date + relativedelta(days=post_event_access_date)
            ):
                # Custom domain is part of an active event
                return self

            if current_date > event.end_date + relativedelta(
                days=post_event_access_date
            ) + relativedelta(months=6):
                older_event_count += 1

        mark_for_expiry = False
        mark_for_expiry = self._should_mark_for_expiry(older_event_count)

        if mark_for_expiry:
            if self.cloudfront_id:
                self.deps.custom_domain_adapter.disable_domain_resources(
                    distribution_id=self.cloudfront_id
                )

            self._disable_linked_events()
            new_state = {
                'verification_status': DomainVerificationStatus.expired,
                'dns_records': [
                    dr.set_data(status=DomainVerificationStatus.expired) for dr in self.dns_records
                ],
                'is_validation_required': False,
                'expired_at': timeutils.now(),
            }
            return self.set_data(**new_state)

        return self

    def disconnect_custom_domain(self):
        """
        Custom domains in the `verified` state should be disconnected
        when they update / remove the DNS records.
        """
        if self.verification_status == DomainVerificationStatus.verified:

            # Check the status of the DNS records
            dns_records = []
            verification_status = DomainVerificationStatus.verified

            for dns_record in self.dns_records:
                updated_dns_record = dns_record.verify_dns()
                if updated_dns_record.status == DomainVerificationStatus.failed:
                    verification_status = DomainVerificationStatus.failed
                dns_records.append(updated_dns_record)

            resource = self.deps.custom_domain_adapter.get_domain_resources(
                certificate_arn=self.certificate_arn
            )

            if resource.certificate.status != ACMCertificateStatus.ISSUED.name:
                verification_status = DomainVerificationStatus.failed

            if verification_status == DomainVerificationStatus.failed:
                # Turn-off config for linked events
                self._disable_linked_events()

                # Disable custom domain resources
                self.deps.custom_domain_adapter.disable_domain_resources(
                    distribution_id=self.cloudfront_id
                )

                return self.set_data(
                    verification_status=verification_status,
                    dns_records=dns_records,
                    is_validation_required=True,
                )

        return self

    def delete_custom_domain(self, user_initiated=False):
        """
        1. There are two ways by which the custom domain is deleted -
        - User initiated: In this case, the custom domain should be deleted straight away.
        - System Initiated: In this case, the custom domain must be in expired state and
                            and expired for a month at least.
        """

        if not user_initiated:

            # Only expired domains should be deleted.
            if (
                self.expired_at is None
                or self.verification_status != DomainVerificationStatus.expired
            ):
                return False

            # Return if the custom domain is not expired for more than a month.
            if (
                self.expired_at is not None
                and self.expired_at.replace(tzinfo=timezone.utc) + relativedelta(months=1)
                > timeutils.now()
            ):
                return False

        return True

    def delete_custom_domain_resources(self):
        dr = self._get_dns_records()
        domain_cname = None
        if dr.get(DNSRecordPurpose.domain):
            domain_record = dr.get(DNSRecordPurpose.domain)
            domain_cname = domain_record.value

        distribution_id = None
        if self.cloudfront_id:
            distribution_id = self.cloudfront_id

        resources_removed = self.deps.custom_domain_adapter.delete_domain_resources(
            certificate_arn=self.certificate_arn,
            domain_cname=domain_cname,
            distribution_id=distribution_id,
        )

        new_state = {'resources_removed': resources_removed}
        return self.set_data(**new_state)


class OrgCustomDomainLinkedEvent(UpdatedAtMixin, CreatedAtMixin, Entity):

    match_on = MatchOnAttribute(
        [
            'id',
        ]
    )

    class Schema(UpdatedAtMixin.Schema, CreatedAtMixin.Schema, EntitySchema):
        id = ef.ID(load_default=uuid.uuid4, metadata={'readonly': True})
        custom_domain_id = ef.UUID(required=True)
        event_id = ef.Int(required=True, metadata={'immutable': True})


class OrgCustomDomainLinkedEventCollection(SchemaEntityCollection):
    CollectionEntity = OrgCustomDomainLinkedEvent


class OrgCustomDomainCollection(SchemaEntityCollection):
    CollectionEntity = OrgCustomDomain
    EntityNotFoundError = OrgCustomDomainNotFoundError


# Response Entities
class OrgCustomDomainRedirect(Entity):
    class Schema(EntitySchema):
        cd_redirect_needed = ef.Bool(required=True)
        cd_redirect_url = ef.URL(allow_none=True, absolute=True, schemes=['https'])


class OrgCustomDomainLookup(Entity):
    class Schema(EntitySchema):
        root_domain_redirect_url = ef.URL(allow_none=True, absolute=True, schemes=['https'])
        linked_events = ef.List(ef.Dict())
