"""Add table for copilot message referenced entities

Revision ID: c1c9b8f1cb98
Revises: b825be90c0b6
Create Date: 2025-06-30 00:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from flux.adapters.db import UUID

# revision identifiers, used by Alembic.
revision = 'c1c9b8f1cb98'
down_revision = 'b825be90c0b6'


def upgrade():
    op.create_table(
        'copilot_message_referenced_entities',
        sa.Column('id', UUID, primary_key=True),
        sa.Column('message_id', UUID, nullable=False, index=True),
        sa.Column('entity_type', sa.VARCHAR(length=50), nullable=False),
        sa.Column('entity_id', UUID, nullable=False),
        sa.Column(
            'created_at',
            sa.dialects.mysql.DATETIME(fsp=6),
            nullable=False,
            server_default=sa.text('CURRENT_TIMESTAMP(6)'),
            index=True,
        ),
        mysql_engine='InnoDB',
        mysql_charset='utf8mb4',
    )
    op.create_index(
        'ix_copilot_ref_entities_message',
        'copilot_message_referenced_entities',
        ['message_id'],
    )
    op.create_unique_constraint(
        'uix_copilot_msg_ref_entities',
        'copilot_message_referenced_entities',
        ['message_id', 'entity_type', 'entity_id'],
    )


def downgrade():
    op.drop_table('copilot_message_referenced_entities')

