"""Create jobs and job_tasks tables for async job management

Revision ID: d2e3f4a5b6c7
Revises: c1c9b8f1cb98
Create Date: 2025-06-28 00:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from flux.adapters.db import UUID, JSON, AwareDateTime, ProperEnum
from flux.subdomains.platform.jobs.entities.job_entities import ContextBoundaryTypes

# revision identifiers, used by Alembic.
revision = 'd2e3f4a5b6c7'
down_revision = 'c1c9b8f1cb98'


def upgrade():
    # Create jobs table
    op.create_table(
        'jobs',
        sa.Column('id', UUID, primary_key=True),
        sa.Column('name', sa.VARCHAR(255), nullable=False),
        sa.Column('status', sa.VARCHAR(50), nullable=False, index=True),
        sa.Column('started_at', AwareDateTime, nullable=True, index=True),
        sa.Column('completed_at', AwareDateTime, nullable=True, index=True),
        sa.Column('message', JSON, nullable=True),
        sa.Column('context_type', sa.VARCHAR(36), nullable=False),
        sa.Column('context_id', sa.VARCHAR(36), nullable=False, index=True),
        sa.Column('created_by', sa.Integer, nullable=True, index=True),
        sa.Column('created_at', sa.dialects.mysql.DATETIME(fsp=6), 
                  nullable=False, 
                  server_default=sa.text('CURRENT_TIMESTAMP'),
                  index=True),
        sa.Column('updated_at', sa.dialects.mysql.DATETIME(fsp=6),
                   nullable=False,
                   server_default=sa.text('CURRENT_TIMESTAMP')),
        mysql_engine='InnoDB',
        mysql_charset='utf8mb4'
    )

    # Create job_tasks table
    op.create_table(
        'job_tasks',
        sa.Column('id', UUID, primary_key=True),
        sa.Column('job_id', UUID, nullable=False, index=True),
        sa.Column('name', sa.VARCHAR(255), nullable=False),
        sa.Column('status', sa.VARCHAR(50), nullable=False, index=True),
        sa.Column('started_at', AwareDateTime, nullable=True, index=True),
        sa.Column('completed_at', AwareDateTime, nullable=True, index=True),
        sa.Column('message', JSON, nullable=True),
        sa.Column('task_meta', JSON, nullable=True),
        sa.Column('chunk_index', sa.Integer, nullable=True),  # Renamed from chunk_count to chunk_index
        sa.Column('is_callback', sa.Boolean, nullable=False, default=False),
        sa.Column('created_at', sa.dialects.mysql.DATETIME(fsp=6), 
                  nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.dialects.mysql.DATETIME(fsp=6), nullable=False,
                   server_default=sa.text('CURRENT_TIMESTAMP')),
        mysql_engine='InnoDB',
        mysql_charset='utf8mb4'
    )

    # Create foreign key constraints
    op.create_foreign_key(
        'fk_job_tasks_job_id',
        'job_tasks',
        'jobs',
        ['job_id'],
        ['id'],
        ondelete='CASCADE'
    )

    op.create_foreign_key(
        'fk_jobs_created_by',
        'jobs',
        'user_accounts',
        ['created_by'],
        ['id'],
        ondelete='SET NULL'
    )




def downgrade():
    # Drop foreign key constraints
    op.drop_constraint('fk_job_tasks_job_id', 'job_tasks', type_='foreignkey')
    op.drop_constraint('fk_jobs_created_by', 'jobs', type_='foreignkey')

    # Drop tables (indexes are dropped automatically with the tables)
    op.drop_table('job_tasks')
    op.drop_table('jobs')
