"""Create jobs and job_tasks tables for async job management

Revision ID: d2e3f4a5b6c7
Revises: c1c9b8f1cb98
Create Date: 2025-06-28 00:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from flux.adapters.db import UUID, JSON

# revision identifiers, used by Alembic.
revision = 'd2e3f4a5b6c7'
down_revision = 'c1c9b8f1cb98'


def upgrade():
    # Create jobs table
    op.create_table(
        'jobs',
        sa.Column('id', UUID, primary_key=True),
        sa.Column('name', sa.VARCHAR(255), nullable=True),  # Changed to nullable=True
        sa.Column('status', sa.VARCHAR(50), nullable=False),
        sa.Column('started_at', sa.dialects.mysql.DATETIME(fsp=6), 
                  nullable=False, 
                  server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('completed_at', sa.dialects.mysql.DATETIME(fsp=6), 
                  nullable=False, 
                  server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('message', JSON, nullable=True),
        sa.Column('context_type', sa.VARCHAR(50), nullable=True),  # Changed to nullable=True
        sa.Column('context_id', sa.VARCHAR(100), nullable=True),  # Changed to VARCHAR and nullable=True
        sa.Column('created_by', sa.Integer, nullable=True),  # Changed to Integer and nullable=True
        sa.Column('created_at', sa.dialects.mysql.DATETIME(fsp=6), 
                  nullable=False, 
                  server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.dialects.mysql.DATETIME(fsp=6),
                   nullable=False,
                   server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        mysql_engine='InnoDB',
        mysql_charset='utf8mb4'
    )

    # Create job_tasks table
    op.create_table(
        'job_tasks',
        sa.Column('id', UUID, primary_key=True),
        sa.Column('job_id', UUID, nullable=False),
        sa.Column('name', sa.VARCHAR(255), nullable=True),  # Changed to nullable=True
        sa.Column('status', sa.VARCHAR(50), nullable=False),
        sa.Column('started_at', sa.DateTime, nullable=True),
        sa.Column('completed_at', sa.DateTime, nullable=True),
        sa.Column('message', JSON, nullable=True),
        sa.Column('task_meta', JSON, nullable=True),
        sa.Column('chunk_index', sa.Integer, nullable=True),  # Renamed from chunk_count to chunk_index
        sa.Column('is_callback', sa.Boolean, nullable=False, default=False),
        sa.Column('created_at', sa.dialects.mysql.DATETIME(fsp=6), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.dialects.mysql.DATETIME(fsp=6), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        mysql_engine='InnoDB',
        mysql_charset='utf8mb4'
    )

    # Create foreign key constraints
    op.create_foreign_key(
        'fk_job_tasks_job_id',
        'job_tasks',
        'jobs',
        ['job_id'],
        ['id'],
        ondelete='CASCADE'
    )

    op.create_foreign_key(
        'fk_jobs_created_by',
        'jobs',
        'user_accounts',
        ['created_by'],
        ['id'],
        ondelete='SET NULL'
    )

    # Create indexes for better query performance (matching current models)
    op.create_index('idx_jobs_context_id', 'jobs', ['context_id'])
    op.create_index('idx_jobs_status', 'jobs', ['status'])
    op.create_index('idx_jobs_started_at', 'jobs', ['started_at'])
    op.create_index('idx_jobs_completed_at', 'jobs', ['completed_at'])
    op.create_index('idx_jobs_created_by', 'jobs', ['created_by'])

    op.create_index('idx_job_tasks_job_id', 'job_tasks', ['job_id'])
    op.create_index('idx_job_tasks_status', 'job_tasks', ['status'])
    op.create_index('idx_job_tasks_started_at', 'job_tasks', ['started_at'])
    op.create_index('idx_job_tasks_completed_at', 'job_tasks', ['completed_at'])


def downgrade():
    # Drop indexes first
    op.drop_index('idx_job_tasks_completed_at', 'job_tasks')
    op.drop_index('idx_job_tasks_started_at', 'job_tasks')
    op.drop_index('idx_job_tasks_status', 'job_tasks')
    op.drop_index('idx_job_tasks_job_id', 'job_tasks')

    op.drop_index('idx_jobs_completed_at', 'jobs')
    op.drop_index('idx_jobs_started_at', 'jobs')
    op.drop_index('idx_jobs_status', 'jobs')
    op.drop_index('idx_jobs_created_by', 'jobs')
    op.drop_index('idx_jobs_context_id', 'jobs')

    # Drop foreign key constraints
    op.drop_constraint('fk_job_tasks_job_id', 'job_tasks', type_='foreignkey')
    op.drop_constraint('fk_jobs_created_by', 'jobs', type_='foreignkey')

    # Drop tables
    op.drop_table('job_tasks')
    op.drop_table('jobs')
