#!/usr/bin/env python3
"""
Quick test to verify timestamp compatibility between Job models and entities
"""
import sys
import os
from datetime import datetime
import pytz

# Add the flux directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

# Test just the timestamp utility functions first
from flux.utils.timeutils import make_timezone_aware


def test_timestamp_functions():
    """Test that timestamp utility functions work correctly"""

    # Create test timestamps
    naive_dt = datetime(2023, 12, 25, 10, 30, 0)  # Naive datetime
    utc_dt = datetime(2023, 12, 25, 10, 30, 0, tzinfo=pytz.UTC)  # UTC datetime

    print("=== Testing make_timezone_aware function ===")
    print(f"Naive datetime: {naive_dt} (tzinfo: {naive_dt.tzinfo})")

    # Test making naive datetime timezone aware
    aware_dt = make_timezone_aware(naive_dt)
    print(f"Made aware: {aware_dt} (tzinfo: {aware_dt.tzinfo})")

    # Test with already aware datetime
    already_aware = make_timezone_aware(utc_dt)
    print(f"Already aware: {already_aware} (tzinfo: {already_aware.tzinfo})")

    # Test with None
    none_result = make_timezone_aware(None) if None else "None handled correctly"
    print(f"None handling: {none_result}")

    print("\n=== Testing timestamp compatibility patterns ===")

    # Simulate the pattern used in our models
    def simulate_model_to_entity_conversion(model_timestamp):
        """Simulate how our models convert timestamps to entities"""
        if model_timestamp:
            return make_timezone_aware(model_timestamp)
        return None

    def simulate_entity_to_model_conversion(entity_timestamp):
        """Simulate how our models store timestamps from entities"""
        # Models typically store timestamps as-is
        return entity_timestamp

    # Test the roundtrip
    original_naive = datetime(2023, 12, 25, 15, 45, 30)
    print(f"1. Original naive timestamp: {original_naive} (tzinfo: {original_naive.tzinfo})")

    # Entity -> Model (direct assignment)
    model_timestamp = simulate_entity_to_model_conversion(original_naive)
    print(f"2. Stored in model: {model_timestamp} (tzinfo: {model_timestamp.tzinfo})")

    # Model -> Entity (make timezone aware)
    entity_timestamp = simulate_model_to_entity_conversion(model_timestamp)
    print(f"3. Converted to entity: {entity_timestamp} (tzinfo: {entity_timestamp.tzinfo})")

    # Test with timezone-aware input
    original_aware = make_timezone_aware(datetime(2023, 12, 25, 15, 45, 30))
    print(f"\n4. Original aware timestamp: {original_aware} (tzinfo: {original_aware.tzinfo})")

    model_timestamp_2 = simulate_entity_to_model_conversion(original_aware)
    print(f"5. Stored in model: {model_timestamp_2} (tzinfo: {model_timestamp_2.tzinfo})")

    entity_timestamp_2 = simulate_model_to_entity_conversion(model_timestamp_2)
    print(f"6. Converted to entity: {entity_timestamp_2} (tzinfo: {entity_timestamp_2.tzinfo})")



if __name__ == "__main__":
    test_timestamp_functions()
