import uuid
import dns.resolver
import boto3

from dateutil.relativedelta import relativedelta
from unittest.mock import Mock, patch, call
from assertpy import assert_that
from moto.moto_api import state_manager

from test.utils.custom_asserts import raises
from test.utils.uapi.test_case import AppTestCase
from test.utils.data_fixtures import factories
from test.utils.mocks.mock_app_config import override_app_config

from flux.apps.config import app_config
from flux.utils import timeutils
from flux.adapters.clients.acm import ACM, ACMAPIError
from flux.adapters.clients.cloudfront import (
    CloudFrontDistributionStatus,
    CloudFront,
)
from flux.adapters.clients.s3lib import S3Lib
from flux.domains.common.errors import AuthorizationError
from flux.domains.common.entity import ProductTypes

from flux.domains.event import config
from flux.domains.organization.organizations.config_keys import ALLOWED_CUSTOM_DOMAIN_COUNT_CONFIG
from flux.domains.organization.organizations.entities import LicenseTypes
from flux.domains.organization.organizations.models import (
    OrganizationConfigurationKeyTable,
    OrganizationConfigurationTable,
)
from flux.adapters.custom_domains.aws_custom_domain_adapter import CustomDomainResource
from flux.domains.organization.custom_domains.entities import (
    DomainVerificationStatus,
    ACMCertificateStatus,
)
from flux.domains.organization.custom_domains.errors import (
    OrgCustomDomainInvalidError,
    OrgCustomDomainAlreadyExistError,
    OrgCustomDomainLimitReachedError,
    OrgCustomDomainNotFoundError,
    OrgCustomDomainUnverifiedError,
    OrgCustomDomainEventAlreadyLinked,
    OrgCustomDomainContractRequiredError,
)
from flux.domains.organization.custom_domains.models import (
    OrganizationCustomDomainsModel,
)

from flux.domains.organization import custom_domains


@AppTestCase.add_patch('boto3')
@AppTestCase.add_patch('sendgrid')
@AppTestCase.add_patch('sendgrid_usecase')
class CustomDomainUseCasesTests(AppTestCase):

    def setUp(self):
        super().setUp()
        self.organization = factories.create_organization(name='EventMobi', shortcode='eventmobi')

        factories.create_contract(
            org_id=self.organization.id,
            start_date=timeutils.now(),
            end_date=timeutils.now() + relativedelta(years=1),
        )

        self.acm_client = ACM()
        self.cloudfront = CloudFront()
        s3 = boto3.client('s3')
        s3.create_bucket(Bucket=app_config.S3_PRIVATE_BUCKET_NAME)
        self.s3lib = S3Lib(bucket_name=app_config.S3_PRIVATE_BUCKET_NAME)
        self.cloudfront_client = boto3.client('cloudfront')
        self.route53_client = boto3.client('route53')
        self.mock_sg = self.patches.sendgrid.mock_sendgrid_client
        self.mock_email_client = self.patches.sendgrid_usecase

    def test_create_custom_domain_admin_organizer(self):
        self.init_admin_organizer(org_id=self.organization.id)

        custom_domain = 'test.eventmobi.com'
        domain = custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=self.organization.id
        )

        m = list(OrganizationCustomDomainsModel.all())
        assert_that(m).is_length(1)

        # Should not raise any error
        self.acm_client.describe_certificate(domain.certificate_arn)

        assert_that(domain.domain_name).is_equal_to(custom_domain)
        assert_that(domain.organization_id).is_equal_to(self.organization.id)
        assert_that(domain.certificate_arn).is_not_none()
        assert_that(domain.verification_status).is_equal_to(DomainVerificationStatus.pending)
        assert_that(domain.root_domain_redirect_url).is_none()
        assert_that(domain.expired_at).is_none()
        assert_that(domain.cloudfront_id).is_none()
        assert_that(domain.cloudfront_arn).is_none()
        assert_that(domain.cloudfront_domain).is_none()
        assert_that(domain.is_validation_required).is_false()
        assert_that(domain.failure_email_sent_at).is_none()
        assert_that(domain.dns_records).is_length(0)
        assert_that(domain.linked_events).is_length(0)

    @raises(OrgCustomDomainInvalidError)
    def test_create_custom_domain_admin_organizer_invalid_subdomain(self):
        self.init_admin_organizer(org_id=self.organization.id)
        custom_domain = 'eventmobi.com'
        custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=self.organization.id
        )

    @raises(OrgCustomDomainAlreadyExistError)
    def test_create_custom_domain_admin_organizer_already_exists(self):
        self.init_admin_organizer(org_id=self.organization.id)

        # Update org config to allow more custom domains
        m = OrganizationConfigurationTable(
            key_id=ALLOWED_CUSTOM_DOMAIN_COUNT_CONFIG,
            organization_id=self.organization.id,
            value='2',
            created_at=timeutils.now(),
        )
        OrganizationConfigurationTable.save(m)

        custom_domain = 'test.eventmobi.com'
        factories.create_org_custom_domain(
            organization_id=self.organization.id, domain_name=custom_domain
        )

        custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=self.organization.id
        )

    @raises(OrgCustomDomainLimitReachedError)
    def test_create_custom_domain_admin_organizer_limit_reached(self):
        self.init_admin_organizer(org_id=self.organization.id)
        factories.create_org_custom_domain(
            organization_id=self.organization.id, domain_name='other.eventmobi.com'
        )
        custom_domain = 'test.eventmobi.com'
        custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=self.organization.id
        )

    def test_create_custom_domain_admin_organizer_exists_deleted(self):
        self.init_admin_organizer(org_id=self.organization.id)
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id, domain_name='test.eventmobi.com'
        )
        # Mark it as deleted
        cd.deleted_at = timeutils.now()
        OrganizationCustomDomainsModel.save(cd)

        custom_domain = 'test.eventmobi.com'
        custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=self.organization.id
        )

    @raises(OrgCustomDomainContractRequiredError)
    def test_create_custom_domain_admin_organizer_no_active_contract(self):
        o = factories.create_organization(
            name='New Org', shortcode='new-org', license_type=LicenseTypes.user_credit
        )
        self.init_admin_organizer(org_id=o.id)
        custom_domain = 'test.eventmobi.com'
        custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=o.id
        )

    def test_create_custom_domain_admin_organizer_event_credit(self):
        o = factories.create_organization(
            name='New Org', shortcode='new-org', license_type=LicenseTypes.event_credit
        )
        factories.create_org_conf(
            key_id=ALLOWED_CUSTOM_DOMAIN_COUNT_CONFIG,
            organization_id=o.id,
            value='5',
        )
        self.init_admin_organizer(org_id=o.id)
        custom_domain = 'test.eventmobi.com'
        custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=o.id
        )

    @raises(AuthorizationError)
    def test_create_custom_domain_collaborator_organizer(self):
        self.init_collaborator_organizer(org_id=self.organization.id)
        custom_domain = 'test.eventmobi.com'
        custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=self.organization.id
        )

    def test_list_custom_domain_admin_organizer(self):
        self.init_admin_organizer(org_id=self.organization.id)
        domains = [
            'admin.eventmobi.com',
            'es.eventmobi.com',
            'reg.eventmobi.com',
            'other.eventmobi.com',
        ]

        for d in domains:
            cd = factories.create_org_custom_domain(
                organization_id=self.organization.id,
                domain_name=d,
                verification_status=DomainVerificationStatus.verified,
            )

        # Mark last one as deleted
        cd.deleted_at = timeutils.now()
        OrganizationCustomDomainsModel.save(cd)

        domain_list = custom_domains.get_by_organization(
            self.context, organization_id=self.organization.id
        )
        assert_that(domain_list).is_length(3)
        assert_that([d.domain_name for d in domain_list]).is_equal_to(domains[:3])

    def test_list_custom_domain_admin_organizer_filter_domains(self):
        self.init_admin_organizer(org_id=self.organization.id)

        domains = [
            {'name': 'es.eventmobi.com', 'status': DomainVerificationStatus.expired},
            {'name': 'reg.eventmobi.com', 'status': DomainVerificationStatus.failed},
            {'name': 'other.eventmobi.com', 'status': DomainVerificationStatus.pending},
            {'name': 'admin.eventmobi.com', 'status': DomainVerificationStatus.verified},
        ]

        for domain in domains:
            factories.create_org_custom_domain(
                organization_id=self.organization.id,
                domain_name=domain['name'],
                verification_status=domain['status'],
            )

        domain_list = custom_domains.get_by_organization(
            self.context,
            organization_id=self.organization.id,
            verification_status=[
                DomainVerificationStatus.expired,
                DomainVerificationStatus.failed,
            ],
        )
        assert_that(domain_list).is_length(2)
        assert_that([d.domain_name for d in domain_list]).is_equal_to(
            ['es.eventmobi.com', 'reg.eventmobi.com']
        )

    def test_list_custom_domain_admin_organizer_filter_by_event_id(self):
        self.init_admin_organizer(org_id=self.organization.id)

        domains = [
            {'name': 'es.eventmobi.com', 'status': DomainVerificationStatus.verified},
            {'name': 'reg.eventmobi.com', 'status': DomainVerificationStatus.verified},
            {'name': 'other.eventmobi.com', 'status': DomainVerificationStatus.failed},
            {'name': 'admin.eventmobi.com', 'status': DomainVerificationStatus.expired},
        ]

        events = []
        e = factories.create_event(organization_id=self.organization.id, shortcode='dummy')
        events.append(e.id)
        for domain in domains:
            cd = factories.create_org_custom_domain(
                organization_id=self.organization.id,
                domain_name=domain['name'],
                verification_status=domain['status'],
            )

            if domain['status'] == DomainVerificationStatus.verified:
                e = factories.create_event(
                    organization_id=self.organization.id, shortcode=str(uuid.uuid4())
                )
                factories.create_org_custom_domain_linked_event(cd.id, e.id)
                events.append(e.id)

        domain_list = custom_domains.get_by_organization(
            self.context,
            organization_id=self.organization.id,
            linked_event_id=events,
        )
        assert_that(domain_list).is_length(2)
        assert_that([d.domain_name for d in domain_list]).is_equal_to(
            ['es.eventmobi.com', 'reg.eventmobi.com']
        )

    def test_list_custom_domain_admin_collaborator(self):
        self.init_admin_organizer(org_id=self.organization.id)
        domains = [
            'admin.eventmobi.com',
            'es.eventmobi.com',
            'reg.eventmobi.com',
            'other.eventmobi.com',
        ]

        for d in domains:
            cd = factories.create_org_custom_domain(
                organization_id=self.organization.id,
                domain_name=d,
                verification_status=DomainVerificationStatus.verified,
            )

        # Mark last one as deleted
        cd.deleted_at = timeutils.now()
        OrganizationCustomDomainsModel.save(cd)

        domain_list = custom_domains.get_by_organization(
            self.context, organization_id=self.organization.id
        )
        assert_that(domain_list).is_length(3)
        assert_that([d.domain_name for d in domain_list]).is_equal_to(domains[:3])

    def test_count_custom_domain(self):
        domains = [
            'admin.eventmobi.com',
            'es.eventmobi.com',
            'reg.eventmobi.com',
            'other.eventmobi.com',
        ]

        for d in domains:
            factories.create_org_custom_domain(
                organization_id=self.organization.id,
                domain_name=d,
                verification_status=DomainVerificationStatus.verified,
            )

        c = custom_domains.count(self.context, organization_id=self.organization.id)
        assert_that(c).is_equal_to(len(domains))

    def test_get_custom_domain_by_id_admin_organizer(self):
        self.init_admin_organizer(org_id=self.organization.id)
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
        )

        domain = custom_domains.get_by_id(
            self.context, custom_domain_id=cd.id, organization_id=self.organization.id
        )

        assert_that(cd.domain_name).is_equal_to(domain.domain_name)

    @raises(OrgCustomDomainNotFoundError)
    def test_get_custom_domain_by_id_admin_organizer_not_found(self):
        self.init_admin_organizer(org_id=self.organization.id)
        custom_domains.get_by_id(
            self.context, custom_domain_id=uuid.uuid4(), organization_id=self.organization.id
        )

    def test_get_custom_domain_by_id_collaborator_organizer(self):
        self.init_collaborator_organizer(org_id=self.organization.id)
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
        )

        domain = custom_domains.get_by_id(
            self.context, custom_domain_id=cd.id, organization_id=self.organization.id
        )

        assert_that(cd.domain_name).is_equal_to(domain.domain_name)

    def test_get_custom_domain_by_event_id(self):
        self.init_admin_organizer(org_id=self.organization.id)
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
        )

        e = factories.create_event(organization_id=self.organization.id)
        factories.create_org_custom_domain_linked_event(cd.id, e.id)

        domain = custom_domains.get_by_event_id(self.context, event_id=e.id)

        assert_that(cd.domain_name).is_equal_to(domain.domain_name)

    @raises(OrgCustomDomainNotFoundError)
    def test_get_custom_domain_by_event_id_unverified(self):
        self.init_admin_organizer(org_id=self.organization.id)
        factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.pending,
        )

        e = factories.create_event(organization_id=self.organization.id)
        custom_domains.get_by_event_id(self.context, event_id=e.id)

    @raises(OrgCustomDomainNotFoundError)
    def test_get_custom_domain_by_event_id_not_found(self):
        self.init_admin_organizer(org_id=self.organization.id)
        factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
        )

        custom_domains.get_by_event_id(self.context, event_id=10)

    def test_delete_custom_domain_not_verified_admin_organizer(self):
        self.init_admin_organizer(org_id=self.organization.id)
        custom_domain = 'test.eventmobi.com'
        domain = custom_domains.create(
            self.context, custom_domain=custom_domain, organization_id=self.organization.id
        )

        certificates = self.acm_client.list_certificates()
        domains = custom_domains.get_by_organization(
            self.context, organization_id=self.organization.id
        )
        assert_that(certificates['CertificateSummaryList']).is_length(1)
        assert_that(domains).is_length(1)

        custom_domains.delete_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        certificates = self.acm_client.list_certificates()
        domains = custom_domains.get_by_organization(
            self.context, organization_id=self.organization.id
        )
        assert_that(certificates['CertificateSummaryList']).is_length(0)
        assert_that(domains).is_length(0)

    @raises(AuthorizationError)
    def test_delete_custom_domain_not_verified_collaborator_organizer(self):
        self.init_collaborator_organizer(org_id=self.organization.id)
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
        )
        custom_domains.delete_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=cd.id
        )

    def test_link_event_custom_domain_admin_organizer_no_prev_events(self):
        self.init_admin_organizer(org_id=self.organization.id)
        custom_domain = 'test.eventmobi.com'
        root_domain_redirect_url = 'https://another-endpoint.example.com/'
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name=custom_domain,
            verification_status=DomainVerificationStatus.verified,
        )

        codes = ['event1', 'event2', 'event3']
        events = []
        for c in codes:
            e = factories.create_event(organization_id=self.organization.id, shortcode=c)
            factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '0')
            events.append(e)

        updated_domain = custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=cd.id,
            root_domain_redirect_url=root_domain_redirect_url,
            linked_events=[e.id for e in events],
        )

        config_values = []
        for e in events:
            cc = config.ConfigCursor(self.context.ud, e.id)
            v = cc.get(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED)
            config_values.append(v)

        assert_that([e.id for e in updated_domain.linked_events]).is_equal_to(
            [e.id for e in events]
        )
        assert_that(updated_domain.root_domain_redirect_url).is_equal_to(root_domain_redirect_url)
        assert_that(config_values).contains_only(True)

    def test_link_event_custom_domain_staff_user_no_prev_events(self):
        u = factories.create_user(email='<EMAIL>')
        self.init_staff(user=u)

        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
            root_domain_redirect_url='https://another-endpoint.example.com/',
        )

        codes = ['event1', 'event2', 'event3']
        events = []
        for c in codes:
            e = factories.create_event(organization_id=self.organization.id, shortcode=c)
            factories.create_org_custom_domain_linked_event(cd.id, e.id)
            factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '1')
            events.append(e)

        removed_event_domain = custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=cd.id,
            linked_events=[],
        )

        assert_that(removed_event_domain.linked_events).is_length(0)
        assert_that(removed_event_domain.root_domain_redirect_url).is_none()

    def test_link_event_custom_domain_admin_organizer_partial_update(self):
        self.init_admin_organizer(org_id=self.organization.id)

        d1 = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='d1.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
        )

        d2 = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='d2.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
        )

        codes = ['event1', 'event2', 'event3', 'event4', 'event5']
        events = []
        for c in codes:
            e = factories.create_event(organization_id=self.organization.id, shortcode=c)
            factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '0')
            events.append(e.as_simple_entity())

        # Attach D1 -> [event1, event2]
        # Attach D2 -> [event3]
        custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=d1.id,
            linked_events=[events[0].id, events[1].id],
        )

        custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=d2.id,
            linked_events=[events[2].id],
        )

        # Test to attach d2 -> [event4, event5]
        d2_updated = custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=d2.id,
            root_domain_redirect_url='https://xyz.someotherdomain.com',
            linked_events=[events[3].id, events[4].id],
        )

        # Validate linked events - D2
        assert_that([e.id for e in d2_updated.linked_events]).is_equal_to(
            [events[3].id, events[4].id]
        )

        # Validate linked events - D1
        d1_updated = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d1.id
        )
        assert_that([e.id for e in d1_updated.linked_events]).is_equal_to(
            [events[0].id, events[1].id]
        )

        # Validate configs
        config_values = []
        for e in events:
            cc = config.ConfigCursor(self.context.ud, e.id)
            v = cc.get(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED)
            config_values.append(v)

        assert_that(config_values).is_equal_to([True, True, False, True, True])

    @raises(OrgCustomDomainUnverifiedError)
    def test_link_event_custom_domain_admin_organizer_unverified(self):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = custom_domains.create(
            self.context, organization_id=self.organization.id, custom_domain='test.eventmobi.com'
        )

        e = factories.create_event(organization_id=self.organization.id)

        custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=domain.id,
            linked_events=[e.id],
        )

    @raises(OrgCustomDomainEventAlreadyLinked)
    def test_link_event_custom_domain_already_linked(self):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = custom_domains.create(
            self.context, organization_id=self.organization.id, custom_domain='test.eventmobi.com'
        )

        e = factories.create_event(organization_id=self.organization.id)
        factories.create_org_custom_domain_linked_event(domain.id, e.id)

        another_domain = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
        )

        custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=another_domain.id,
            linked_events=[e.id],
        )

    @raises(AuthorizationError)
    def test_link_event_custom_domain_collaborator_organizer(self):
        self.init_collaborator_organizer(org_id=self.organization.id)
        custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=uuid.uuid4(),
            linked_events=[1],
        )

    def test_lookup_custom_domain(self):
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.verified,
            root_domain_redirect_url='https://another-site.something.com/path',
        )

        codes = ['event1', 'event2']
        for c in codes:
            e = factories.create_event(organization_id=self.organization.id, shortcode=c)
            factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '1')
            factories.create_org_custom_domain_linked_event(cd.id, e.id)

        result = custom_domains.lookup(self.context, custom_domain='test.eventmobi.com')

        assert_that(result.root_domain_redirect_url).is_equal_to(
            'https://another-site.something.com/path'
        )
        assert_that(result.linked_events).is_length(2)
        assert_that([e['code'] for e in result.linked_events]).is_equal_to(['event1', 'event2'])
        assert_that([e['enabled'] for e in result.linked_events]).contains_only(True)

    @raises(OrgCustomDomainNotFoundError)
    def test_lookup_custom_domain_not_exists(self):
        custom_domains.lookup(self.context, custom_domain='test.eventmobi.com')

    @raises(OrgCustomDomainNotFoundError)
    def test_lookup_custom_domain_unverified(self):
        factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.eventmobi.com',
            verification_status=DomainVerificationStatus.pending,
        )

        custom_domains.lookup(self.context, custom_domain='test.eventmobi.com')

    @override_app_config(EVENTMOBI_DOMAIN='eventmobi.com')
    def test_redirect_custom_domain_em_host_no_domain_does_not_exist(self):

        e = factories.create_event(organization_id=self.organization.id)
        factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '0')

        response = {
            ProductTypes.event_space: f'https://eventmobi.com/{e.shortcode}',
            ProductTypes.registration: f'https://eventmobi.com/website/{e.shortcode}',
            ProductTypes.website: f'https://eventmobi.com/website/{e.shortcode}',
            ProductTypes.attendee_portal: f'https://eventmobi.com/attendee-portal/{e.shortcode}',
        }

        for product in list(response.keys()):
            result = custom_domains.redirect(
                self.context,
                host_name='eventmobi.com',
                event_code=e.shortcode,
                product=product,
            )
            assert_that(result.cd_redirect_needed).is_equal_to(False)
            assert_that(result.cd_redirect_url).is_equal_to(response[product])

    @override_app_config(EVENTMOBI_DOMAIN='eventmobi.com')
    def test_redirect_custom_domain_em_host_domain_unverified(self):

        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.pending,
        )

        e = factories.create_event(organization_id=self.organization.id)
        factories.create_org_custom_domain_linked_event(cd.id, e.id)
        factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '0')

        response = {
            ProductTypes.event_space: f'https://eventmobi.com/{e.shortcode}',
            ProductTypes.registration: f'https://eventmobi.com/website/{e.shortcode}',
            ProductTypes.website: f'https://eventmobi.com/website/{e.shortcode}',
            ProductTypes.attendee_portal: f'https://eventmobi.com/attendee-portal/{e.shortcode}',
        }

        for product in list(response.keys()):
            result = custom_domains.redirect(
                self.context,
                host_name='eventmobi.com',
                event_code=e.shortcode,
                product=product,
            )
            assert_that(result.cd_redirect_needed).is_equal_to(False)
            assert_that(result.cd_redirect_url).is_equal_to(response[product])

    @override_app_config(EVENTMOBI_DOMAIN='eventmobi.com')
    def test_redirect_custom_domain_em_host_domain_verified(self):

        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
        )

        e = factories.create_event(organization_id=self.organization.id)
        factories.create_org_custom_domain_linked_event(cd.id, e.id)
        factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '1')

        response = {
            ProductTypes.event_space: f'https://test.example.com/{e.shortcode}',
            ProductTypes.registration: f'https://test.example.com/website/{e.shortcode}',
            ProductTypes.website: f'https://test.example.com/website/{e.shortcode}',
            ProductTypes.attendee_portal: (
                f'https://test.example.com/attendee-portal/{e.shortcode}'
            ),
        }

        for product in list(response.keys()):
            result = custom_domains.redirect(
                self.context,
                host_name='eventmobi.com',
                event_code=e.shortcode,
                product=product,
            )
            assert_that(result.cd_redirect_needed).is_equal_to(True)
            assert_that(result.cd_redirect_url).is_equal_to(response[product])

    @override_app_config(EVENTMOBI_DOMAIN='eventmobi.com')
    def test_redirect_custom_domain_custom_host_domain_unverified(self):

        # Domain in pending state
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.pending,
        )

        e = factories.create_event(organization_id=self.organization.id)
        factories.create_org_custom_domain_linked_event(cd.id, e.id)
        factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '1')

        response = {
            ProductTypes.event_space: f'https://eventmobi.com/{e.shortcode}',
            ProductTypes.registration: f'https://eventmobi.com/website/{e.shortcode}',
            ProductTypes.attendee_portal: f'https://eventmobi.com/attendee-portal/{e.shortcode}',
        }

        for product in list(response.keys()):
            result = custom_domains.redirect(
                self.context,
                host_name='test.example.com',
                event_code=e.shortcode,
                product=product,
            )
            assert_that(result.cd_redirect_needed).is_equal_to(True)
            assert_that(result.cd_redirect_url).is_equal_to(response[product])

    def test_redirect_custom_domain_custom_host_domain_verified(self):
        # Domain in verified state
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
        )

        e = factories.create_event(organization_id=self.organization.id)
        factories.create_org_custom_domain_linked_event(cd.id, e.id)
        factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '1')

        response = {
            ProductTypes.event_space: f'https://test.example.com/{e.shortcode}',
            ProductTypes.registration: f'https://test.example.com/website/{e.shortcode}',
            ProductTypes.website: f'https://test.example.com/website/{e.shortcode}',
            ProductTypes.attendee_portal: (
                f'https://test.example.com/attendee-portal/{e.shortcode}'
            ),
        }

        for product in list(response.keys()):
            result = custom_domains.redirect(
                self.context,
                host_name='test.example.com',
                event_code=e.shortcode,
                product=product,
            )
            assert_that(result.cd_redirect_needed).is_equal_to(False)
            assert_that(result.cd_redirect_url).is_equal_to(response[product])

    def test_redirect_custom_domain_custom_host_no_event_code(self):
        factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            root_domain_redirect_url='https://another-site.something.com',
        )

        for product in [
            ProductTypes.event_space,
            ProductTypes.registration,
            ProductTypes.attendee_portal,
        ]:
            result = custom_domains.redirect(
                self.context,
                host_name='test.example.com',
                product=product,
            )

            assert_that(result.cd_redirect_needed).is_equal_to(True)
            assert_that(result.cd_redirect_url).is_equal_to('https://another-site.something.com')

    def test_redirect_custom_domain_custom_host_no_event_code_no_redirect_url(self):
        factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
        )

        for product in [
            ProductTypes.event_space,
            ProductTypes.registration,
            ProductTypes.attendee_portal,
        ]:
            result = custom_domains.redirect(
                self.context,
                host_name='test.example.com',
                product=product,
            )

            assert_that(result.cd_redirect_needed).is_equal_to(False)
            assert_that(result.cd_redirect_url).is_equal_to(None)

    def test_redirect_custom_domain_custom_host_no_event_code_no_redirect_url_single_event(self):
        cd = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
        )

        e = factories.create_event(organization_id=self.organization.id, shortcode='sample1')
        factories.create_org_custom_domain_linked_event(cd.id, e.id)
        factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, e.id, '1')

        response = {
            ProductTypes.event_space: f'https://test.example.com/{e.shortcode}',
            ProductTypes.registration: f'https://test.example.com/website/{e.shortcode}',
            ProductTypes.attendee_portal: (
                f'https://test.example.com/attendee-portal/{e.shortcode}'
            ),
        }

        for product in list(response.keys()):
            result = custom_domains.redirect(
                self.context,
                host_name='test.example.com',
                product=product,
            )

            assert_that(result.cd_redirect_needed).is_equal_to(True)
            assert_that(result.cd_redirect_url).is_equal_to(response[product])

    @override_app_config(EVENTMOBI_DOMAIN='eventmobi.com')
    def test_redirect_custom_domain_invalid_event_code(self):
        result = custom_domains.redirect(
            self.context,
            host_name='eventmobi.com',
            event_code='sample1',
            product=ProductTypes.event_space,
        )

        assert_that(result.cd_redirect_needed).is_equal_to(False)
        assert_that(result.cd_redirect_url).is_equal_to('https://eventmobi.com/sample1')

        result = custom_domains.redirect(
            self.context,
            host_name='test.example.com',
            event_code='sample1',
            product=ProductTypes.registration,
        )

        assert_that(result.cd_redirect_needed).is_equal_to(True)
        assert_that(result.cd_redirect_url).is_equal_to('https://eventmobi.com/website/sample1')

    def test_verify_custom_domain_no_dns_records(self):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = custom_domains.create(
            self.context, organization_id=self.organization.id, custom_domain='test.example.com'
        )

        assert_that(domain.dns_records).is_length(0)
        updated_domain = custom_domains.verify(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )
        assert_that(updated_domain.dns_records).is_length(2)

    def test_process_custom_domain_no_dns_records(self):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = custom_domains.create(
            self.context, organization_id=self.organization.id, custom_domain='test.example.com'
        )

        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.dns_records).is_length(2)

    @patch('dns.resolver.resolve')
    def test_process_custom_domain_domain_record_configured_acm_pending(self, mock_resolver):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        # End user configured
        mock = Mock()
        mock.target = domain.dns_records[1].value
        mock_resolver.side_effect = [dns.resolver.NXDOMAIN, [mock]]

        # Validate the DNS records
        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.pending
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.pending
        )
        assert_that(updated_domain.is_validation_required).is_true()

    @patch('dns.resolver.resolve')
    def test_process_custom_domain_record_incorrect_acm_pending(self, mock_resolver):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        # End user configured both DNS records correctly
        # But the ACM certificate status is still PENDING_VALIDATION
        r1 = Mock()
        r1.target = 'dummy_value'
        mock_resolver.side_effect = [dns.resolver.NXDOMAIN, [r1]]

        # Validate the DNS records
        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.pending
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.pending
        )
        assert_that(updated_domain.is_validation_required).is_true()

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_cert_record_incorrect_acm_failed(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        # End user configured incorrect cert dns record
        r0 = Mock()
        r0.target = 'dummy_value'
        r1 = Mock()
        r1.target = domain.dns_records[1].value
        dns_resolver.side_effect = [[r0], [r1]]

        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.FAILED.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        # Validate the DNS records
        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.is_validation_required).is_true()
        assert_that(updated_domain.failure_email_sent_at).is_not_none()
        self.mock_email_client.assert_called_once_with(
            to_address=['<EMAIL>'],
            subject='(DEV) Verification failed - Unable to connect Custom domain to EventMobi',
            template_name='cms_2/custom_domain_failed',
        )

    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_validation_records'
    )
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_no_dns_records_acm_failed(
        self, domain_resources, get_validation_records
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = custom_domains.create(
            self.context, organization_id=self.organization.id, custom_domain='test.example.com'
        )

        get_validation_records.return_value = []

        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.FAILED.name
        cert.failure_reason = 'ADDITIONAL_VERIFICATION_REQUIRED'

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        # Validate the DNS records
        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.dns_records).is_length(0)
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.is_validation_required).is_false()
        assert_that(updated_domain.failure_email_sent_at).is_not_none()
        self.mock_email_client.assert_called_once_with(
            to_address=['<EMAIL>'],
            subject='(DEV) Verification failed - Unable to connect Custom domain to EventMobi',
            template_name='cms_2/custom_domain_failed',
        )

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_cert_record_incorrect_acm_failed_email_failed_multiple(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        # End user configured incorrect cert dns record
        r0 = Mock()
        r0.target = 'dummy_value'
        r1 = Mock()
        r1.target = domain.dns_records[1].value
        dns_resolver.side_effect = [[r0], [r1]]

        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.FAILED.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        # Validate the DNS records
        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.failure_email_sent_at).is_not_none()

        # Called it after sending email before 1 day.
        # Shouldnt't send the email.
        # End user configured incorrect cert dns record
        r0 = Mock()
        r0.target = 'dummy_value'
        r1 = Mock()
        r1.target = domain.dns_records[1].value
        dns_resolver.side_effect = [[r0], [r1]]

        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        # Update the failure_email_sent to last 3 days
        m = OrganizationCustomDomainsModel.get_by_id(pk_id=domain.id)
        m.failure_email_sent_at = m.failure_email_sent_at - relativedelta(days=3)
        OrganizationCustomDomainsModel.save(m)

        # Call the process again. Should send the email.
        r0 = Mock()
        r0.target = 'dummy_value'
        r1 = Mock()
        r1.target = domain.dns_records[1].value
        dns_resolver.side_effect = [[r0], [r1]]

        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(self.mock_email_client.call_count).is_equal_to(2)

    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_acm_cert_timed_out_user_initiated_before_system(
        self, domain_resources
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        # The ACM certificate timed out
        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.VALIDATION_TIMED_OUT.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.process(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=domain.id,
            user_initiated=True,
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        # Should return the same entity
        assert_that(updated_domain.dns_records).is_equal_to(domain.dns_records)
        assert_that(updated_domain.is_validation_required).is_equal_to(
            domain.is_validation_required
        )
        assert_that(updated_domain.certificate_arn).is_equal_to(domain.certificate_arn)
        assert_that(updated_domain.verification_status).is_equal_to(domain.verification_status)

    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_acm_cert_timed_out_system_initiated(self, domain_resources):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        # The ACM certificate timed out
        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.VALIDATION_TIMED_OUT.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.process(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=domain.id,
            user_initiated=False,
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        # Should return the same entity except the set the dns_validation attribute
        assert_that(updated_domain.dns_records).is_equal_to(domain.dns_records)
        assert_that(updated_domain.is_validation_required).is_equal_to(
            domain.is_validation_required
        )
        assert_that(updated_domain.certificate_arn).is_equal_to(domain.certificate_arn)
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.validation_timed_out
        )

    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_acm_cert_timed_out_user_initiated_after_system(
        self, domain_resources
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        # The ACM certificate timed out
        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.VALIDATION_TIMED_OUT.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.process(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=domain.id,
            user_initiated=False,
        )

        custom_domains.process(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=domain.id,
            user_initiated=True,
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        with self.assertRaises(ACMAPIError):
            # Should delete the existing ACM certificate
            self.acm_client.describe_certificate(domain.certificate_arn)

        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.pending
        )
        assert_that(updated_domain.dns_records).is_length(0)
        assert_that(updated_domain.is_validation_required).is_false()
        assert_that(updated_domain.certificate_arn).is_not_equal_to(domain.certificate_arn)

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_domain_record_incorrect_acm_issued(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.ISSUED.name

        # End user configured correct certificate record but incorrect domain record
        r0 = Mock()
        r0.target = domain.dns_records[0].value
        r1 = Mock()
        r1.target = 'dummy_value'

        dns_resolver.side_effect = [[r0], [r1]]
        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.is_validation_required).is_true()
        assert_that(updated_domain.failure_email_sent_at).is_not_none()
        self.mock_email_client.assert_called_once_with(
            to_address=['<EMAIL>'],
            subject='(DEV) Verification failed - Unable to connect Custom domain to EventMobi',
            template_name='cms_2/custom_domain_failed',
        )

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_domain_record_not_found_acm_issued(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain = self._setup_custom_domain()

        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.ISSUED.name

        r0 = Mock()
        r0.target = domain.dns_records[0].value

        # End user configured correct certificate record and didn't configured domain record
        dns_resolver.side_effect = [[r0], dns.resolver.NXDOMAIN]
        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.pending
        )
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.pending
        )
        assert_that(updated_domain.is_validation_required).is_true()

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_process_custom_domain_domain_record_correct_acm_issued(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        self._create_hosted_zone('example.com')
        domain = self._setup_custom_domain()

        cert = self.acm_client.describe_certificate(domain.certificate_arn)
        cert.status = ACMCertificateStatus.ISSUED.name

        # DNS records configured correctly and certificate is issued
        r0 = Mock()
        r0.target = domain.dns_records[0].value
        r1 = Mock()
        r1.target = domain.dns_records[1].value

        dns_resolver.side_effect = [[r0], [r1]]
        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.is_validation_required).is_false()
        assert_that(updated_domain.cloudfront_id).is_not_none()
        assert_that(updated_domain.cloudfront_domain).is_not_none()
        assert_that(self.mock_email_client.call_count).is_equal_to(2)
        cors_domains = self.s3lib.download_file_as_bytes(
            key='assets/org_custom_domains_for_cors.csv'
        ).decode('utf-8')

        assert_that(cors_domains).is_equal_to('https://test.example.com')

    @patch(
        'flux.adapters.tasks.custom_domain.async_org_custom_domain_tasks.'
        'process_custom_domain_async.delay'
    )
    @override_app_config(ENV='production')
    def test_schedule_process_custom_domain(self, mock_process_custom_domain_async):
        self.init_admin_organizer(org_id=self.organization.id)

        # Update org config to allow more custom domains
        m = OrganizationConfigurationKeyTable.get_by_id(ALLOWED_CUSTOM_DOMAIN_COUNT_CONFIG)
        m.default_value = 10
        OrganizationConfigurationKeyTable.save(m)

        factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='verified.domain.com',
            verification_status=DomainVerificationStatus.verified,
        )

        domains = []
        for d in ['d1.example.com', 'd2.example.com', 'd3.example.com']:
            domains.append(self._setup_custom_domain(d))

        custom_domains.schedule_process(self.context)
        calls = [call(custom_domain_id=d.id, user_initiated=False) for d in domains]
        assert_that(mock_process_custom_domain_async.call_count).is_equal_to(len(domains))
        assert_that(mock_process_custom_domain_async.assert_has_calls(calls, any_order=True))

    @patch(
        'flux.adapters.tasks.custom_domain.async_org_custom_domain_tasks.'
        'process_custom_domain_async.delay'
    )
    def test_schedule_process_custom_domain_for_stage_env(self, mock_process_custom_domain_async):
        self.init_admin_organizer(org_id=self.organization.id)

        # Update org config to allow more custom domains
        m = OrganizationConfigurationKeyTable.get_by_id(ALLOWED_CUSTOM_DOMAIN_COUNT_CONFIG)
        m.default_value = 10
        OrganizationConfigurationKeyTable.save(m)

        app_config.ENV = 'production'
        for d in ['prod-1.example.com', 'prod-2.example.com', 'prod-3.example.com']:
            self._setup_custom_domain(d)

        app_config.ENV = 'stage'
        # We don't want stage scheduler to process prod domains
        stage_domain = self._setup_custom_domain('stage.example.com')

        # Should only process stage domain as the env is set to stage
        custom_domains.schedule_process(self.context)
        calls = [call(custom_domain_id=stage_domain.id, user_initiated=False)]
        assert_that(mock_process_custom_domain_async.call_count).is_equal_to(1)
        assert_that(mock_process_custom_domain_async.assert_has_calls(calls, any_order=True))

    def test_expire_custom_domain_created_10_days_back(self):
        self.init_admin_organizer(org_id=self.organization.id)
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
        )

        d.created_at = timeutils.now() - relativedelta(days=10)
        d.updated_at = timeutils.now() - relativedelta(days=10)
        OrganizationCustomDomainsModel.save(d)
        custom_domains.expire(self.context, custom_domain_id=d.id)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        assert_that(updated_domain.expired_at).is_none()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.verified
        )

    def test_expire_custom_domain_when_part_of_upcoming_event(self):
        self.init_admin_organizer(org_id=self.organization.id)
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
        )

        e = factories.create_event(
            organization_id=self.organization.id,
            shortcode='event-1',
            start_date=timeutils.now().date() + relativedelta(days=20),
            end_date=timeutils.now().date() + relativedelta(days=40),
        )
        factories.create_org_custom_domain_linked_event(d.id, e.id)
        custom_domains.expire(self.context, custom_domain_id=d.id)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        assert_that(updated_domain.expired_at).is_none()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.verified
        )

    def test_expire_custom_domain_when_part_of_active_event(self):
        self.init_admin_organizer(org_id=self.organization.id)
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
        )

        e = factories.create_event(
            organization_id=self.organization.id,
            shortcode='event-1',
            start_date=timeutils.now().date() - relativedelta(days=15),
            end_date=timeutils.now().date() - relativedelta(days=1),
        )
        factories.create_org_custom_domain_linked_event(d.id, e.id)
        factories.create_conf(config.keys.EVENTAPP_POST_EVENT_ACCESS_DAYS, e.id, 10)

        custom_domains.expire(self.context, custom_domain_id=d.id)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        assert_that(updated_domain.expired_at).is_none()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.verified
        )

    def test_expire_custom_domain_post_event_access_days_null(self):
        self.init_admin_organizer(org_id=self.organization.id)
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
        )

        d.created_at = timeutils.now() - relativedelta(months=8)
        d.updated_at = timeutils.now() - relativedelta(months=8, days=7)
        OrganizationCustomDomainsModel.save(d)

        time_before_4_months = timeutils.now().date() - relativedelta(months=4)
        t = 10
        for e in ['event-1', 'event-2', 'event-3']:
            ev = factories.create_event(
                organization_id=self.organization.id,
                name=e,
                shortcode=e,
                start_date=time_before_4_months - relativedelta(days=t),
                end_date=time_before_4_months - relativedelta(days=t - 5),
            )
            factories.create_org_custom_domain_linked_event(d.id, ev.id)
            t += 30

        custom_domains.expire(self.context, custom_domain_id=d.id)
        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        assert_that(updated_domain.expired_at).is_none()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.verified
        )

    def test_expire_custom_domain_linked_with_older_events_for_4_months(self):
        self.init_admin_organizer(org_id=self.organization.id)
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
        )

        d.created_at = timeutils.now() - relativedelta(months=8)
        d.updated_at = timeutils.now() - relativedelta(months=8, days=7)
        OrganizationCustomDomainsModel.save(d)

        time_before_4_months = timeutils.now().date() - relativedelta(months=4)
        t = 10
        for e in ['event-1', 'event-2', 'event-3']:
            ev = factories.create_event(
                organization_id=self.organization.id,
                name=e,
                shortcode=e,
                start_date=time_before_4_months - relativedelta(days=t),
                end_date=time_before_4_months - relativedelta(days=t - 5),
            )
            factories.create_org_custom_domain_linked_event(d.id, ev.id)
            factories.create_conf(config.keys.EVENTAPP_POST_EVENT_ACCESS_DAYS, ev.id, 1)
            t += 30

        custom_domains.expire(self.context, custom_domain_id=d.id)
        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        assert_that(updated_domain.expired_at).is_none()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.verified
        )

    @override_app_config(ENV='production')
    def test_expire_custom_domain_linked_with_older_events_for_7_months(self):
        self.init_admin_organizer(org_id=self.organization.id)

        cert_arn = self.acm_client.request_ssl_certificate(
            domain_name='test.example.com', organization_id=self.organization.id
        )
        cf = self.cloudfront.deploy_distribution(
            alternate_domain_cert_arn=cert_arn,
            alternate_domain_name='test.example.com',
            organization_id=self.organization.id
        )

        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            certificate_arn=cert_arn,
            cloudfront_arn=cf.arn,
            cloudfront_id=cf.id,
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
        )

        d.created_at = timeutils.now() - relativedelta(months=11)
        d.updated_at = timeutils.now() - relativedelta(months=11)
        OrganizationCustomDomainsModel.save(d)

        time_before_7_months = timeutils.now().date() - relativedelta(months=7)
        t = 10
        events = []
        for e in ['event-1', 'event-2', 'event-3']:
            ev = factories.create_event(
                organization_id=self.organization.id,
                name=e,
                shortcode=e,
                start_date=time_before_7_months - relativedelta(days=t),
                end_date=time_before_7_months - relativedelta(days=t - 5),
            )
            factories.create_org_custom_domain_linked_event(d.id, ev.id)
            factories.create_conf(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, ev.id, '1')
            factories.create_conf(config.keys.EVENTAPP_POST_EVENT_ACCESS_DAYS, ev.id, 1)
            t += 30
            events.append(ev)

        custom_domains.expire(self.context, custom_domain_id=d.id)
        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        distribution = self.cloudfront_client.get_distribution(Id=cf.id)

        assert_that(distribution['Distribution']['DistributionConfig']['Enabled']).is_false()
        assert_that(updated_domain.expired_at).is_not_none()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.expired
        )
        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.expired
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.expired
        )
        cors_domains = self.s3lib.download_file_as_bytes(
            key='assets/org_custom_domains_for_cors.csv'
        ).decode('utf-8')

        assert_that(cors_domains).is_equal_to('')

        for e in events:
            cc = config.ConfigCursor(self.ud, e.id)
            assert_that(cc.get(config.keys.IS_CUSTOM_DOMAIN_CONFIGURED)).is_equal_to(False)

    def test_expire_custom_domain_no_linked_events_older_than_4_months(self):
        self.init_admin_organizer(org_id=self.organization.id)
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
        )

        d.created_at = timeutils.now() - relativedelta(months=4)
        d.updated_at = timeutils.now() - relativedelta(months=4)
        OrganizationCustomDomainsModel.save(d)

        custom_domains.expire(self.context, custom_domain_id=d.id)
        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        assert_that(updated_domain.expired_at).is_none()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.verified
        )

    @override_app_config(ENV='production')
    def test_expire_custom_domain_no_linked_events_older_than_7_months(self):
        self.init_admin_organizer(org_id=self.organization.id)
        cert_arn = self.acm_client.request_ssl_certificate(
            domain_name='test.example.com', organization_id=self.organization.id
        )
        cf = self.cloudfront.deploy_distribution(
            alternate_domain_cert_arn=cert_arn,
            alternate_domain_name='test.example.com',
            organization_id=self.organization.id
        )

        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            certificate_arn=cert_arn,
            cloudfront_arn=cf.arn,
            cloudfront_id=cf.id,
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
        )

        d.created_at = timeutils.now() - relativedelta(months=7)
        d.updated_at = timeutils.now() - relativedelta(months=7)
        OrganizationCustomDomainsModel.save(d)

        custom_domains.expire(self.context, custom_domain_id=d.id)
        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        distribution = self.cloudfront_client.get_distribution(Id=cf.id)

        assert_that(distribution['Distribution']['DistributionConfig']['Enabled']).is_false()
        assert_that(updated_domain.expired_at).is_not_none()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.expired
        )
        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.expired
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.expired
        )
        cors_domains = self.s3lib.download_file_as_bytes(
            key='assets/org_custom_domains_for_cors.csv'
        ).decode('utf-8')

        assert_that(cors_domains).is_equal_to('')
        assert_that(self.mock_email_client.call_count).is_equal_to(2)

    @patch(
        'flux.adapters.tasks.custom_domain.async_org_custom_domain_tasks.'
        'expire_custom_domain_async.delay'
    )
    @override_app_config(ENV='production')
    def test_schedule_expire_custom_domain(self, mock_expire_custom_domain_async):
        domain_list = [
            {
                'domain_name': 'd1.example.com',
                'domain_cname_status': DomainVerificationStatus.verified,
                'certificate_cname_status': DomainVerificationStatus.verified,
                'verification_status': DomainVerificationStatus.verified,
            },
            {
                'domain_name': 'd2.example.com',
                'domain_cname_status': DomainVerificationStatus.verified,
                'certificate_cname_status': DomainVerificationStatus.failed,
                'verification_status': DomainVerificationStatus.failed,
            },
        ]

        domains = []
        for d in domain_list:
            domains.append(
                factories.create_org_custom_domain(
                    organization_id=self.organization.id,
                    certificate_arn=self.acm_client.request_ssl_certificate(
                        d['domain_name'], organization_id=self.organization.id
                    ),
                    **d,
                )
            )

        custom_domains.schedule_expiry(self.context)
        calls = [call(custom_domain_id=d.id) for d in domains]
        assert_that(mock_expire_custom_domain_async.call_count).is_equal_to(len(domains))
        assert_that(mock_expire_custom_domain_async.assert_has_calls(calls, any_order=True))

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_disconnect_custom_domain_all_records_verified_acm_issued(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain_resources.return_value = CustomDomainResource
        domain_name = 'test.example.com'
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name=domain_name,
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            certificate_arn=self.acm_client.request_ssl_certificate(
                domain_name, self.organization.id
            ),
        )

        r0 = Mock()
        r0.target = d.certificate_cname_value
        r1 = Mock()
        r1.target = d.domain_cname

        dns_resolver.side_effect = [[r0], [r1]]

        cert = self.acm_client.describe_certificate(d.certificate_arn)
        cert.status = ACMCertificateStatus.ISSUED.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.disconnect(self.context, custom_domain_id=d.id)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.verified
        )

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_disconnect_custom_domain_cert_record_incorrect_acm_failed(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)

        cert_arn = self.acm_client.request_ssl_certificate(
            domain_name='test.example.com', organization_id=self.organization.id
        )
        cf = self.cloudfront.deploy_distribution(
            alternate_domain_cert_arn=cert_arn,
            alternate_domain_name='test.example.com',
            organization_id=self.organization.id
        )

        domain_resources.return_value = CustomDomainResource
        domain_name = 'test.example.com'
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name=domain_name,
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            certificate_arn=cert_arn,
            cloudfront_id=cf.id,
            cloudfront_arn=cf.arn
        )

        r0 = Mock()
        r0.target = 'dummy_value'
        r1 = Mock()
        r1.target = d.domain_cname

        dns_resolver.side_effect = [[r0], [r1]]

        cert = self.acm_client.describe_certificate(d.certificate_arn)
        cert.status = ACMCertificateStatus.FAILED.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.disconnect(self.context, custom_domain_id=d.id)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        distribution = self.cloudfront_client.get_distribution(Id=cf.id)

        assert_that(distribution['Distribution']['DistributionConfig']['Enabled']).is_false()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        self.mock_email_client.assert_called_once_with(
            to_address=['<EMAIL>'],
            subject='(DEV) Disconnected Custom domain from EventMobi',
            template_name='cms_2/custom_domain_disconnected',
        )
        cors_domains = self.s3lib.download_file_as_bytes(
            key='assets/org_custom_domains_for_cors.csv'
        ).decode('utf-8')

        assert_that(cors_domains).is_equal_to('')

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'disable_domain_resources'
    )
    def test_disconnect_custom_domain_cert_record_removed_acm_failed(
        self, disable_domain_resources, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain_resources.return_value = CustomDomainResource
        domain_name = 'test.example.com'
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name=domain_name,
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            certificate_arn=self.acm_client.request_ssl_certificate(
                domain_name, self.organization.id
            ),
        )

        r1 = Mock()
        r1.target = d.domain_cname

        disable_domain_resources.return_value = None
        dns_resolver.side_effect = [dns.resolver.NXDOMAIN, [r1]]

        cert = self.acm_client.describe_certificate(d.certificate_arn)
        cert.status = ACMCertificateStatus.FAILED.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.disconnect(self.context, custom_domain_id=d.id)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        self.mock_email_client.assert_called_once_with(
            to_address=['<EMAIL>'],
            subject='(DEV) Disconnected Custom domain from EventMobi',
            template_name='cms_2/custom_domain_disconnected',
        )

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_disconnect_custom_domain_domain_record_incorrect_acm_issued(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain_resources.return_value = CustomDomainResource
        domain_name = 'test.example.com'

        cert_arn = self.acm_client.request_ssl_certificate(
            domain_name, self.organization.id
        )
        cf = self.cloudfront.deploy_distribution(
            alternate_domain_cert_arn=cert_arn,
            alternate_domain_name='test.example.com',
            organization_id=self.organization.id
        )

        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name=domain_name,
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            certificate_arn=cert_arn,
            cloudfront_id=cf.id,
            cloudfront_arn=cf.arn,
        )

        r0 = Mock()
        r0.target = d.certificate_cname_value
        r1 = Mock()
        r1.target = 'dummy_value'

        dns_resolver.side_effect = [[r0], [r1]]

        cert = self.acm_client.describe_certificate(d.certificate_arn)
        cert.status = ACMCertificateStatus.ISSUED.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.disconnect(self.context, custom_domain_id=d.id)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )

        distribution = self.cloudfront_client.get_distribution(Id=cf.id)

        assert_that(distribution['Distribution']['DistributionConfig']['Enabled']).is_false()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.failed
        )
        self.mock_email_client.assert_called_once_with(
            to_address=['<EMAIL>'],
            subject='(DEV) Disconnected Custom domain from EventMobi',
            template_name='cms_2/custom_domain_disconnected',
        )

    @patch('dns.resolver.resolve')
    @patch(
        'flux.adapters.custom_domains.aws_custom_domain_adapter.AWSCustomDomainAdapter.'
        'get_domain_resources'
    )
    def test_disconnect_custom_domain_domain_record_removed_acm_issued(
        self, domain_resources, dns_resolver
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        domain_resources.return_value = CustomDomainResource
        domain_name = 'test.example.com'
        cert_arn = self.acm_client.request_ssl_certificate(
            domain_name, self.organization.id
        )
        cf = self.cloudfront.deploy_distribution(
            alternate_domain_cert_arn=cert_arn,
            alternate_domain_name='test.example.com',
            organization_id=self.organization.id
        )
        d = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name=domain_name,
            verification_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            certificate_arn=cert_arn,
            cloudfront_arn=cf.arn,
            cloudfront_id=cf.id
        )

        r0 = Mock()
        r0.target = d.certificate_cname_value

        dns_resolver.side_effect = [[r0], dns.resolver.NXDOMAIN]

        cert = self.acm_client.describe_certificate(d.certificate_arn)
        cert.status = ACMCertificateStatus.ISSUED.name

        domain_resources.return_value = CustomDomainResource(
            certificate=cert,
            distribution=None,
        )

        custom_domains.disconnect(self.context, custom_domain_id=d.id)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=d.id
        )
        distribution = self.cloudfront_client.get_distribution(Id=cf.id)

        assert_that(distribution['Distribution']['DistributionConfig']['Enabled']).is_false()
        assert_that(updated_domain.verification_status).is_equal_to(
            DomainVerificationStatus.failed
        )
        assert_that(updated_domain.dns_records[0].status).is_equal_to(
            DomainVerificationStatus.verified
        )
        assert_that(updated_domain.dns_records[1].status).is_equal_to(
            DomainVerificationStatus.failed
        )
        self.mock_email_client.assert_called_once_with(
            to_address=['<EMAIL>'],
            subject='(DEV) Disconnected Custom domain from EventMobi',
            template_name='cms_2/custom_domain_disconnected',
        )

    @patch(
        'flux.adapters.tasks.custom_domain.async_org_custom_domain_tasks.'
        'disconnect_custom_domain_async.delay'
    )
    @override_app_config(ENV='production')
    def test_schedule_disconnect_custom_domain(self, mock_disconnect_custom_domain_async):
        domain_list = [
            {
                'domain_name': 'd1.example.com',
                'domain_cname_status': DomainVerificationStatus.verified,
                'certificate_cname_status': DomainVerificationStatus.verified,
                'verification_status': DomainVerificationStatus.verified,
            },
            {
                'domain_name': 'd2.example.com',
                'domain_cname_status': DomainVerificationStatus.verified,
                'certificate_cname_status': DomainVerificationStatus.failed,
                'verification_status': DomainVerificationStatus.failed,
            },
        ]

        domains = []
        for d in domain_list:
            domains.append(
                factories.create_org_custom_domain(
                    organization_id=self.organization.id,
                    certificate_arn=self.acm_client.request_ssl_certificate(
                        d['domain_name'], organization_id=self.organization.id
                    ),
                    **d,
                )
            )

        custom_domains.schedule_disconnect(self.context)
        calls = [call(custom_domain_id=domains[0].id)]
        assert_that(mock_disconnect_custom_domain_async.call_count).is_equal_to(1)
        assert_that(mock_disconnect_custom_domain_async.assert_has_calls(calls, any_order=True))

    def test_delete_custom_domain_verified_deployed_distribution(self):
        self.init_admin_organizer(org_id=self.organization.id)
        self._create_hosted_zone('example.com')
        cert = self.acm_client.request_ssl_certificate(
            domain_name='test.example.com', organization_id=self.organization.id
        )

        certificate = self.acm_client.describe_certificate(cert)
        certificate.status = ACMCertificateStatus.ISSUED.name

        cf = self.cloudfront.deploy_distribution(
            alternate_domain_cert_arn=cert,
            alternate_domain_name='test.example.com',
            organization_id=self.organization.id
        )

        domain = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_arn=cert,
            cloudfront_arn=cf.arn,
            cloudfront_id=cf.id,
        )

        custom_domains.delete_by_id(
            self.context,
            organization_id=domain.organization_id,
            custom_domain_id=domain.id,
            user_initiated=True,
        )

        m = OrganizationCustomDomainsModel.get_by_id(domain.id, deleted=True)
        updated_distribution = self.cloudfront.describe_distribution(distribution_id=cf.id)
        assert_that(m.deleted_at).is_not_none()
        assert_that(m.resources_removed).is_false()
        assert_that(
            updated_distribution['Distribution']['DistributionConfig']['Enabled']
        ).is_false()

    def test_delete_custom_domain_verified_deployed_distribution_in_progress(self):
        self.init_admin_organizer(org_id=self.organization.id)
        self._create_hosted_zone('example.com')
        cert = self.acm_client.request_ssl_certificate(
            domain_name='test.example.com', organization_id=self.organization.id
        )

        cf = self.cloudfront.deploy_distribution(
            alternate_domain_cert_arn=cert,
            alternate_domain_name='test.example.com',
            organization_id=self.organization.id
        )

        # Set distribution to in progress
        state_manager.set_transition(
            model_name="cloudfront::distribution",
            transition={"progression": "time", "seconds": 300}
        )
        self.cloudfront.disable_distribution(distribution_id=cf.id)

        domain = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_arn=cert,
            cloudfront_arn=cf.arn,
            cloudfront_id=cf.id,
        )

        custom_domains.delete_by_id(
            self.context,
            organization_id=domain.organization_id,
            custom_domain_id=domain.id,
            user_initiated=True,
        )

        m = OrganizationCustomDomainsModel.get_by_id(domain.id, deleted=True)
        updated_distribution = self.cloudfront.describe_distribution(distribution_id=cf.id)
        assert_that(m.deleted_at).is_not_none()
        assert_that(m.resources_removed).is_false()
        assert_that(
            updated_distribution['Distribution']['DistributionConfig']['Enabled']
        ).is_false()

    @patch('flux.adapters.clients.cloudfront.CloudFront.describe_distribution')
    @patch('flux.adapters.clients.cloudfront.CloudFront.delete_distribution')
    @patch('flux.adapters.clients.route53.Route53.change_resource_record_sets')
    def test_delete_custom_domain_verified_disabled_distribution(
        self, route53_change_rs, delete_distribution, describe_distribution
    ):
        self.init_admin_organizer(org_id=self.organization.id)
        self._create_hosted_zone('example.com')
        cert = self.acm_client.request_ssl_certificate(
            domain_name='test.example.com', organization_id=self.organization.id
        )

        cloudfront_id = 'E2D19PPFO07VTF'
        cloudfront_arn = 'arn:aws:cloudfront::123456789012:distribution/E2D19PPFO07VTF'

        describe_distribution.return_value = {
            'Distribution': {
                'DomainName': 'd1p0x1ood3suc.cloudfront.net',
                'Status': CloudFrontDistributionStatus.Deployed.name,
                'DistributionConfig': {'Enabled': False},
            }
        }

        route53_change_rs.return_value = None
        delete_distribution.return_value = None

        domain = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_arn=cert,
            cloudfront_arn=cloudfront_arn,
            cloudfront_id=cloudfront_id,
        )

        custom_domains.delete_by_id(
            self.context,
            organization_id=domain.organization_id,
            custom_domain_id=domain.id,
            user_initiated=True,
        )

        m = OrganizationCustomDomainsModel.get_by_id(domain.id, deleted=True)
        describe_distribution.assert_called_once()
        delete_distribution.assert_called_once()
        route53_change_rs.assert_called_once()
        assert_that(m.deleted_at).is_not_none()
        assert_that(m.resources_removed).is_true()
        cors_domains = self.s3lib.download_file_as_bytes(
            key='assets/org_custom_domains_for_cors.csv'
        ).decode('utf-8')

        assert_that(cors_domains).is_equal_to('')

    def test_delete_custom_domain_verified(self):
        self.init_admin_organizer(org_id=self.organization.id)

        cert = self.acm_client.request_ssl_certificate(
            domain_name='test.example.com', organization_id=self.organization.id
        )

        cf = self.cloudfront.deploy_distribution(
            alternate_domain_cert_arn=cert,
            alternate_domain_name='test.example.com',
            organization_id=self.organization.id
        )

        domain = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='test.example.com',
            verification_status=DomainVerificationStatus.verified,
            certificate_cname_status=DomainVerificationStatus.verified,
            domain_cname_status=DomainVerificationStatus.verified,
            certificate_arn=cert,
            cloudfront_arn=cf.arn,
            cloudfront_id=cf.id,
        )

        events = []
        for code in ['event1', 'event2']:
            e = factories.create_event(organization_id=self.organization.id, shortcode=code)
            factories.create_conf(
                key=config.keys.IS_CUSTOM_DOMAIN_CONFIGURED, event_id=e.id, value='0'
            )
            events.append(e)

        custom_domains.link_events(
            self.context,
            organization_id=self.organization.id,
            custom_domain_id=domain.id,
            linked_events=[e.id for e in events],
        )

        config_values = []
        for e in events:
            cc = config.ConfigCursor(self.context.ud, event_id=e.id)
            v = cc.get(key_name=config.keys.IS_CUSTOM_DOMAIN_CONFIGURED)
            config_values.append(v)

        updated_domain = custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        assert_that(config_values).contains_only(True)
        custom_domains.delete_by_id(
            self.context,
            organization_id=updated_domain.organization_id,
            custom_domain_id=updated_domain.id,
            user_initiated=True,
        )

        m = OrganizationCustomDomainsModel.get_by_id(updated_domain.id, deleted=True)
        assert_that(m.linked_events).is_length(0)

        config_values = []
        for e in events:
            cc = config.ConfigCursor(self.context.ud, event_id=e.id)
            v = cc.get(key_name=config.keys.IS_CUSTOM_DOMAIN_CONFIGURED)
            config_values.append(v)

        assert_that(config_values).contains_only(False)

    @patch(
        'flux.adapters.tasks.custom_domain.async_org_custom_domain_tasks.'
        'delete_custom_domain_async.delay'
    )
    @patch(
        'flux.adapters.tasks.custom_domain.async_org_custom_domain_tasks.'
        'delete_custom_domain_resources_async.delay'
    )
    @override_app_config(ENV='production')
    def test_schedule_delete_custom_domain(
        self, mock_delete_custom_resource_async, mock_delete_custom_domain_async
    ):
        self.init_admin_organizer(org_id=self.organization.id)

        d1 = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='expired.domain.com',
            verification_status=DomainVerificationStatus.expired,
            certificate_arn=self.acm_client.request_ssl_certificate(
                domain_name='expired.domain.com', organization_id=self.organization.id
            ),
        )

        d2 = factories.create_org_custom_domain(
            organization_id=self.organization.id,
            domain_name='pending.domain.com',
            verification_status=DomainVerificationStatus.pending,
            certificate_arn=self.acm_client.request_ssl_certificate(
                domain_name='pending.domain.com', organization_id=self.organization.id
            ),
            deleted_at=timeutils.now(),
        )

        custom_domains.schedule_delete(self.context)
        d1_call = [call(organization_id=d1.organization_id, custom_domain_id=d1.id)]
        d2_call = [call(custom_domain_id=d2.id)]
        assert_that(mock_delete_custom_domain_async.call_count).is_equal_to(1)
        assert_that(mock_delete_custom_domain_async.assert_has_calls(d1_call, any_order=True))
        assert_that(mock_delete_custom_resource_async.call_count).is_equal_to(1)
        assert_that(mock_delete_custom_resource_async.assert_has_calls(d2_call, any_order=True))

    def _setup_custom_domain(self, domain_name='test.example.com'):
        domain = custom_domains.create(
            self.context, organization_id=self.organization.id, custom_domain=domain_name
        )

        # End user configured only domain record and certificate status is still PENDING_VALIDATION
        custom_domains.process(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

        return custom_domains.get_by_id(
            self.context, organization_id=self.organization.id, custom_domain_id=domain.id
        )

    def _create_hosted_zone(self, hosted_zone):
        hosted_zone = self.route53_client.create_hosted_zone(
            Name=hosted_zone,
            CallerReference=hosted_zone,
        )
        app_config.AWS_ROUTE53_HOSTED_ZONE_ID = hosted_zone['HostedZone']['Id']
        app_config.AWS_ROUTE53_HOSTED_ZONE = hosted_zone['HostedZone']['Name']
