"""
Tests for the CopilotRepository
"""
import uuid

from test.utils.uapi.test_case import AppTestCase
from flux.subdomains.application.genai.adapters.repositories.db_copilot_repository import (
    CopilotRepositoryImpl
)
from flux.subdomains.application.genai.adapters.repositories.models import ConversationModel
from flux.subdomains.application.genai.entities.copilot_entities import Conversation
from flux.subdomains.application.genai.entities.copilot_entities import Message
from flux.domains.common.pagination import PaginationInfo, SortingCriteria


class TestCopilotRepository(AppTestCase):
    """Test cases for the CopilotRepository"""

    def setUp(self):
        """Set up test dependencies"""
        super().setUp()
        self.repository = CopilotRepositoryImpl()
        self.event_id = 123
        self.user_id = 456

    def test_create_conversation(self):
        """Test creating a new conversation"""
        # Arrange
        title = "Test Conversation"

        # Act
        conversation_id = uuid.uuid4()
        result = self.repository.create_conversation(
            conversation_id,
            self.event_id,
            self.user_id,
            title,
        )

        # Assert
        self.assertIsInstance(result, Conversation)
        self.assertEqual(result.event_id, self.event_id)
        self.assertEqual(result.user_id, self.user_id)
        self.assertEqual(result.title, title)

        # Verify it was actually saved to the database
        db_conversation = ConversationModel.get_by_id(result.id)
        self.assertIsNotNone(db_conversation)
        self.assertEqual(db_conversation.title, title)

    def test_get_conversation(self):
        """Test retrieving a conversation by ID"""
        # Arrange
        title = "Test Conversation"
        conversation_id = uuid.uuid4()
        self.repository.create_conversation(
            conversation_id,
            self.event_id,
            self.user_id,
            title,
        )

        # Act
        retrieved_conversation = self.repository.get_conversation(conversation_id)

        # Assert
        self.assertIsInstance(retrieved_conversation, Conversation)
        self.assertEqual(retrieved_conversation.id, conversation_id)
        self.assertEqual(retrieved_conversation.event_id, self.event_id)
        self.assertEqual(retrieved_conversation.user_id, self.user_id)
        self.assertEqual(retrieved_conversation.title, title)

    def test_get_conversations(self):
        """Test retrieving all conversations for a user"""
        # Arrange
        titles = ["Conversation 1", "Conversation 2", "Conversation 3"]
        for title in titles:
            conversation_id = uuid.uuid4()
            self.repository.create_conversation(
                conversation_id,
                self.event_id,
                self.user_id,
                title,
            )

        # Act
        conversations = self.repository.get_conversations(self.event_id, self.user_id)

        # Assert
        self.assertEqual(len(conversations), len(titles))
        conversation_titles = [conv.title for conv in conversations]
        for title in titles:
            self.assertIn(title, conversation_titles)

    def test_get_conversations_with_pagination(self):
        """Test retrieving all conversations for a user with pagination"""
        # Arrange
        titles = ["Conversation 1", "Conversation 2", "Conversation 3"]
        for title in titles:
            conversation_id = uuid.uuid4()
            self.repository.create_conversation(
                conversation_id,
                self.event_id,
                self.user_id,
                title,
            )

        pagination = PaginationInfo(
            items_per_page=1,
            page_offset=0,
            sorting_fields=[SortingCriteria(field_name="updated_at", desc=True)]
        )

        # Act
        conversations = self.repository.get_conversations(
            self.event_id, self.user_id, page_info=pagination
        )

        # Assert
        self.assertEqual(len(conversations), 1)
        self.assertEqual(conversations[0].title, titles[-1])

    def test_update_conversation_title(self):
        """Test updating a conversation's title"""
        # Arrange
        original_title = "Original Title"
        new_title = "Updated Title"
        conversation_id = uuid.uuid4()
        self.repository.create_conversation(
            conversation_id,
            self.event_id,
            self.user_id,
            original_title,
        )

        # Act
        updated_conversation = self.repository.update_conversation_title(conversation_id,
                                                                         new_title)

        # Assert
        self.assertEqual(updated_conversation.title, new_title)

        # Verify it was actually updated in the database
        retrieved_conversation = self.repository.get_conversation(conversation_id)
        self.assertEqual(retrieved_conversation.title, new_title)

    def test_add_message(self):
        """Test adding a message to a conversation"""
        # Arrange
        conversation_id = uuid.uuid4()
        self.repository.create_conversation(
            conversation_id,
            self.event_id,
            self.user_id,
            "Test Conversation",
        )
        role = "user"
        content = "Hello, world!"
        metadata = {"key": "value"}

        # Act
        message = self.repository.add_message(conversation_id, role, content, metadata)

        # Assert
        self.assertIsInstance(message, Message)
        self.assertEqual(message.conversation_id, conversation_id)
        self.assertEqual(message.role, role)
        self.assertEqual(message.content, content)
        self.assertEqual(message.metadata, metadata)

        # Verify it was actually saved to the database
        messages = self.repository.get_messages(conversation_id)
        self.assertEqual(len(messages), 1)
        self.assertEqual(messages[0].content, content)

    def test_get_messages(self):
        """Test retrieving all messages for a conversation"""
        # Arrange
        conversation_id = uuid.uuid4()
        self.repository.create_conversation(
            conversation_id,
            self.event_id,
            self.user_id,
            "Test Conversation",
        )

        # Add multiple messages
        messages_data = [
            ("user", "Hello", {"type": "greeting"}),
            ("assistant", "Hi there!", {"type": "response"}),
            ("user", "How are you?", {"type": "question"})
        ]

        for role, content, metadata in messages_data:
            self.repository.add_message(conversation_id, role, content, metadata)

        # Act
        messages = self.repository.get_messages(conversation_id)

        # Assert
        self.assertEqual(len(messages), len(messages_data))
        for i, (role, content, metadata) in enumerate(messages_data):
            self.assertEqual(messages[i].role, role)
            self.assertEqual(messages[i].content, content)
            self.assertEqual(messages[i].metadata, metadata)

    def test_delete_conversation(self):
        """Test deleting a conversation"""
        # Arrange
        conversation_id = uuid.uuid4()
        self.repository.create_conversation(
            conversation_id,
            self.event_id,
            self.user_id,
            "Test Conversation",
        )

        # Add a message to the conversation
        self.repository.add_message(conversation_id, "user", "Test message", {})

        # Act
        result = self.repository.delete_conversation(conversation_id)

        # Assert
        self.assertTrue(result)

        # Verify it was actually deleted from the database
        deleted_conversation = self.repository.get_conversation(conversation_id)
        self.assertIsNone(deleted_conversation)

        # Verify messages were also deleted
        messages = self.repository.get_messages(conversation_id)
        self.assertEqual(len(messages), 0)

    def test_delete_nonexistent_conversation(self):
        """Test deleting a conversation that doesn't exist"""
        # Arrange
        nonexistent_id = uuid.uuid4()

        # Act
        result = self.repository.delete_conversation(nonexistent_id)

        # Assert
        self.assertFalse(result)

    def test_get_messages_with_pagination(self):
        """Test retrieving messages for a conversation with pagination"""
        # Arrange
        conversation_id = uuid.uuid4()
        self.repository.create_conversation(
            conversation_id,
            self.event_id,
            self.user_id,
            "Test Conversation",
        )

        # Add multiple messages
        messages_data = [
            ("user", "Message 1", {}),
            ("assistant", "Response 1", {}),
            ("user", "Message 2", {}),
            ("assistant", "Response 2", {}),
            ("user", "Message 3", {})
        ]

        for role, content, metadata in messages_data:
            self.repository.add_message(conversation_id, role, content, metadata)

        # Create pagination info
        # Test with limit 2, page 1 (second page), sorted by created_at asc
        pagination = PaginationInfo(
            items_per_page=2,
            page_offset=1,  # Second page (0-indexed)
            sorting_fields=[SortingCriteria(field_name="created_at", desc=False)]
        )

        # Act
        messages = self.repository.get_messages(conversation_id, pagination)

        # Assert
        # Should return 2 messages from the second page
        self.assertEqual(len(messages), 2)
        self.assertEqual(messages[0].content, "Message 2")
        self.assertEqual(messages[1].content, "Response 2")

        # Test with different pagination
        # Test with limit 2, page 0 (first page), sorted by created_at desc (newest first)
        pagination_desc = PaginationInfo(
            items_per_page=2,
            page_offset=0,
            sorting_fields=[SortingCriteria(field_name="created_at", desc=True)]
        )

        # Act
        messages_desc = self.repository.get_messages(conversation_id, pagination_desc)

        # Assert
        # Should return 2 messages from the first page in descending order
        self.assertEqual(len(messages_desc), 2)
        self.assertEqual(messages_desc[0].content, "Message 3")
        self.assertEqual(messages_desc[1].content, "Response 2")

    def test_referenced_entities_persistence(self):
        conversation_id = uuid.uuid4()
        self.repository.create_conversation(
            conversation_id,
            self.event_id,
            self.user_id,
            "Test Conversation",
        )

        message = self.repository.add_message(conversation_id, "assistant", "Hi")
        refs = [
            {"entity_type": "sessions", "entity_ids": [str(uuid.uuid4())]},
            {"entity_type": "speakers", "entity_ids": [str(uuid.uuid4()), str(uuid.uuid4())]},
        ]
        self.repository.add_referenced_entities(message.id, refs)

        result = self.repository.get_referenced_entities([message.id])
        self.assertIn(message.id, result)
        self.assertEqual(len(result[message.id]), 2)

    def test_get_single_message(self):
        conv_id = uuid.uuid4()
        self.repository.create_conversation(conv_id, self.event_id, self.user_id, "Test")
        msg = self.repository.add_message(conv_id, "assistant", "hello")

        fetched = self.repository.get_message(conv_id, msg.id)

        self.assertIsNotNone(fetched)
        self.assertEqual(fetched.id, msg.id)
