"""
Test the CopilotUseCase
"""
import uuid
from unittest.mock import Mock, ANY

from test.utils.uapi.test_case import AppTestCase
from flux.subdomains.application.genai.entities.copilot_entities import (
    Message, Conversation, Response, SuggestedAction, ReferencedEntity
)
from flux.subdomains.application.genai.interfaces.copilot_repository import CopilotRepository
from flux.subdomains.application.genai.services.copilot_service import CopilotService
from flux.subdomains.application.genai.use_cases.copilot_usecases import (
    CopilotUseCase,
    ConversationNotFoundError,
    EmptyMessageError,
)
from flux.domains.common.pagination import PaginationInfo, SortingCriteria


class TestCopilotUseCase(AppTestCase):
    def setUp(self):
        """Set up the test dependencies"""
        super().setUp()
        self.copilot_service = Mock(spec=CopilotService)
        self.copilot_repository = Mock(spec=CopilotRepository)

        self.use_case = CopilotUseCase(
            context=self.context,  # Pass context to the constructor
            repository=self.copilot_repository,
            copilot_service=self.copilot_service,
        )

        # Common test data
        self.event_id = 123
        self.user_id = 456
        self.conversation_id = uuid.uuid4()

    def test_handle_chat_message_new_conversation(self):
        """Test handling a chat message for a new conversation"""
        # Arrange
        message_text = "What sessions are available tomorrow?"

        # Mock return values
        self.copilot_repository.get_conversation.return_value = None

        conversation = Conversation(
            id=self.conversation_id,
            event_id=self.event_id,
            user_id=self.user_id,
            title="New Conversation",
            messages=[]
        )
        self.copilot_repository.create_conversation.return_value = conversation

        msg_1_id = uuid.uuid4()
        # Mock message creation
        user_message = Message(
            id=msg_1_id,
            conversation_id=self.conversation_id,
            role="user",
            content=message_text,
        )
        self.copilot_repository.add_message.side_effect = [user_message, None]

        # Mock response generation
        response = Response(
            content="Here are the sessions for tomorrow...",
            suggested_actions=[
                SuggestedAction(
                    action_type="SHOW_SESSIONS",
                    label="View sessions",
                    payload={"date": "tomorrow"}
                )
            ],
            referenced_entities=[
                ReferencedEntity(
                    entity_type="SESSION",
                    entity_id="session-1",
                    display_name="Machine Learning Workshop"
                )
            ]
        )
        self.copilot_service.process_query.return_value = response

        # Mock title generation
        self.copilot_service.generate_text.return_value = "Session Inquiry"

        # Act
        result = self.use_case.handle_chat_message(
            content=message_text,
            event_id=self.event_id,
            user_id=self.user_id
        )

        # Assert
        self.copilot_repository.create_conversation.assert_called_once()
        self.copilot_repository.get_messages.assert_not_called()  # No messages to get
        self.copilot_service.process_query.assert_called_once()

        # Verify empty conversation history was passed
        process_query_args = self.copilot_service.process_query.call_args[0]
        self.assertEqual(len(process_query_args), 5)  # Verify all 4 args are passed
        self.assertEqual(process_query_args[4], [])  # Verify empty conversation history

        self.copilot_service.generate_text.assert_called_once()
        self.copilot_repository.update_conversation_title.assert_called_once()
        self.assertEqual(result["content"], "Here are the sessions for tomorrow...")
        self.assertEqual(result["conversation_id"], self.conversation_id)

    def test_handle_chat_message_existing_conversation(self):
        """Test handling a chat message for an existing conversation"""
        # Arrange
        message_text = "Tell me more about the Machine Learning Workshop"

        msg_1_id = uuid.uuid4()
        msg_2_id = uuid.uuid4()

        # Create existing conversation with message history
        existing_messages = [
            Message(id=msg_1_id, conversation_id=self.conversation_id, role="user",
                    content="What sessions are available tomorrow?"),
            Message(id=msg_2_id, conversation_id=self.conversation_id, role="assistant",
                    content="Here are the sessions for tomorrow...")
        ]

        conversation = Conversation(
            id=self.conversation_id,
            event_id=self.event_id,
            user_id=self.user_id,
            title="Session Inquiry",
            messages=[]  # Empty because we'll use get_messages instead
        )
        self.copilot_repository.get_conversation.return_value = conversation

        # Mock repository get_messages to return conversation history
        self.copilot_repository.get_messages.return_value = existing_messages

        # Mock message creation
        user_message = Message(id=uuid.uuid4(), conversation_id=self.conversation_id, role="user",
                               content=message_text)
        ai_message = Message(id=uuid.uuid4(), conversation_id=self.conversation_id,
                             role="assistant",
                             content="The Machine Learning Workshop is a hands-on session...")
        self.copilot_repository.add_message.side_effect = [user_message, ai_message]

        # Mock response generation
        response = Response(
            content="The Machine Learning Workshop is a hands-on session...",
            referenced_entities=[
                ReferencedEntity(
                    entity_type="SESSION",
                    entity_id="session-1",
                    display_name="Machine Learning Workshop"
                )
            ]
        )
        self.copilot_service.process_query.return_value = response

        # Act
        result = self.use_case.handle_chat_message(
            content=message_text,
            event_id=self.event_id,
            user_id=self.user_id,
            conversation_id=self.conversation_id
        )

        # Assert
        self.copilot_repository.get_conversation.assert_called_once()
        self.copilot_repository.get_messages.assert_called_once()
        self.copilot_service.process_query.assert_called_once()

        # Verify conversation history was passed correctly
        process_query_args = self.copilot_service.process_query.call_args[0]
        self.assertEqual(len(process_query_args), 5)  # Verify all 5 args are passed
        self.assertEqual(process_query_args[4], existing_messages)  # Verify conversation history

        self.assertEqual(
            result["content"],
            "The Machine Learning Workshop is a hands-on session..."
        )
        self.assertEqual(result["conversation_id"], self.conversation_id)

    def test_handle_chat_message_empty_message(self):
        """Test handling an empty chat message"""
        # Act & Assert
        with self.assertRaises(EmptyMessageError):
            self.use_case.handle_chat_message(
                content="",
                event_id=self.event_id,
                user_id=self.user_id
            )

        with self.assertRaises(EmptyMessageError):
            self.use_case.handle_chat_message(
                content="   ",
                event_id=self.event_id,
                user_id=self.user_id
            )

    def test_handle_chat_message_conversation_not_found(self):
        """Test handling a chat message with non-existent conversation ID"""
        # Arrange
        self.copilot_repository.get_conversation.return_value = None

        # Act & Assert
        with self.assertRaises(ConversationNotFoundError):
            self.use_case.handle_chat_message(
                content="Hello",
                event_id=self.event_id,
                user_id=self.user_id,
                conversation_id=uuid.uuid4(),
            )

    def test_get_conversation(self):
        """Test retrieving a conversation"""
        # Arrange
        messages = [
            Message(id=uuid.uuid4(), conversation_id=self.conversation_id, role="user",
                    content="What sessions are available tomorrow?"),
            Message(id=uuid.uuid4(), conversation_id=self.conversation_id, role="assistant",
                    content="Here are the sessions for tomorrow...")
        ]

        conversation = Conversation(
            id=self.conversation_id,
            event_id=self.event_id,
            user_id=self.user_id,
            title="Session Inquiry"
        )
        self.copilot_repository.get_conversation.return_value = conversation
        self.copilot_repository.get_messages.return_value = messages

        # Act
        result = self.use_case.get_conversation(
            conversation_id=self.conversation_id,
            event_id=self.event_id,
            user_id=self.user_id
        )

        # Assert
        self.copilot_repository.get_conversation.assert_called_once_with(self.conversation_id,
                                                                         self.event_id,
                                                                         self.user_id)
        self.copilot_repository.get_messages.assert_called_once_with(self.conversation_id)
        self.assertEqual(result.id, self.conversation_id)
        self.assertEqual(len(result.messages), 2)

    def test_get_conversations(self):
        """Test retrieving all conversations for a user"""
        # Arrange
        conversation_1_id = uuid.uuid4()
        conversation_2_id = uuid.uuid4()
        conversations = [
            Conversation(
                id=conversation_1_id,
                event_id=self.event_id,
                user_id=self.user_id,
                title="Session Inquiry"
            ),
            Conversation(
                id=conversation_2_id,
                event_id=self.event_id,
                user_id=self.user_id,
                title="Speaker Information"
            )
        ]
        self.copilot_repository.get_conversations.return_value = conversations

        # Act
        result = self.use_case.get_conversations(
            event_id=self.event_id,
            user_id=self.user_id
        )

        # Assert
        self.copilot_repository.get_conversations.assert_called_once()
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0].id, conversation_1_id)
        self.assertEqual(result[1].id, conversation_2_id)

    def test_get_messages(self):
        """Test retrieving messages for a conversation"""
        # Arrange
        messages = [
            Message(id=uuid.uuid4(), conversation_id=self.conversation_id, role="user",
                    content="What sessions are available tomorrow?"),
            Message(id=uuid.uuid4(), conversation_id=self.conversation_id, role="assistant",
                    content="Here are the sessions for tomorrow...")
        ]
        self.copilot_repository.get_messages.return_value = messages

        # Act
        result = self.use_case.get_messages(self.conversation_id)

        # Assert
        self.copilot_repository.get_messages.assert_called_once_with(self.conversation_id, None)
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0].content, "What sessions are available tomorrow?")
        self.assertEqual(result[1].content, "Here are the sessions for tomorrow...")

    def test_get_messages_with_pagination(self):
        """Test retrieving messages for a conversation with pagination"""
        # Arrange
        messages = [
            Message(id=uuid.uuid4(), conversation_id=self.conversation_id, role="user",
                    content="What sessions are available tomorrow?"),
            Message(id=uuid.uuid4(), conversation_id=self.conversation_id, role="assistant",
                    content="Here are the sessions for tomorrow...")
        ]

        # Create a pagination object with sorting
        pagination = PaginationInfo(
            items_per_page=1,
            page_offset=0,
            sorting_fields=[SortingCriteria(field_name="created_at", desc=True)]
        )

        self.copilot_repository.get_messages.return_value = [
            messages[0]
        ]  # Return only first message due to pagination

        # Act
        result = self.use_case.get_messages(self.conversation_id, pagination)

        # Assert
        self.copilot_repository.get_messages.assert_called_once_with(
            self.conversation_id, pagination
        )
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].content, "What sessions are available tomorrow?")

    def test_get_single_message(self):
        msg_id = uuid.uuid4()
        msg = Message(id=msg_id, conversation_id=self.conversation_id, role="assistant",
                      content="hello")
        self.copilot_repository.get_message.return_value = msg

        result = self.use_case.get_message(self.conversation_id, msg_id)

        self.copilot_repository.get_message.assert_called_once_with(self.conversation_id, msg_id)
        self.assertEqual(result.id, msg_id)

    def test_generate_title(self):
        """Test title generation for a new conversation"""
        # Arrange
        first_message = "What sessions are available tomorrow?"
        expected_title = "Sessions Available Tomorrow"
        self.copilot_service.generate_text.return_value = expected_title

        # Act
        title = self.use_case._generate_title(first_message)  # skipcq: PYL-W0212

        # Assert
        self.copilot_service.generate_text.assert_called_once()
        self.assertEqual(title, expected_title)

    def test_generate_title_truncation(self):
        """Test title generation with truncation for long titles"""
        # Arrange
        first_message = "What sessions are available tomorrow?"
        long_title = (
            "This is a very long title that should be truncated because it exceeds the maximum "
            "length of 50 characters allowed for conversation titles in the system"
        )
        self.copilot_service.generate_text.return_value = long_title

        # Act
        title = self.use_case._generate_title(first_message)  # skipcq: PYL-W0212

        # Assert
        self.assertEqual(len(title), 100)
        self.assertEqual(title, long_title[:100])

    def test_conversation_not_found(self):
        """Test handling case when conversation is not found"""
        # Arrange
        self.copilot_repository.get_conversation.return_value = None

        non_existent_conversation_id = uuid.uuid4()

        # Act
        result = self.use_case.get_conversation(
            conversation_id=non_existent_conversation_id,
            event_id=self.event_id,
            user_id=self.user_id
        )

        # Assert
        self.assertIsNone(result)
        self.copilot_repository.get_conversation.assert_called_once_with(
            non_existent_conversation_id, self.event_id, self.user_id
        )

    def test_create_empty_conversation(self):
        """Test creating a conversation with no content"""
        # Arrange
        conversation_id = uuid.uuid4()
        conversation = Conversation(
            id=conversation_id,
            event_id=self.event_id,
            user_id=self.user_id,
            title="New Conversation"
        )
        self.copilot_repository.create_conversation.return_value = conversation

        # Act
        result = self.use_case.handle_chat_message(
            event_id=self.event_id,
            user_id=self.user_id
        )

        # Assert
        self.assertEqual(result["conversation_id"], conversation_id)
        self.copilot_repository.create_conversation.assert_called_once_with(
            ANY, self.event_id, self.user_id,
        )
        # Verify the LLM was not invoked
        self.copilot_service.process_query.assert_not_called()
        self.copilot_service.generate_text.assert_not_called()

    def test_handle_chat_message_stream(self):
        """Test streaming a chat message"""
        message_text = "Hi"
        self.copilot_repository.get_conversation.return_value = None
        conversation = Conversation(
            id=self.conversation_id,
            event_id=self.event_id,
            user_id=self.user_id,
        )
        self.copilot_repository.create_conversation.return_value = conversation
        self.copilot_repository.add_message.side_effect = [
            Message(id=uuid.uuid4()),
            Message(id=uuid.uuid4()),
        ]
        self.copilot_service.process_query_stream.return_value = iter(["a", "b"])
        self.copilot_service.generate_text.return_value = "Generated Title"

        _, _, stream = self.use_case.handle_chat_message_stream(
            event_id=self.event_id,
            user_id=self.user_id,
            content=message_text,
        )

        self.assertEqual(list(stream), ["a", "b"])
        self.copilot_service.process_query_stream.assert_called_once()
