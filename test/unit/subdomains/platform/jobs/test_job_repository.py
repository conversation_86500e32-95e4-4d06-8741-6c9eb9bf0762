"""
Unit tests for Jobs repository implementation
"""

import pytest
from uuid import uuid4

from flux.subdomains.platform.jobs.entities.job_entities import Job, JobTask, JobStatus
from flux.subdomains.platform.jobs.adapters.repositories.db_repository import JobRepositoryImpl
from flux.subdomains.platform.jobs.adapters.repositories.models import JobModel, JobTaskModel


class TestJobRepository:
    """Test cases for JobRepositoryImpl"""

    def setup_method(self):
        """Set up test fixtures"""
        self.job_repo = JobRepositoryImpl(Job)

    def test_job_model_from_entity(self):
        """Test creating JobModel from Job entity"""
        # Create a job entity with tasks
        job_id = uuid4()
        task_id = uuid4()
        
        job_entity = Job.create(
            id=job_id,
            name="Test Job",
            status=JobStatus.pending,
            context_type="event",
            context_id=str(uuid4()),
            created_by=str(uuid4()),
            message={"description": "Test job description"},
            tasks=[
                JobTask.create(
                    id=task_id,
                    job_id=job_id,
                    name="Test Task",
                    status=JobStatus.pending,
                    task_meta={"chunk_size": 100},
                    chunk_count=5,
                    is_callback=False
                )
            ]
        )
        
        # Convert to model
        job_model = JobModel.from_entity(job_entity)
        
        # Verify job model fields
        assert job_model.id == job_id
        assert job_model.name == "Test Job"
        assert job_model.status == JobStatus.pending
        assert job_model.context_type == "event"
        assert job_model.message == {"description": "Test job description"}
        
        # Verify task models
        assert len(job_model.tasks) == 1
        task_model = job_model.tasks[0]
        assert task_model.id == task_id
        assert task_model.job_id == job_id
        assert task_model.name == "Test Task"
        assert task_model.task_meta == {"chunk_size": 100}
        assert task_model.chunk_count == 5
        assert task_model.is_callback is False

    def test_job_model_as_entity_without_include(self):
        """Test converting JobModel back to Job entity without include set"""
        # Create job model with task
        job_id = uuid4()
        task_id = uuid4()

        job_model = JobModel(
            id=job_id,
            name="Test Job",
            status=JobStatus.running,
            context_type="event",
            context_id=str(uuid4()),
            created_by=str(uuid4()),
            message={"status": "processing"}
        )

        task_model = JobTaskModel(
            id=task_id,
            job_id=job_id,
            name="Test Task",
            status=JobStatus.running,
            task_meta={"processed": 50},
            chunk_count=10,
            is_callback=True
        )

        job_model.tasks = [task_model]

        # Convert to entity without include set (empty set)
        job_entity = job_model.as_entity(Job, include=set())

        # Verify job entity fields
        assert job_entity.id == job_id
        assert job_entity.name == "Test Job"
        assert job_entity.status == JobStatus.running
        assert job_entity.message == {"status": "processing"}

        # Verify tasks are None when not included
        assert job_entity.tasks is None

    def test_job_model_as_entity_with_tasks_include(self):
        """Test converting JobModel back to Job entity with tasks in include set"""
        # Create job model with task
        job_id = uuid4()
        task_id = uuid4()

        job_model = JobModel(
            id=job_id,
            name="Test Job",
            status=JobStatus.running,
            context_type="event",
            context_id=str(uuid4()),
            created_by=str(uuid4()),
            message={"status": "processing"}
        )

        task_model = JobTaskModel(
            id=task_id,
            job_id=job_id,
            name="Test Task",
            status=JobStatus.running,
            task_meta={"processed": 50},
            chunk_count=10,
            is_callback=True
        )

        job_model.tasks = [task_model]

        # Convert to entity with 'tasks' in include set
        job_entity = job_model.as_entity(Job, include={'tasks'})

        # Verify job entity fields
        assert job_entity.id == job_id
        assert job_entity.name == "Test Job"
        assert job_entity.status == JobStatus.running
        assert job_entity.message == {"status": "processing"}

        # Verify task entities are included
        assert job_entity.tasks is not None
        assert len(job_entity.tasks) == 1
        task_entity = job_entity.tasks[0]
        assert task_entity.id == task_id
        assert task_entity.job_id == job_id
        assert task_entity.name == "Test Task"
        assert task_entity.task_meta == {"processed": 50}
        assert task_entity.chunk_count == 10
        assert task_entity.is_callback is True

    def test_job_model_as_entity_with_all_include(self):
        """Test converting JobModel back to Job entity with 'all' in include set"""
        # Create job model with task
        job_id = uuid4()
        task_id = uuid4()

        job_model = JobModel(
            id=job_id,
            name="Test Job",
            status=JobStatus.running,
            context_type="event",
            context_id=str(uuid4()),
            created_by=str(uuid4()),
            message={"status": "processing"}
        )

        task_model = JobTaskModel(
            id=task_id,
            job_id=job_id,
            name="Test Task",
            status=JobStatus.running,
            task_meta={"processed": 50},
            chunk_count=10,
            is_callback=True
        )

        job_model.tasks = [task_model]

        # Convert to entity with 'all' in include set
        job_entity = job_model.as_entity(Job, include={'all'})

        # Verify task entities are included when 'all' is specified
        assert job_entity.tasks is not None
        assert len(job_entity.tasks) == 1

    def test_repository_interface_compliance(self):
        """Test that repository implements the interface correctly"""
        from flux.subdomains.platform.jobs.interfaces.job_repository import JobRepository

        # Verify that JobRepositoryImpl is a subclass of JobRepository
        assert issubclass(JobRepositoryImpl, JobRepository)

        # Verify that all abstract methods are implemented
        repo = JobRepositoryImpl(Job)
        assert hasattr(repo, 'create_job')

    def test_entity_creation_with_tasks(self):
        """Test creating Job entity with tasks"""
        job_id = uuid4()
        
        job_data = {
            'id': job_id,
            'name': 'Bulk Import Job',
            'status': JobStatus.pending,
            'context_type': 'event',
            'context_id': str(uuid4()),
            'created_by': str(uuid4()),
            'message': {'total_records': 1000},
            'tasks': [
                {
                    'id': uuid4(),
                    'job_id': job_id,
                    'name': 'Process Chunk 1',
                    'status': JobStatus.pending,
                    'task_meta': {'start_index': 0, 'end_index': 100},
                    'chunk_count': 1,
                    'is_callback': False
                },
                {
                    'id': uuid4(),
                    'job_id': job_id,
                    'name': 'Process Chunk 2',
                    'status': JobStatus.pending,
                    'task_meta': {'start_index': 100, 'end_index': 200},
                    'chunk_count': 1,
                    'is_callback': False
                }
            ]
        }
        
        job = Job.create(**job_data)
        
        assert job.id == job_id
        assert job.name == 'Bulk Import Job'
        assert len(job.tasks) == 2
        assert job.tasks[0].name == 'Process Chunk 1'
        assert job.tasks[1].name == 'Process Chunk 2'

    def test_create_job_use_case_with_include_set(self):
        """Test that CreateJobUseCase handles include set correctly"""
        from flux.subdomains.platform.jobs.usecases.job_usecases import CreateJobUseCase

        # Mock repository
        class MockJobRepo:
            def __init__(self):
                self.create_job_called_with_include = None

            def create_job(self, job, include=None):
                self.create_job_called_with_include = include
                # Return the job with tasks based on include
                if include and ('tasks' in include or 'all' in include):
                    job.tasks = []  # Mock empty tasks list
                else:
                    job.tasks = None
                return job

        mock_repo = MockJobRepo()
        use_case = CreateJobUseCase(mock_repo, Job)

        job_data = {
            'name': 'Test Job',
            'status': JobStatus.pending,
            'context_type': 'event',
            'context_id': str(uuid4()),
            'created_by': str(uuid4()),
        }

        # Test with default include (should be {'all'})
        job = use_case.run(job_data)
        assert mock_repo.create_job_called_with_include == {'all'}
        assert job.tasks is not None  # Should include tasks with 'all'

        # Test with explicit include set
        job = use_case.run(job_data, include={'tasks'})
        assert mock_repo.create_job_called_with_include == {'tasks'}
        assert job.tasks is not None  # Should include tasks

        # Test with empty include set
        job = use_case.run(job_data, include=set())
        assert mock_repo.create_job_called_with_include == set()
        assert job.tasks is None  # Should not include tasks
