from typing import List, Dict, Any, Iterable
from unittest.mock import patch
from uuid import UUID

from test.utils.uapi.test_case import ApiTestCase
from test.utils.data_fixtures import factories
from flux.subdomains.application.genai.interfaces.llm_adapter import LLMAdapter, LLMResponse

CONVERSATIONS_URL = '/uapi/attendee/events/{event_id}/ai/conversations'


class TestLLMAdapter(LLMAdapter):
    """
    Test implementation of LLMAdapter for testing purposes.

    This class provides a controlled, predictable response for the AI service
    without requiring actual connection to external LLM services like AWS Bedrock.
    """

    def __init__(self):
        self.response_text = "This is an AI response."

    def generate_text(self, prompt: str, max_tokens: int = 1000, temperature: float = 0.7
                      ) -> LLMResponse:
        """Return a predefined response for testing"""
        metadata = {
            "model": "test-model",
            "elapsed_time": 0.1,
            "input_tokens": 10,
            "output_tokens": 20,
            "cost": 0.0,
        }
        return LLMResponse(text=self.response_text, metadata=metadata)

    def chat(self, conversation_id, messages, max_tokens: int = 1000, temperature: float = 0.7,
             message_id: UUID = None) -> LLMResponse:
        """Return a predefined response for testing"""
        return self.generate_text("", max_tokens, temperature)

    def chat_stream(
        self,
        conversation_id: UUID,
        messages: List[Dict[str, Any]],
        max_tokens: int = 1000,
        temperature: float = 0.7,
        message_id: UUID = None,
    ) -> Iterable[str]:
        """Return a stream of predefined responses for testing"""
        yield self.response_text


class TestAttendeeAIEndpointsIntegration(ApiTestCase):
    """
    Integration test for the Attendee AI Endpoints.

    This test uses a mock implementation of the LLMAdapter to avoid making
    actual calls to external AI services during testing, while still testing
    the integration between the endpoint, controller, and use case layers.
    """

    def setUp(self):
        super().setUp()

        # Create test event and attendee
        self.event = self._setup_event_fixture()
        self.attendee = factories.create_event_person(event_id=self.event.id)
        self.attendee, self.attendee_session = self.setup_attendee_session(self.event.id)
        self.client.set_authn_from_session(self.attendee_session)

        # Enable AI copilot feature for this event
        factories.create_feature_flag_key('ai_copilot_enabled', enabled=True)
        factories.create_feature_flag_override(
            'ai_copilot_enabled', resource_id=self.event.id, enabled=True
        )

        # Create test LLM adapter instance
        self.test_llm_adapter = TestLLMAdapter()

    @patch('flux.subdomains.application.genai.factory.create_llm_adapter')
    def test_create_conversation_endpoint_integration(self, mock_create_llm_adapter):
        """Test the create conversation endpoint with a mock LLM adapter"""
        # Inject our test adapter to avoid actual AWS Bedrock calls
        mock_create_llm_adapter.return_value = self.test_llm_adapter

        # Test data
        data = {"content": "What sessions are available tomorrow?"}

        # Make the request
        response = self.client.post(
            CONVERSATIONS_URL.format(event_id=self.event.id), data=data, version='p.9'
        )

        # Verify the response status (created)
        self.assert_response_status(response, 201)

        # Get the JSON and validate response structure
        resp_json = response.get_json()
        self.assertIn("data", resp_json)
        data = resp_json["data"]

        # Verify conversation data
        self.assertIn("conversation_id", data)
        self.assertIn("content", data)
        self.assertEqual("This is an AI response.", data["content"])
        self.assertIn("suggested_actions", data)
        self.assertIn("referenced_entities", data)
