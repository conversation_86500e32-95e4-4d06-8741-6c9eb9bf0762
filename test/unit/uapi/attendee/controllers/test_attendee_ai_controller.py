"""
Tests for the Attendee AI Controller
"""
import uuid
from unittest.mock import MagicMock, patch
from datetime import datetime

from flux.domains.common.errors import AuthorizationError
from flux.uapi.attendee.controllers.attendee_ai_controller import (
    AttendeeAIController,
)
from flux.subdomains.application.genai.use_cases.copilot_usecases import (
    CopilotUseCase
)
from flux.subdomains.application.genai.entities.copilot_entities import Conversation, Message

from test.utils.data_fixtures import factories
from test.utils.uapi.test_case import AppTestCase
from flux.domains.common.pagination import PaginationInfo, SortingCriteria


@AppTestCase.add_patch('boto3')
class TestAttendeeAIController(AppTestCase):
    """Test cases for the Attendee AI Controller"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        self.event_id = 1234
        self.user_id = 4567
        self.conversation_id = uuid.uuid4()

        # Mock context with user that has event access
        self.context = MagicMock()
        self.context.user.id = self.user_id
        self.context.user.assert_has_event_access = MagicMock()

        # Mock CopilotUseCase
        self.mock_usecase = MagicMock(spec=CopilotUseCase)
        self.usecase_patcher = patch.object(
            AttendeeAIController,
            '_get_copilot_use_case',
            return_value=self.mock_usecase
        )
        self.usecase_patcher.start()
        self.addCleanup(self.usecase_patcher.stop)

        # Create controller instance
        self.controller = AttendeeAIController(context=self.context)

    def _setup_feature_flag(self, enabled=True):
        """Helper to set up the feature flag"""
        factories.create_feature_flag_key(
            key_id=AttendeeAIController.FEATURE_FLAG_KEY,
            enabled=enabled,
            resource_type='events'
        )
        if enabled:
            factories.create_feature_flag_override(
                key_id=AttendeeAIController.FEATURE_FLAG_KEY,
                resource_id=self.event_id,
                enabled=True,
                resource_type='events'
            )

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_check_feature_enabled_when_enabled(self, mock_is_ff_enabled):
        """Test that check_feature_enabled returns True when the feature flag is enabled"""
        # Arrange
        mock_is_ff_enabled.return_value = True

        # Act
        self.controller.check_feature_enabled(self.event_id)

        # Assert - no exception raised means success
        mock_is_ff_enabled.assert_called_once()

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_check_feature_enabled_when_disabled(self, mock_is_ff_enabled):
        """Test that check_feature_enabled raises AuthorizationError when feature
        flag is disabled"""
        # Arrange
        mock_is_ff_enabled.return_value = False

        # Act & Assert
        with self.assertRaises(AuthorizationError):
            self.controller.check_feature_enabled(self.event_id)

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_get_conversations(self, mock_is_ff_enabled):
        """Test get_conversations method"""
        # Arrange
        mock_is_ff_enabled.return_value = True

        # Mock data
        mock_conv1 = self._create_mock_conversation("conv-1", "Conversation 1")
        mock_conv2 = self._create_mock_conversation("conv-2", "Conversation 2")
        self.mock_usecase.get_conversations.return_value = [mock_conv1, mock_conv2]

        # Call the method
        result = self.controller.get_conversations(self.event_id)

        # Verify results
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["id"], "conv-1")
        self.assertEqual(result[1]["id"], "conv-2")

        # Verify use case was called correctly
        self.mock_usecase.get_conversations.assert_called_once_with(
            self.event_id, self.user_id, page_info=None,
        )

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_get_conversation_found(self, mock_is_ff_enabled):
        """Test get_conversation method when conversation is found"""
        # Arrange
        mock_is_ff_enabled.return_value = True

        # Mock data
        mock_conv = self._create_mock_conversation(self.conversation_id, "Test Conversation")
        self.mock_usecase.get_conversation.return_value = mock_conv

        # Call the method
        result = self.controller.get_conversation(self.event_id, self.conversation_id)

        # Verify results
        self.assertIsNotNone(result)
        self.assertEqual(result["id"], str(self.conversation_id))
        self.assertEqual(result["title"], "Test Conversation")

        # Verify use case was called correctly
        self.mock_usecase.get_conversation.assert_called_once_with(
            self.conversation_id, self.event_id, self.user_id
        )

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_get_conversation_not_found(self, mock_is_ff_enabled):
        """Test get_conversation method when conversation is not found"""
        # Arrange
        mock_is_ff_enabled.return_value = True

        # Mock data - conversation not found
        self.mock_usecase.get_conversation.return_value = None

        # Call the method
        result = self.controller.get_conversation(self.event_id, self.conversation_id)

        # Verify results
        self.assertIsNone(result)

        # Verify use case was called correctly
        self.mock_usecase.get_conversation.assert_called_once_with(
            self.conversation_id, self.event_id, self.user_id
        )

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_send_message_new_conversation(self, mock_is_ff_enabled):
        """Test send_message method for a new conversation"""
        # Arrange
        mock_is_ff_enabled.return_value = True

        # Mock data
        content = "Hello AI!"
        mock_response = {
            "conversation_id": "new-conv-id",
            "content": "Hello human!",
            "suggested_actions": [],
            "referenced_entities": []
        }
        self.mock_usecase.handle_chat_message.return_value = mock_response

        # Call the method (no conversation_id for new conversation)
        result = self.controller.send_message(self.event_id, content)

        # Verify results
        self.assertEqual(result["conversation_id"], "new-conv-id")
        self.assertEqual(result["content"], "Hello human!")

        # Verify use case was called correctly
        self.mock_usecase.handle_chat_message.assert_called_once_with(
            event_id=self.event_id,
            user_id=self.user_id,
            content=content,
            conversation_id=None
        )

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_send_message_existing_conversation(self, mock_is_ff_enabled):
        """Test send_message method for an existing conversation"""
        # Arrange
        mock_is_ff_enabled.return_value = True

        # Mock data
        mock_conv = self._create_mock_conversation(self.conversation_id, "Test Conversation")
        self.mock_usecase.get_conversation.return_value = mock_conv

        content = "Tell me more!"
        mock_response = {
            "conversation_id": str(self.conversation_id),
            "content": "Here's more information.",
            "suggested_actions": [
                MagicMock(action_type="VIEW", label="View Schedule", payload={"id": "123"})
            ],
            "referenced_entities": [
                MagicMock(entity_type="sessions", entity_id="456", display_name="Keynote")
            ]
        }
        self.mock_usecase.handle_chat_message.return_value = mock_response

        # Call the method with conversation_id
        result = self.controller.send_message(self.event_id, content, self.conversation_id)

        # Verify results
        self.assertEqual(result["conversation_id"], str(self.conversation_id))
        self.assertEqual(result["content"], "Here's more information.")
        self.assertEqual(len(result["suggested_actions"]), 1)
        self.assertEqual(len(result["referenced_entities"]), 1)

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_send_message_stream(self, mock_is_ff_enabled):
        """Test streaming a message"""
        mock_is_ff_enabled.return_value = True
        content = "Hello"
        self.mock_usecase.handle_chat_message_stream.return_value = (
            self.conversation_id,
            uuid.uuid4(),
            iter(["a", "b"]),
        )
        _, _, gen = self.controller.send_message(self.event_id, content, stream=True)
        self.assertEqual(list(gen), ["a", "b"])
        self.mock_usecase.handle_chat_message_stream.assert_called_once_with(
            event_id=self.event_id,
            user_id=self.user_id,
            content=content,
            conversation_id=None,
        )

    def test_check_event_access_success(self):
        """Test successful event access check"""
        # Should not raise exception
        self.controller.check_event_access(self.event_id)

        # Verify assert_has_event_access was called
        self.context.user.assert_has_event_access.assert_called_once_with(self.event_id)

    def test_check_event_access_no_user(self):
        """Test event access check with no user"""
        # Set context.user to None
        self.controller._context.user = None  # skipcq: PYL-W0212

        # Should raise AuthorizationError
        with self.assertRaises(AuthorizationError):
            self.controller.check_event_access(self.event_id)

    def _create_mock_conversation(self, conv_id, title, message_count=0):
        """Helper to create a mock conversation object"""
        conv = MagicMock(spec=Conversation)
        conv.id = conv_id
        conv.title = title
        conv.event_id = self.event_id
        conv.created_at = datetime.now()
        conv.updated_at = datetime.now()
        conv.message_count = message_count
        conv.messages = []
        conv.user_id = self.user_id

        return conv

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_get_messages(self, mock_is_ff_enabled):
        """Test retrieving messages with pagination"""
        # Arrange
        # Enable feature flag
        mock_is_ff_enabled.return_value = True

        msg_1_id = uuid.uuid4()
        msg_2_id = uuid.uuid4()

        # Create some test messages
        messages = [
            Message(
                id=msg_1_id,
                conversation_id=self.conversation_id,
                role="user",
                content="Hello AI!",
                created_at=datetime.now()
            ),
            Message(
                id=msg_2_id,
                conversation_id=self.conversation_id,
                role="assistant",
                content="Hi there! How can I help?",
                created_at=datetime.now(),
                metadata={
                    "response": {
                        "suggested_actions": [
                            {
                                "action_type": "VIEW_SESSIONS",
                                "label": "Browse Sessions",
                                "payload": {"date": "tomorrow"}
                            }
                        ],
                        "referenced_entities": [
                            {
                                "entity_type": "EVENT",
                                "entity_id": "event-123",
                                "display_name": "Conference"
                            }
                        ]
                    }
                }
            )
        ]

        # Create a test conversation
        conversation = Conversation(
            id=self.conversation_id,
            event_id=self.event_id,
            user_id=self.user_id,
        )

        # Set up mock responses
        self.mock_usecase.get_conversation.return_value = conversation
        self.mock_usecase.get_messages.return_value = messages

        # Create pagination info
        pagination = PaginationInfo(
            items_per_page=10,
            page_offset=0,
            sorting_fields=[SortingCriteria(field_name="created_at", desc=False)]
        )

        # Act
        result = self.controller.get_messages(self.event_id, self.conversation_id, pagination)

        # Assert
        # Verify access checks were performed
        self.context.user.assert_has_event_access.assert_called_once_with(self.event_id)

        # Verify get_conversation was called to verify access
        self.mock_usecase.get_conversation.assert_called_once_with(
            self.conversation_id, self.event_id, self.context.user.id
        )

        # Verify get_messages was called with correct parameters
        self.mock_usecase.get_messages.assert_called_once_with(
            conversation_id=self.conversation_id,
            page_info=pagination
        )

        # Verify result contains both messages
        self.assertEqual(len(result), 2)

        # Check message mapping
        self.assertEqual(result[0]["id"], str(msg_1_id))
        self.assertEqual(result[0]["role"], "user")
        self.assertEqual(result[0]["content"], "Hello AI!")
        self.assertNotIn("suggested_actions", result[0])

        # Check assistant message with metadata
        self.assertEqual(result[1]["id"], str(msg_2_id))
        self.assertEqual(result[1]["role"], "assistant")
        self.assertEqual(result[1]["content"], "Hi there! How can I help?")
        self.assertIn("referenced_entities", result[1])

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_get_messages_conversation_not_found(self, mock_is_ff_enabled):
        """Test retrieving messages when conversation doesn't exist or user doesn't have access"""
        # Enable feature flag
        mock_is_ff_enabled.return_value = True

        # Arrange
        self.mock_usecase.get_conversation.return_value = None

        # Act
        result = self.controller.get_messages(self.event_id, self.conversation_id)

        # Assert
        self.assertEqual(result, [])
        self.mock_usecase.get_messages.assert_not_called()

    def test_add_referenced_entities(self):
        refs = [{"entity_type": "sessions", "entity_ids": [str(uuid.uuid4())]}]
        self.mock_usecase.add_referenced_entities.return_value = refs

        self.controller.add_referenced_entities(
            self.event_id, uuid.uuid4(), refs
        )
        self.mock_usecase.add_referenced_entities.assert_called_once()

    @patch('flux.uapi.attendee.controllers.attendee_ai_controller.is_ff_enabled')
    def test_get_single_message(self, mock_is_ff_enabled):
        # Enable feature flag
        mock_is_ff_enabled.return_value = True

        msg_id = uuid.uuid4()
        msg = Message(
            id=msg_id,
            conversation_id=self.conversation_id,
            role="assistant",
            content="hi",
        )
        self.mock_usecase.get_conversation.return_value = Conversation(
            id=self.conversation_id,
            event_id=self.event_id,
            user_id=self.user_id,
        )
        self.mock_usecase.get_message.return_value = msg

        result = self.controller.get_message(self.event_id, self.conversation_id, msg_id)

        self.assertEqual(result["id"], str(msg_id))
        self.mock_usecase.get_message.assert_called_once_with(self.conversation_id, msg_id)
